module.exports = {
  local: {
    OKTA_ISSUER: process.env.OKTA_ISSUER,
    OKTA_CLIENT_ID: process.env.OKTA_CLIENT_ID,
    APP_CLIENT_ID: process.env.APP_CLIENT_ID,
    USER_POOL_ID: process.env.USER_POOL_ID,
    JWT_KEY: process.env.JWT_KEY,
    COGNITO_ENDPOINT: process.env.COGNITO_ENDPOINT,
    PAYMENT_SERVICE_ENDPOINT: process.env.PAYMENT_SERVICE_ENDPOINT,
  },
  development: {
    OKTA_ISSUER: process.env.OKTA_ISSUER,
    OKTA_CLIENT_ID: process.env.OKTA_CLIENT_ID,
    APP_CLIENT_ID: process.env.APP_CLIENT_ID,
    USER_POOL_ID: process.env.USER_POOL_ID,
    JWT_KEY: process.env.JWT_KEY,
    COGNITO_ENDPOINT: process.env.COGNITO_ENDPOINT,
    PAYMENT_SERVICE_ENDPOINT: process.env.PAYMENT_SERVICE_ENDPOINT,
  },
  test: {
    OKTA_ISSUER: process.env.OKTA_ISSUER,
    OKTA_CLIENT_ID: process.env.OKTA_CLIENT_ID,
    APP_CLIENT_ID: process.env.APP_CLIENT_ID,
    USER_POOL_ID: process.env.USER_POOL_ID,
    JWT_KEY: process.env.JWT_KEY,
    COGNITO_ENDPOINT: process.env.COGNITO_ENDPOINT,
    PAYMENT_SERVICE_ENDPOINT: process.env.PAYMENT_SERVICE_ENDPOINT,
  },
  preprod: {
    OKTA_ISSUER: process.env.OKTA_ISSUER,
    OKTA_CLIENT_ID: process.env.OKTA_CLIENT_ID,
    APP_CLIENT_ID: process.env.APP_CLIENT_ID,
    USER_POOL_ID: process.env.USER_POOL_ID,
    JWT_KEY: process.env.JWT_KEY,
    COGNITO_ENDPOINT: process.env.COGNITO_ENDPOINT,
    PAYMENT_SERVICE_ENDPOINT: process.env.PAYMENT_SERVICE_ENDPOINT,
  },
  production: {
    OKTA_ISSUER: process.env.OKTA_ISSUER,
    OKTA_CLIENT_ID: process.env.OKTA_CLIENT_ID,
    APP_CLIENT_ID: process.env.APP_CLIENT_ID,
    USER_POOL_ID: process.env.USER_POOL_ID,
    JWT_KEY: process.env.JWT_KEY,
    COGNITO_ENDPOINT: process.env.COGNITO_ENDPOINT,
    PAYMENT_SERVICE_ENDPOINT: process.env.PAYMENT_SERVICE_ENDPOINT,
  },
};
