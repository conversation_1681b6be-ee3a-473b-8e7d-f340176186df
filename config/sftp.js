module.exports = {
  local: {
    host: process.env.SFTP_HOST,
    port: process.env.SFTP_PORT,
    username: process.env.SFTP_USERNAME,
    private_key: process.env.SFTP_PRIVATE_KEY,
    upload_path: process.env.SFTP_UPLOAD_PATH,
    upload_wireless: process.env.SFTP_UPLOAD_WIRELESS,
  },
  development: {
    host: process.env.SFTP_HOST,
    port: process.env.SFTP_PORT,
    username: process.env.SFTP_USERNAME,
    private_key: process.env.SFTP_PRIVATE_KEY,
    upload_path: process.env.SFTP_UPLOAD_PATH,
    upload_wireless: process.env.SFTP_UPLOAD_WIRELESS,
  },
  test: {
    host: process.env.SFTP_HOST,
    port: process.env.SFTP_PORT,
    username: process.env.SFTP_USERNAME,
    private_key: process.env.SFTP_PRIVATE_KEY,
    upload_path: process.env.SFTP_UPLOAD_PATH,
    upload_wireless: process.env.SFTP_UPLOAD_WIRELESS,
  },
  preprod: {
    host: process.env.SFTP_HOST,
    port: process.env.SFTP_PORT,
    username: process.env.SFTP_USERNAME,
    private_key: process.env.SFTP_PRIVATE_KEY,
    upload_path: process.env.SFTP_UPLOAD_PATH,
    upload_wireless: process.env.SFTP_UPLOAD_WIRELESS,
  },
  production: {
    host: process.env.SFTP_HOST,
    port: process.env.SFTP_PORT,
    username: process.env.SFTP_USERNAME,
    private_key: process.env.SFTP_PRIVATE_KEY,
    upload_path: process.env.SFTP_UPLOAD_PATH,
    upload_wireless: process.env.SFTP_UPLOAD_WIRELESS,
  },
};
