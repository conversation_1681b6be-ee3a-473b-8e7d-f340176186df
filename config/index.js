require('dotenv').config();
const fs = require('fs');
const path = require('path');
const database = require('./database');
const oauth = require('./oauth');
const paymentGateway = require('./paymentGateway');
const athena = require('./athena');
const sftp = require('./sftp');
const recipient = require('./recipient');
const patternId = require('./notifPatternId');
const kafkaConfig = require('./kafka');

const ENV = process.env.NODE_ENV || 'development';
const PORT = process.env.PORT || 3000;
const appUrl = process.env.APP_URL || 'http://localhost:3000';
const webPortalUrl = process.env.WEB_PORTAL_URL || 'http://localhost:4000';

function loadDbConfig() {
  if (process.env.DATABASE_URL) {
    return process.env.DATABASE_URL;
  }

  if (fs.existsSync(path.join(__dirname, './database.js'))) {
    return database[ENV];
  }
  return undefined;
}

function loadLogConfig() {
  if (fs.existsSync(path.join(__dirname, './logging.js'))) {
    return require('./logging')[ENV];
  }
  return undefined;
}

function loadAuthConfig() {
  if (fs.existsSync(path.join(__dirname, './oauth.js'))) {
    return oauth[ENV];
  }
  return undefined;
}

function loadPaymentGatewayConfig() {
  if (fs.existsSync(path.join(__dirname, './paymentGateway.js'))) {
    return paymentGateway[ENV];
  }
  return undefined;
}

function loadAthenaConfig() {
  if (fs.existsSync(path.join(__dirname, './athena.js'))) {
    return athena[ENV];
  }
  return undefined;
}

function loadSftpConfig() {
  if (fs.existsSync(path.join(__dirname, './sftp.js'))) {
    return sftp[ENV];
  }
  return undefined;
}

function loadRecipientConfig() {
  if (fs.existsSync(path.join(__dirname, './recipient.js'))) {
    return recipient[ENV];
  }
  return undefined;
}

function loadNotificationPatternIdsConfig() {
  if (fs.existsSync(path.join(__dirname, './notifPatternId.js'))) {
    return patternId[ENV];
  }
  return undefined;
}

function loadKafkaConfig() {
  if (fs.existsSync(path.join(__dirname, './kafka.js'))) {
    return kafkaConfig[ENV];
  }
  return undefined;
}

function checkDebug() {
  if (process.env.NODE_ENV === 'production') {
    process.env.DEBUG = false;
  } else {
    process.env.DEBUG = true;
  }
}

const dbConfig = loadDbConfig();
const logConfig = loadLogConfig();
const authConfig = loadAuthConfig();
const athenaConfig = loadAthenaConfig();
const paymentGatewayConfig = loadPaymentGatewayConfig();
const sftpConfig = loadSftpConfig();
const recipientConfig = loadRecipientConfig();
const notifPatternId = loadNotificationPatternIdsConfig();
const kafka = loadKafkaConfig();

const config = Object.assign({
  [ENV]: true,
  env: ENV,
  web: { port: PORT },
  db: dbConfig,
  auth: authConfig,
  logging: logConfig,
  athena: athenaConfig,
  paymentGateway: paymentGatewayConfig,
  sftp: sftpConfig,
  recipient: recipientConfig,
  notifPatternId,
  appUrl,
  webPortalUrl,
  kafka,
});

module.exports = config;

checkDebug();
