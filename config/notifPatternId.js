const commonConfig = {
  emails: {
    User: {
      registration: '30161',
      activate: '30181',
      deactivate: '30201',
      update: '30221',
      delete: '30341',
      newRole: '30421',
    },
    Role: {
      update: '30421',
    },
    Channel: {
      create: '30241',
      update: '30261',
      delete: '30281',
      activate: '30321',
    },
    Reports: {
      creditCard: '25363',
      collection: '25364',
      globeOne: '30021',
      ecpay: '31521',
    },
  },
  sms: {
    User: {
      registration: '29961',
      activate: '30001',
      deactivate: '29901',
      update: '29921',
      delete: '29981',
      newRole: '30021',
    },
    Role: {
      update: '29941',
    },
    Channel: {
      create: '30041',
      update: '30061',
      delete: '30081',
    },
  },
};

module.exports = {
  development: {
    ...commonConfig,
  },
  local: {
    ...commonConfig,
  },
  test: {
    ...commonConfig,
  },
  preprod: {
    ...commonConfig,
  },
  production: {
    ...commonConfig,
  },
};
