module.exports = {
  local: {
    PAYMENT_GATEWAY_API_KEY: process.env.PAYMENT_GATEWAY_API_KEY,
    PAYMENT_GATEWAY_API_URL: process.env.PAYMENT_GATEWAY_API_URL,
    PAYMENT_GATEWAY_CHECKOUT_URL: process.env.PAYMENT_GATEWAY_CHECKOUT_URL,
    PAYMENT_GATEWAY_MERCHANT_ACCOUNT: process.env.PAYMENT_GATEWAY_MERCHANT_ACCOUNT,
    PAYMENT_GATEWAY_SDK_VERSION: process.env.PAYMENT_GATEWAY_SDK_VERSION,
    REFUND_REQUEST_EXPECTED_STATUSES: process.env.REFUND_REQUEST_EXPECTED_STATUSES,
    REFUND_APPROVAL_EXPECTED_PAYMENT_GATEWAY: process.env.REFUND_APPROVAL_EXPECTED_PAYMENT_GATEWAY,
  },
  development: {
    PAYMENT_GATEWAY_API_KEY: process.env.PAYMENT_GATEWAY_API_KEY,
    PAYMENT_GATEWAY_API_URL: process.env.PAYMENT_GATEWAY_API_URL,
    PAYMENT_GATEWAY_CHECKOUT_URL: process.env.PAYMENT_GATEWAY_CHECKOUT_URL,
    PAYMENT_GATEWAY_MERCHANT_ACCOUNT: process.env.PAYMENT_GATEWAY_MERCHANT_ACCOUNT,
    PAYMENT_GATEWAY_SDK_VERSION: process.env.PAYMENT_GATEWAY_SDK_VERSION,
    REFUND_REQUEST_EXPECTED_STATUSES: process.env.REFUND_REQUEST_EXPECTED_STATUSES,
    REFUND_APPROVAL_EXPECTED_PAYMENT_GATEWAY: process.env.REFUND_APPROVAL_EXPECTED_PAYMENT_GATEWAY,
  },
  test: {
    PAYMENT_GATEWAY_API_KEY: process.env.PAYMENT_GATEWAY_API_KEY,
    PAYMENT_GATEWAY_API_URL: process.env.PAYMENT_GATEWAY_API_URL,
    PAYMENT_GATEWAY_CHECKOUT_URL: process.env.PAYMENT_GATEWAY_CHECKOUT_URL,
    PAYMENT_GATEWAY_MERCHANT_ACCOUNT: process.env.PAYMENT_GATEWAY_MERCHANT_ACCOUNT,
    PAYMENT_GATEWAY_SDK_VERSION: process.env.PAYMENT_GATEWAY_SDK_VERSION,
    REFUND_REQUEST_EXPECTED_STATUSES: process.env.REFUND_REQUEST_EXPECTED_STATUSES,
  },
  preprod: {
    PAYMENT_GATEWAY_API_KEY: process.env.PAYMENT_GATEWAY_API_KEY,
    PAYMENT_GATEWAY_API_URL: process.env.PAYMENT_GATEWAY_API_URL,
    PAYMENT_GATEWAY_CHECKOUT_URL: process.env.PAYMENT_GATEWAY_CHECKOUT_URL,
    PAYMENT_GATEWAY_MERCHANT_ACCOUNT: process.env.PAYMENT_GATEWAY_MERCHANT_ACCOUNT,
    PAYMENT_GATEWAY_SDK_VERSION: process.env.PAYMENT_GATEWAY_SDK_VERSION,
    REFUND_REQUEST_EXPECTED_STATUSES: process.env.REFUND_REQUEST_EXPECTED_STATUSES,
    REFUND_APPROVAL_EXPECTED_PAYMENT_GATEWAY: process.env.REFUND_APPROVAL_EXPECTED_PAYMENT_GATEWAY,
  },
  production: {
    PAYMENT_GATEWAY_API_KEY: process.env.PAYMENT_GATEWAY_API_KEY,
    PAYMENT_GATEWAY_API_URL: process.env.PAYMENT_GATEWAY_API_URL,
    PAYMENT_GATEWAY_CHECKOUT_URL: process.env.PAYMENT_GATEWAY_CHECKOUT_URL,
    PAYMENT_GATEWAY_MERCHANT_ACCOUNT: process.env.PAYMENT_GATEWAY_MERCHANT_ACCOUNT,
    PAYMENT_GATEWAY_SDK_VERSION: process.env.PAYMENT_GATEWAY_SDK_VERSION,
    REFUND_REQUEST_EXPECTED_STATUSES: process.env.REFUND_REQUEST_EXPECTED_STATUSES,
    REFUND_APPROVAL_EXPECTED_PAYMENT_GATEWAY: process.env.REFUND_APPROVAL_EXPECTED_PAYMENT_GATEWAY,
  },
};
