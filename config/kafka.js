const envConfig = {
  sasl: {
    'sasl.username': process.env.KAFKA_SASL_USERNAME,
    'sasl.password': process.env.<PERSON><PERSON>KA_SASL_PASSWORD,
    'sasl.mechanisms': process.env.<PERSON>AFKA_MECHANISM,
    'security.protocol': process.env.<PERSON>AFKA_SECURITY_PROTOCOL,
  },
  ssl: {
    identificationAlgo: process.env.KAFKA_SSL_ENDPOINT_IDENTIFICATION_ALGORITHM,
  },
  brokers: process.env.KAFKA_BROKERS,
  schemaRegistry: {
    host: process.env.KAFKA_SCHEMA_REGISTRY_HOST,
    auth: {
      username: process.env.<PERSON>AFKA_SCHEMA_REGISTRY_USERNAME,
      password: process.env.KAFKA_SCHEMA_REGISTRY_PASSWORD,
    },
  },
  securityProtocol: process.env.KAFKA_SECURITY_PROTOCOL,
  toggleConnection: process.env.<PERSON><PERSON><PERSON>_TOGGLE_CONNECTION,
  topics: {
    refund: process.env.KAFKA_TOPIC_REFUND,
  },
  events: {
    cc_refund: process.env.KAFKA_MESSAGE_EVENT_CC_REFUND,
  },
  schemas: {
    refund: process.env.KAFKA_TOPIC_REFUND_SCHEMA_VERSION,
  },
  topicConfig: {
    acks: process.env.KAFKA_PRODUCER_ACKS,
  },
  globalConfig: {
    'bootstrap.servers': process.env.KAFKA_PRODUCE_BROKERS,
    'queue.buffering.max.kbytes': parseInt(process.env.KAFKA_PRODUCER_QUEUE_BUFFERING_MAX_KBYTES ?? '1'),
    'linger.ms': parseInt(process.env.KAFKA_PRODUCER_LINGER_MS ?? '10'),
    'max.in.flight.requests.per.connection': parseInt(
      process.env.KAFKA_PRODUCER_MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION ?? '1'
    ),
    retries: parseInt(process.env.KAFKA_PRODUCER_RETRIES ?? '3'),
    'batch.num.messages': parseInt(process.env.KAFKA_PRODUCER_BATCH_NUM_MESSAGES ?? '5'),
  },
};
module.exports = {
  local: {
    ...envConfig,
  },
  development: {
    ...envConfig,
  },
  test: {
    ...envConfig,
  },
  preprod: {
    ...envConfig,
  },
  production: {
    ...envConfig,
  },
};
