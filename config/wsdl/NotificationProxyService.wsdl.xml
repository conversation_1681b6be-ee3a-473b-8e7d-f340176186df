<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions name="NotificationService" targetNamespace="http://www.globe.com/warcraft/wsdl/notification/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://www.globe.com/warcraft/wsdl/notification/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:ns1="http://www.globe.com/warcraft/wsdl/header/">
  <wsdl:types>
<xs:schema xmlns:tns="http://www.globe.com/warcraft/wsdl/notification/" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="unqualified" targetNamespace="http://www.globe.com/warcraft/wsdl/notification/">
  <xs:element name="NotifyCaseStatusUpdatesResponse" type="tns:NotifyCaseStatusUpdatesResponse"/>
  <xs:element name="NotifyOrderStatusUpdatesResponse" type="tns:NotifyOrderStatusUpdatesResponse"/>
  <xs:element name="SendEmailNotificationResponse" type="tns:SendEmailNotificationResponse"/>
  <xs:element name="SendSMSNotificationResponse" type="tns:SendSMSNotificationResponse"/>
  <xs:complexType name="SendSMSNotification">
    <xs:sequence>
      <xs:element minOccurs="0" name="SMSSourceAddress">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="MSISDN">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="80"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" name="SMSMessage">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" name="NotifPatternId">
        <xs:simpleType>
          <xs:restriction base="xs:int">
            <xs:whiteSpace value="collapse"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" name="NotifParameters">
        <xs:complexType>
          <xs:sequence>
            <xs:element maxOccurs="15" name="NotifParameter" type="tns:NotifParameter"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="NotifParameter">
    <xs:sequence>
      <xs:element minOccurs="0" name="Name">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="32"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" name="Value">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="SendSMSNotificationResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="SendSMSNotificationResult" type="tns:SendSMSNotificationResult"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="SendSMSNotificationResult">
    <xs:sequence>
      <xs:element name="ResultCode">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="5"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="ResultNamespace">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="20"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="NotifyOrderStatusUpdates">
    <xs:sequence>
      <xs:element name="OrderID">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="OrderActionID">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="Status">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="32"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="NotificationType">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="64"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" name="CaseID">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="TimeStamp" type="xs:dateTime"/>
      <xs:element name="SubSalesChannel">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="NotifyOrderStatusUpdatesResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="NotifyOrderStatusUpdatesResult" type="tns:NotifyOrderStatusUpdatesResult"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="NotifyOrderStatusUpdatesResult">
    <xs:sequence>
      <xs:element name="ResultNamespace" type="xs:string"/>
      <xs:element name="QueryResult" type="tns:QueryResult"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="QueryResult">
    <xs:sequence>
      <xs:element name="Status" type="xs:string"/>
      <xs:element name="MessageText" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="SendEmailNotification">
    <xs:sequence>
      <xs:element name="To">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="80"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" name="From">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="64"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="Cc">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="64"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element maxOccurs="unbounded" minOccurs="0" name="Bcc">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="64"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" name="Subject">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="120"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" name="Content">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" name="NotifPatternId">
        <xs:simpleType>
          <xs:restriction base="xs:int">
            <xs:whiteSpace value="collapse"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" name="NotifParameterList">
        <xs:complexType>
          <xs:sequence>
            <xs:element maxOccurs="15" name="NotifParameter" type="tns:NotifParameter"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="SendEmailNotificationResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="SendEmailNotificationResult" type="tns:SendEmailNotificationResult"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="SendEmailNotificationResult">
    <xs:sequence>
      <xs:element name="ResultCode">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="5"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="ResultNamespace">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="20"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="NotifyCaseStatusUpdates">
    <xs:sequence>
      <xs:element name="CaseId">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="CaseTypeLevel1">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="40"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="CaseTypeLevel2">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="40"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="CaseTypeLevel3">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="40"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="CaseTypeLevel4">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="40"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="CaseTypeLevel5">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="40"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="CaseStatus">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element name="CaseStatusReasonCode">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" name="ResolutionCode">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:whiteSpace value="collapse"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="255"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="NotifyCaseStatusUpdatesResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="NotifyCaseStatusUpdatesResult" type="tns:NotifyCaseStatusUpdatesResult"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="NotifyCaseStatusUpdatesResult">
    <xs:sequence>
      <xs:element name="ResultNamespace" type="xs:string"/>
      <xs:element minOccurs="0" name="ResponseCode" type="xs:int"/>
      <xs:element minOccurs="0" name="ResponseText" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SendSMSNotification" nillable="true" type="tns:SendSMSNotification"/>
  <xs:element name="NotifyOrderStatusUpdates" nillable="true" type="tns:NotifyOrderStatusUpdates"/>
  <xs:element name="SendEmailNotification" nillable="true" type="tns:SendEmailNotification"/>
  <xs:element name="NotifyCaseStatusUpdates" nillable="true" type="tns:NotifyCaseStatusUpdates"/>
</xs:schema>
<xs:schema xmlns="http://www.globe.com/warcraft/wsdl/header/" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="unqualified" targetNamespace="http://www.globe.com/warcraft/wsdl/header/">
  <xs:complexType final="extension restriction" name="WarcraftHeader">
    <xs:sequence>
      <xs:element minOccurs="0" name="EsbMessageID" nillable="true" type="xs:string"/>
      <xs:element minOccurs="0" name="EsbRequestDateTime" nillable="true" type="xs:dateTime"/>
      <xs:element minOccurs="0" name="EsbResponseDateTime" nillable="true" type="xs:dateTime"/>
      <xs:element minOccurs="0" name="EsbIMLNumber" nillable="true" type="xs:string"/>
      <xs:element minOccurs="0" name="EsbOperationName" nillable="true" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="WarcraftHeader" nillable="true" type="WarcraftHeader"/>
</xs:schema>
  </wsdl:types>
  <wsdl:message name="NotifyCaseStatusUpdatesResponse">
    <wsdl:part name="result" element="tns:NotifyCaseStatusUpdatesResponse">
    </wsdl:part>
    <wsdl:part name="WarcraftHeader" element="ns1:WarcraftHeader">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="SendSMSNotificationResponse">
    <wsdl:part name="result" element="tns:SendSMSNotificationResponse">
    </wsdl:part>
    <wsdl:part name="WarcraftHeader" element="ns1:WarcraftHeader">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="SendEmailNotificationResponse">
    <wsdl:part name="result" element="tns:SendEmailNotificationResponse">
    </wsdl:part>
    <wsdl:part name="WarcraftHeader" element="ns1:WarcraftHeader">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="NotifyCaseStatusUpdates">
    <wsdl:part name="parameters" element="tns:NotifyCaseStatusUpdates">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="SendSMSNotification">
    <wsdl:part name="parameters" element="tns:SendSMSNotification">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="NotifyOrderStatusUpdatesResponse">
    <wsdl:part name="result" element="tns:NotifyOrderStatusUpdatesResponse">
    </wsdl:part>
    <wsdl:part name="WarcraftHeader" element="ns1:WarcraftHeader">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="NotifyOrderStatusUpdates">
    <wsdl:part name="parameters" element="tns:NotifyOrderStatusUpdates">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="SendEmailNotification">
    <wsdl:part name="parameters" element="tns:SendEmailNotification">
    </wsdl:part>
  </wsdl:message>
  <wsdl:portType name="NotificationProxyService">
    <wsdl:operation name="SendSMSNotification">
      <wsdl:input name="SendSMSNotification" message="tns:SendSMSNotification">
    </wsdl:input>
      <wsdl:output name="SendSMSNotificationResponse" message="tns:SendSMSNotificationResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="NotifyOrderStatusUpdates">
      <wsdl:input name="NotifyOrderStatusUpdates" message="tns:NotifyOrderStatusUpdates">
    </wsdl:input>
      <wsdl:output name="NotifyOrderStatusUpdatesResponse" message="tns:NotifyOrderStatusUpdatesResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendEmailNotification">
      <wsdl:input name="SendEmailNotification" message="tns:SendEmailNotification">
    </wsdl:input>
      <wsdl:output name="SendEmailNotificationResponse" message="tns:SendEmailNotificationResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="NotifyCaseStatusUpdates">
      <wsdl:input name="NotifyCaseStatusUpdates" message="tns:NotifyCaseStatusUpdates">
    </wsdl:input>
      <wsdl:output name="NotifyCaseStatusUpdatesResponse" message="tns:NotifyCaseStatusUpdatesResponse">
    </wsdl:output>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="NotificationServiceSoapBinding" type="tns:NotificationProxyService">
    <soap12:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="SendSMSNotification">
      <soap12:operation soapAction="SendSMSNotification" style="document"/>
      <wsdl:input name="SendSMSNotification">
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="SendSMSNotificationResponse">
        <soap12:header message="tns:SendSMSNotificationResponse" part="WarcraftHeader" use="literal">
        </soap12:header>
        <soap12:body parts="result" use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="NotifyOrderStatusUpdates">
      <soap12:operation soapAction="NotifyOrderStatusUpdates" style="document"/>
      <wsdl:input name="NotifyOrderStatusUpdates">
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="NotifyOrderStatusUpdatesResponse">
        <soap12:header message="tns:NotifyOrderStatusUpdatesResponse" part="WarcraftHeader" use="literal">
        </soap12:header>
        <soap12:body parts="result" use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendEmailNotification">
      <soap12:operation soapAction="SendEmailNotification" style="document"/>
      <wsdl:input name="SendEmailNotification">
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="SendEmailNotificationResponse">
        <soap12:header message="tns:SendEmailNotificationResponse" part="WarcraftHeader" use="literal">
        </soap12:header>
        <soap12:body parts="result" use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="NotifyCaseStatusUpdates">
      <soap12:operation soapAction="NotifyCaseStatusUpdates" style="document"/>
      <wsdl:input name="NotifyCaseStatusUpdates">
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="NotifyCaseStatusUpdatesResponse">
        <soap12:header message="tns:NotifyCaseStatusUpdatesResponse" part="WarcraftHeader" use="literal">
        </soap12:header>
        <soap12:body parts="result" use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="NotificationService">
    <wsdl:port name="NotificationServicePort" binding="tns:NotificationServiceSoapBinding">
      <soap12:address location="http://***********:8011/notification"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>