const commonConfig = {
  awsAccessKey: process.env.AWS_ACCESS_KEY,
  awsSecret: process.env.AWS_SECRET_KEY,
  awsRegion: process.env.AWS_REGION,
  dynamooseCore: {
    region: process.env.DDB_AWS_REGION,
    endpoint: process.env.DDB_ENDPOINT,
    maxAttempts: process.env.DDB_MAX_ATTEMPTS || 10,
    requestHandler: {
      httpsAgent: {
        maxSockets: 50,
      },
      // requestTimeout: 60_000,
    },
  },
  dynamooseTable: {
    create: process.env.DYNAMOOSE_CREATE_TABLES || false,
    update: process.env.DYNAMOOSE_UPDATE_TABLES || false,
    // disable waitForActive so dynamoose don't need to use DynamoDB:DescribeTable action
    // this could lead to potential sync issues if the schema on the cloud is different from the app
    waitForActive: process.env.DYNAMOOSE_WAIT_FOR_ACTIVE || false,
  },
};

module.exports = {
  local: {
    ...commonConfig,
    dynamooseCore: {
      ...commonConfig.dynamooseCore,
      // dummy creds since it's required in latest aws sdk
      credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test',
      },
    },
    // create and update tables in local for easy testing
    dynamooseTable: {
      ...commonConfig.dynamooseTable,
      create: true,
      update: true,
    },
  },
  development: {
    ...commonConfig,
  },
  test: {
    ...commonConfig,
  },
  preprod: {
    ...commonConfig,
  },
  production: {
    ...commonConfig,
  },
};
