module.exports = {
  development: {
    collection: ['j<PERSON><PERSON><PERSON>@stratpoint.com', '<EMAIL>'],
    creditCard: ['j<PERSON>nan<PERSON>@stratpoint.com', '<EMAIL>'],
    billingNotif: ['j<PERSON>nan<PERSON>@stratpoint.com', '<EMAIL>'],
    globeOne: ['j<PERSON><PERSON><PERSON>@stratpoint.com', '<EMAIL>'],
    ecpay: ['j<PERSON><EMAIL>', '<EMAIL>'],
  },
  test: {
    collection: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    creditCard: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    billingNotif: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    globeOne: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    ecpay: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
  },
  preprod: {
    collection: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    creditCard: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    billingNotif: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    globeOne: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    ecpay: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
  },
  production: {
    collection: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
    creditCard: ['<EMAIL>'],
    billingNotif: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    globeOne: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
    ecpay: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ],
  },
};
