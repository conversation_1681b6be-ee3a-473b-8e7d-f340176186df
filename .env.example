PORT=3000
STAGE=local

# CORS
APP_URL=http://localhost:3000

# AWS_DYNAMO_ENDPOINT=http://localhost:4566
# Use this if you are not using localstack
# AWS_ENDPOINT=http://localhost:4566
AWS_ENDPOINT=http://localstack:4566
AWS_REGION=ap-southeast-1

DDB_ENDPOINT=http://localstack:4566
DDB_AWS_REGION=ap-southeast-1
DDB_MAX_ATTEMPTS=10

DDB_TABLE_TRANSACTION_LOGS=isg-gpayo-local-transLogs
DDB_TABLE_CHANNELS=isg-gpayo-local-channels
DDB_TABLE_USERS=isg-gpayo-local-users
DDB_TABLE_AUDITLOGS=isg-gpayo-local-auditlogs
DDB_TABLE_ROLES=isg-gpayo-local-roles
DDB_TABLE_CONFIGURATION=isg-gpayo-local-configurations
DDB_TABLE_REFUND=isg-gpayo-local-refundTrans
DDB_TABLE_SETTLEMENT=isg-npayo-local-settlement-ns
DDB_TABLE_POST_PAYMENT_CONFIG=isg-gpayo-local-post-payment-config

JWT_KEY=fBu~DDrWX95JE%25D5xk%3FQ%sCL19Ie

OKTA_ISSUER=
OKTA_CLIENT_ID=

KAFKA_TOPIC_REFUND=
KAFKA_MESSAGE_EVENT_CC_REFUND=
KAFKA_TOPIC_REFUND_SCHEMA_VERSION=
KAFKA_BROKERS=localhost:19092

KAFKA_SASL_USERNAME=CHANGE_ME
KAFKA_SASL_PASSWORD=CHANGE_ME
KAFKA_SECURITY_PROTOCOL=SASL_SSL
KAFKA_MECHANISM=PLAIN


KAFKA_PRODUCER_ACKS=-1

# ---------------------------------------------------------------- #
# -------------------------- PAY BY LINK ------------------------- #
# ---------------------------------------------------------------- #
XENDIT_URL=https://api.xendit.co
XENDIT_SECRET_KEY=test_secret_key
PAYBYLINK_INVOICE_DURATION=86400
PAYBYLINK_PAYMENT_METHODS=CREDIT_CARD,DD_BPI,DD_RCBC,DD_UBP,GRABPAY,PAYMAYA,SHOPEEPAY