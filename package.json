{"name": "gpayo-cms-backend-service", "version": "1.0.0", "description": "Graphql template for generating resolver server", "main": "index.js", "private": true, "engines": {"node": ">=18.19.0"}, "scripts": {"dev": "cross-env NODE_PATH=. NODE_ENV=local nodemon --ext js,json,graphql --stack-trace-limit=1000", "server": "cross-env NODE_PATH=. node index.js --stack-trace-limit=1000", "test": "cross-env NODE_PATH=. jest", "test:watch": "cross-env NODE_PATH=. jest --watch", "test:coverage": "cross-env NODE_PATH=. jest --coverage", "test:mocha": "cross-env NODE_PATH=. mocha --config test/.mocharc.json --exit 'test/**/*.spec.js'", "coverage": "cross-env NODE_PATH=. NODE_ENV=test nyc --reporter=lcov npm run test:mocha", "lint": "eslint {src,test,config}/**/*.js -f json -o ./reports/eslint-issues.json", "lint:xendit-file": "eslint src/app/handlers/xenditRefund.js -f json -o ./reports/eslint-issues.json", "console": "cross-env NODE_PATH=. node src/interfaces/console/index.js", "seeder": "node src/infra/seeder/index.js", "prepare": "husky"}, "repository": {}, "author": "Stratpoint Technologies Inc.", "contributors": ["<PERSON><PERSON> <j<PERSON><PERSON><PERSON>@stratpoint.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Aviel Cana <<EMAIL>>"], "license": "MIT", "dependencies": {"@aws-sdk/client-athena": "^3.509.0", "@aws-sdk/client-dynamodb": "^3.514.0", "@aws-sdk/client-s3": "^3.509.0", "@aws-sdk/lib-dynamodb": "^3.514.0", "@aws-sdk/lib-storage": "^3.507.0", "@aws-sdk/s3-request-presigner": "^3.511.0", "@graphql-tools/load-files": "^7.0.0", "@graphql-tools/merge": "^9.0.1", "@graphql-tools/schema": "^10.0.2", "@graphql-yoga/plugin-disable-introspection": "^2.1.1", "@okta/jwt-verifier": "^4.0.1", "@spbrewery/adyen": "^2.0.3", "@spbrewery/hip": "^2.0.1", "@spbrewery/log": "^2.0.1", "@kafkajs/confluent-schema-registry": "3.3.0", "awilix": "^10.0.1", "awilix-express": "^9.0.1", "aws-sdk": "^2.985.0", "bcrypt": "^6.0.0", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^3.1.6", "cross-env": "^7.0.3", "csv-writer": "^1.6.0", "csvtojson": "^2.0.10", "dotenv": "^16.4.2", "dynamoose": "^4.0.0", "express": "^4.18.2", "express-useragent": "^1.0.15", "graphql": "^16.8.1", "graphql-middleware": "^6.1.35", "graphql-upload-minimal": "^1.5.5", "graphql-yoga": "^5.1.1", "http-status": "^1.7.3", "inflection": "^3.0.0", "listr": "^0.14.3", "method-override": "^3.0.0", "microtime": "3.1.1", "node-os-utils": "^1.3.7", "node-rdkafka": "^3.3.1", "replace-in-file": "^7.1.0", "ssh2-sftp-client": "^10.0.3", "structure": "^2.0.1"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@eslint/js": "^9.3.0", "@faker-js/faker": "^8.4.1", "chai": "^4.5.0", "chance": "^1.1.11", "cz-conventional-changelog": "^3.3.0", "dirty-chai": "^2.0.1", "eslint": "^9.3.0", "eslint-config-prettier": "^9.1.0", "globals": "^15.3.0", "husky": "^9.1.7", "jest": "^29.7.0", "joi": "^17.12.0", "mocha": "^10.8.2", "nodemon": "^3.0.3", "nyc": "^15.1.0", "prettier": "^3.2.5", "sinon": "^17.0.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.js": ["eslint --cache --cache-location node_modules --report-unused-disable-directives --no-warn-ignored --max-warnings 1", "prettier --write --ignore-unknown"], "test/**/*.spec.js": "cross-env NODE_PATH=. jest --findRelatedTests"}}