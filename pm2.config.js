/*
  PM2 Process Management
  https://pm2.keymetrics.io/docs/usage/process-management/

  PM2 Ecosystem
  https://pm2.keymetrics.io/docs/usage/application-declaration/

  Use either of the ff. commands to manage PM2 processes
  Start environment
    pm2 start pm2.config --only <environment_name>

  Restart environment
    pm2 restart <environment_name> --update-env
    pm2 restart pm2.config --only <environment_name>

  Delete environment
    pm2 delete <environment_name>
    pm2 delete pm2.config --only <environment_name>
*/

// add details here if you want different environment configuration
const environments = [
  { name: 'develop', environment: 'development' },
  { name: 'testbed', environment: 'test' },
  { name: 'prod', environment: 'production' },
];

const apps = environments.map((env) => {
  const { name, environment } = env;
  return {
    name,
    script: 'npm run server',
    restart_delay: 1000 * 60 * 1, // restart every N milliseconds on error
    env: {
      NODE_ENV: environment,
    },
  };
});

module.exports = {
  apps,
};
