const { createContainer, asClass, asFunction, asValue } = require('awilix');
const { scopePerRequest } = require('awilix-express');
const { BreweryEsb, getWsdlByFile } = require('@spbrewery/hip');

const path = require('path');
const handlers = require('./app/handlers');
const middlewares = require('./app/middlewares');
const errors = require('./domain/errors');

const authorization = require('./app/middlewares/authorization');

/* Require Operations END */

const config = require('../config');
const Application = require('./app/Application');
const serializer = require('./interfaces/http/utils/serializer');

const Server = require('./interfaces/http/Server');
const router = require('./interfaces/http/router');
const loggerMiddleware = require('./interfaces/http/logging/loggerMiddleware');
const errorHandler = require('./interfaces/http/errors/errorHandler');
const devErrorHandler = require('./interfaces/http/errors/devErrorHandler');
// TODO: Remove comment after DynamoDB provisioning
// const Cronjobs = require('./interfaces/jobs/index');

const base64 = require('./infra/utils/base64');
const jwt = require('./infra/jwt');
const uuidGenerator = require('./infra/utils/Uuid');
const Mapper = require('./infra/utils/Mapper');
const DateHelper = require('./infra/utils/date');
const Category = require('./infra/utils/Category');
const Performance = require('./infra/utils/Performance');
const Notification = require('./infra/utils/Notification');
const uploadFile = require('./infra/utils/UploadS3Files');
const dataMask = require('./infra/utils/dataMask');
const formatter = require('./infra/utils/formatter');
const randomString = require('./infra/utils/randomString');

const authClient = require('./infra/authentication');

const paymentGateway = require('./infra/paymentGateway/paymentGateway');

const okta = require('./infra/okta/verify');
const logger = require('./infra/logging/logger');
const repositories = require('./infra/repositories');
const Kafka = require('./infra/kafka');
// const athenaGateway = require('./infra/athena');
// TODO: Remove comment after DynamoDB provisioning
const { database, models, schemas } = require('./infra/database');

const XenditService = require('./services/xenditService');

// Notification ESB
const notifWsdl = getWsdlByFile(path.resolve('config/wsdl', 'NotificationProxyService.wsdl.xml'));
const notifEsbClient = new BreweryEsb({
  wsdl: notifWsdl,
  apiUrl: process.env.ESB_NOTIFICATION_ENDPOINT,
});

const container = createContainer();

// System
container.register({
  // TODO: Remove comment after DynamoDB provisioning
  // cron: asClass(Cronjobs).singleton(),
  app: asClass(Application).singleton(),
  server: asClass(Server).singleton(),
  router: asFunction(router).singleton(),
  logger: asFunction(logger).singleton(),
  loginVerifier: asFunction(okta).singleton(),
  paymentGateway: asFunction(paymentGateway).singleton(),
  config: asValue(config),
  kafka: asClass(Kafka).singleton(),
  xenditService: asClass(XenditService),
});

// Middlewares
container.register({
  loggerMiddleware: asFunction(loggerMiddleware).singleton(),
  errorHandler: asValue(config.production ? errorHandler : devErrorHandler),
});

// Registering middleware for per-request scope
container.register({
  containerMiddleware: asValue(scopePerRequest(container)),
});

// Repositories
// TODO: Remove comment after DynamoDB provisioning
container.register(repositories);
// container.register(athenaGateway);

// Database
// TODO: Remove comment after DynamoDB provisioning

container.register({
  ...models,
  ...schemas,
  database: asValue(database),
});

// serializer
container.register({
  serializer: asValue(serializer),
});

// Errors
container.register({
  errors: asValue(errors),
});

// Handlers and Middlewares
container.register({
  handlers: asClass(handlers),
  middlewares: asClass(middlewares),
});

// Authorization
container.register({
  authorization: asClass(authorization).singleton(),
  jwt: asClass(jwt),
});

// Other values
container.register({
  uuid: asValue(uuidGenerator),
  Mapper: asValue(Mapper),
  DateHelper: asValue(DateHelper),
  Category: asValue(Category),
  Performance: asValue(Performance),
  base64: asValue(base64),
  authClient: asValue(authClient),
  notifEsbClient: asValue(notifEsbClient),
  Notification: asValue(Notification),
  uploadFile: asValue(uploadFile),
  dataMask: asValue(dataMask),
  formatter: asValue(formatter),
  randomString: asValue(randomString),
});

module.exports = container;
