// todo DomainError
const makeError = (err) => () => new Error(err);

module.exports = {
  accessTokenRequest: makeError('ACCESS_TOKEN_REQUEST_ERROR'),
  bindingNotFound: makeError('BINDING_RECORD_NOT_FOUND'),
  notFound: makeError('NOT_FOUND'),
  userNotFound: makeError('USER_NOT_FOUND'),
  deletionError: makeError('COULD_NOT_DELETE_DATA'),
  verificationError: makeError('VERIFICATION_FAILED'),
  userCreateError: makeError('USER_EMAIL_ALREADY_EXISTS'),
  userUpdateError: makeError('USER_EMAIL_ALREADY_EXISTS'),
  roleNotFound: makeError('ROLE_NOT_FOUND'),
  channelCreationError: makeError('CHANNEL_EMAIL_ALREADY_EXISTS'),
  channelIdError: makeError('CHANNEL_ID_ALREADY_EXISTS'),
  convenienceFeeUniqueness: makeError('CONVENIENCE_FEE_ALREADY_EXISTS'),
  channelIdNotFound: makeError('CHANNEL_ID_NOT_FOUND'),
  channelIdNotMatch: makeError('CHANNEL_ID_DOES_NOT_MATCH'),
  channelCodeError: makeError('CHANNEL_CODE_ALREADY_EXISTS'),
  channelSubMerchantError: makeError('SUB_MERCHANT_DOES_NOT_EXIST_IN_CONFIG'),
  channelSubMerchantExistError: makeError('SUB_MERCHANT_IS_USED'),
  channelDeletionError: makeError('CHANNEL_IS_NOT_VERIFIED'),
  channelNotFound: makeError('CHANNEL_NOT_FOUND'),
  channelGcreditSubMerchant: makeError('GCREDIT_SUBMERCHANT_ALREADY_EXIST'),
  channelClientIdError: makeError('CLIENT_ID_ALREADY_EXISTS'),
  channelDataValidationError: makeError('CHANNEL_DATA_VALIDATION_ERROR'),
  roleIdError: makeError('ROLE_ID_ALREADY_EXISTS'),
  bankError: makeError('BANK_ALREADY_EXISTS'),
  userGetError: makeError('COULD_NOT_FOUND_USER'),
  mIdNotFound: makeError('MERCHANT_ID_NOT_FOUND'),
  mIdExistError: makeError('MERCHANT_ID_ALREADY_EXISTS'),
  mIdBillUniqueness: makeError('COMPANY_PAYMENT_TYPE_BILL_TYPE_ALREADY_EXIST'),
  mIdNonBillUniqueness: makeError('CHANNEL_HAS_EXISTING_MID'),
  mIdAlreadyInUsedByMainMId: makeError('INSTALLMENT_MID_ALREADY_INUSED_BY_MAIN_MID'),
  mIdNonBillChannelError: makeError('MID_CHANNEL_NOT_FOUND'),
  bankCodeError: makeError('BANK_CODE_ALREADY_EXIST'),
  bankNameError: makeError('BANK_NAME_ALREADY_EXIST'),
  installmentMIdUniqueness: makeError('INSTALLMENT_DETAILS_ALREADY_EXISt'),
  deativationUserNotFound: makeError('SOME_USERS_NOT_EXISTS'),
};
