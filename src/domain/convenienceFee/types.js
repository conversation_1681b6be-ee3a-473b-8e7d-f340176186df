const { attributes } = require('structure');

const ConvenienceFee = {
  InsertConvenienceFee: attributes({
    id: {
      type: String,
      required: true,
    },
    compositeKey: {
      type: String,
      required: true,
    },
    channelId: {
      type: String,
      maxLength: 100,
      required: true,
      regex: /^[^=,@,+,-]/,
    },
    name: {
      type: String,
      required: true,
    },
    brand: {
      type: String,
      regex: /^[a-zA-Z-]+$/,
      required: true,
    },
    gatewayProcessor: {
      type: String,
      equal: ['adyen', 'gcash', 'xendit'],
      required: true,
    },
    paymentMethod: {
      type: String,
      required: true,
    },
    transactionType: {
      type: String,
      equal: ['Bill', 'NonBill'],
      required: true,
    },
    convenienceFeeType: {
      type: String,
      equal: ['Off', 'Flat', 'Percent', 'Tiered_Scheme'],
      default: 'Off',
    },
    convenienceFeeValue: {
      type: Number,
      postive: true,
      min: 0,
      default: 0,
      precision: 2,
    },
    convenienceFeeThreshold: {
      type: Number,
      postive: true,
      min: 0,
      default: 0,
      precision: 2,
    },
    convenienceFeeTieredScheme: {
      type: Number,
      postive: true,
      min: 0,
      default: 0,
      precision: 2,
    },
    createdAt: {
      type: String,
    },
    updatedAt: {
      type: String,
    },
  })(
    class ConvenienceFee {
      validatePaymentMethod() {
        let isValid = {
          valid: true,
          errors: [],
        };
        const hasValidPaymentMethod = {
          adyen: ['default', 'dropin', 'paybylink'],
          gcash: ['dragonpay_gcash'],
          xendit: ['CC_DC', 'PAYMAYA', 'SHOPEEPAY', 'GRABPAY', 'BPI', 'UBP', 'RCBC'],
        };
        const isValidPaymentMethod = hasValidPaymentMethod[this.gatewayProcessor].includes(this.paymentMethod);
        if (!isValidPaymentMethod) {
          isValid.errors.push(
            `paymentMethod: '${this.paymentMethod}' is not supported in selected gatewayProcessor: '${this.gatewayProcessor}'`
          );
        }
        isValid = {
          valid: isValidPaymentMethod,
          errors: isValid.errors,
        };
        return isValid;
      }
    }
  ),
  UpdateConvenienceFee: attributes({
    id: {
      type: String,
      required: true,
    },
    compositeKey: {
      type: String,
      required: true,
    },
    channelId: {
      type: String,
      maxLength: 100,
      regex: /^[^=,@,+,-]/,
      required: true,
    },
    brand: {
      type: String,
      regex: /^[a-zA-Z-]+$/,
      required: true,
    },
    gatewayProcessor: {
      type: String,
      equal: ['adyen', 'gcash', 'xendit'],
      required: true,
    },
    paymentMethod: {
      type: String,
      required: true,
    },
    transactionType: {
      type: String,
      equal: ['Bill', 'NonBill'],
      required: true,
    },
    convenienceFeeType: {
      type: String,
      equal: ['Off', 'Flat', 'Percent', 'Tiered_Scheme'],
      default: 'Off',
    },
    convenienceFeeValue: {
      type: Number,
      postive: true,
      min: 0,
      default: 0,
      precision: 2,
    },
    convenienceFeeThreshold: {
      type: Number,
      postive: true,
      min: 0,
      default: 0,
      precision: 2,
    },
    convenienceFeeTieredScheme: {
      type: Number,
      postive: true,
      min: 0,
      default: 0,
      precision: 2,
    },
    createdAt: {
      type: String,
    },
    updatedAt: {
      type: String,
    },
  })(
    class ConvenienceFee {
      validatePaymentMethod() {
        let isValid = {
          valid: true,
          errors: [],
        };
        const hasValidPaymentMethod = {
          adyen: ['default', 'dropin', 'paybylink'],
          gcash: ['dragonpay_gcash'],
          xendit: ['CC_DC', 'PAYMAYA', 'SHOPEEPAY', 'GRABPAY', 'BPI', 'UBP', 'RCBC'],
        };
        const isValidPaymentMethod = hasValidPaymentMethod[this.gatewayProcessor].includes(this.paymentMethod);
        if (!isValidPaymentMethod) {
          isValid.errors.push(
            `paymentMethod: '${this.paymentMethod}' is not supported in selected gatewayProcessor: '${this.gatewayProcessor}'`
          );
        }
        isValid = {
          valid: isValidPaymentMethod,
          errors: isValid.errors,
        };
        return isValid;
      }
    }
  ),
};

module.exports = ConvenienceFee;
