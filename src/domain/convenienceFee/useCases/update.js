const types = require('../../types');

module.exports = (payload) => {
  // Deconstruct payload for type coersions
  const convenienceFeePayload = new types.UpdateConvenienceFee(payload);

  // Validate Input Params
  const { valid, errors } = convenienceFeePayload.validate();

  if (!valid) {
    const error = new Error('ConvenienceFeeUpdateError');
    error.message = JSON.stringify(errors);
    throw error;
  }

  // Valid payment method according to payment gateway
  const paymentMethodValidation = convenienceFeePayload.validatePaymentMethod();
  if (!paymentMethodValidation.valid) {
    const error = new Error('ConvenienceFeeCreationError');
    error.message = paymentMethodValidation.errors[0];
    throw error;
  }

  return convenienceFeePayload.toJSON();
};
