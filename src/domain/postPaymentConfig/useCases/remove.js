const types = require('../../types');

module.exports = (payload) => {
  const postPaymentConfigPayload = new types.DeletePostPaymentConfig(payload);
  // Validate Input Params
  const { valid, errors } = postPaymentConfigPayload.validate();
  if (!valid) {
    const error = new Error('PostPaymentConfigPayloadDeletionError');
    error.message = errors;
    throw error;
  }

  return postPaymentConfigPayload.toJSON();
};
