const types = require('../../types');

module.exports = (payload) => {
  // Deconstruct payload for type coersions
  const postPaymentConfigPayload = new types.UpdatePostPaymentConfig(payload);

  // Validate Input Params
  const { valid, errors } = postPaymentConfigPayload.validate();
  if (!valid) {
    const error = new Error('PostPaymentConfigPayloadUpdateError');
    error.message = errors;
    throw error;
  }

  return postPaymentConfigPayload.toJSON();
};
