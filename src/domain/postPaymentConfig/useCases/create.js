const types = require('../../types');

module.exports = (payload) => {
  const postPaymentConfigPayload = new types.PostPaymentConfig(payload);
  // Validate Input Params
  const { valid, errors } = postPaymentConfigPayload.validate();
  if (!valid) {
    const error = new Error('PostPaymentConfigPayloadCreationError');
    error.message = errors;
    throw error;
  }

  return postPaymentConfigPayload.toJSON();
};
