const { attributes } = require('structure');

const PostPaymentConfig = {
  PostPaymentConfig: attributes({
    channelId: {
      type: String,
      maxLength: 100,
      required: true,
      regex: /^[^=,@,+,-]/,
    },
    compositeKey: {
      type: String,
      default: (instance) => instance.getCompositeKey(),
    },
    gatewayProcessor: {
      type: String,
      // equal: ['adyen', 'gcash', 'xendit'],
      equal: ['xendit'],
      required: true,
    },
    paymentMethod: {
      type: String,
      // equal: [
      //   'cc_dc',
      //   'gcash',
      //   'bpi',
      //   'rcbc',
      //   'ubp',
      //   'paymaya',
      //   'grabpay',
      //   'shopeepay',
      //   'visa',
      //   'mastercard',
      // ],
      equal: ['card_straight', 'card_installment'],
      required: true,
    },
    billRealTime: {
      type: String,
      required: true,
    },
    billFallout: {
      type: Number,
      required: true,
    },
    nonBill: {
      type: Number,
      required: true,
    },
    createDateTime: {
      type: String,
      default: () => new Date().toISOString(),
    },
    updateDateTime: {
      type: String,
      default: () => new Date().toISOString(),
    },
  })(
    class PostPaymentConfig {
      getCompositeKey() {
        return `${this.gatewayProcessor}_${this.paymentMethod}`;
      }
    }
  ),

  UpdatePostPaymentConfig: attributes({
    billRealTime: {
      type: String,
    },
    billFallout: {
      type: Number,
    },
    nonBill: {
      type: Number,
    },
    updateDateTime: {
      type: String,
      default: () => new Date().toISOString(),
    },
  })(class PostPaymentConfig {}),

  DeletePostPaymentConfig: attributes({
    channelId: {
      type: String,
      required: true,
    },
    compositeKey: {
      type: String,
      required: true,
    },
  })(class PostPaymentConfig {}),
};

module.exports = PostPaymentConfig;
