const { attributes } = require('structure');

const ConvenienceFeeBrand = {
  InsertConvenienceFeeBrand: attributes({
    id: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      maxLength: 400,
      required: true,
    },
    createdAt: {
      type: String,
    },
    updatedAt: {
      type: String,
    },
  })(class InsertConvenienceFeeBrand {}),

  UpdateConvenienceFeeBrand: attributes({
    id: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      maxLength: 400,
      required: true,
    },
    updatedAt: {
      type: String,
    },
  })(class UpdateConvenienceFee {}),
};

module.exports = ConvenienceFeeBrand;
