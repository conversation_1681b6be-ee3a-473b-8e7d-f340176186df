const types = require('../../types');

module.exports = (payload) => {
  // Deconstruct payload for type coersions
  const convenienceFeeBrandPayload = new types.InsertConvenienceFeeBrand(payload);

  // Validate Input Params
  const { valid, errors } = convenienceFeeBrandPayload.validate();
  if (!valid) {
    const error = new Error('ConvenienceFeeBrandCreationError');
    error.message = JSON.stringify(errors);
    throw error;
  }

  return convenienceFeeBrandPayload.toJSON();
};
