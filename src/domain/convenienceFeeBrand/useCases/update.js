const types = require('../../types');

module.exports = (payload) => {
  // Deconstruct payload for type coersions
  const convenienceFeeBrandPayload = new types.UpdateConvenienceFeeBrand(payload);

  // Validate Input Params
  const { valid, errors } = convenienceFeeBrandPayload.validate();

  if (!valid) {
    const error = new Error('ConvenienceFeeBrandUpdateError');
    error.message = JSON.stringify(errors);
    throw error;
  }

  return convenienceFeeBrandPayload.toJSON();
};
