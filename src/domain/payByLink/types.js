const { attributes } = require('structure');

const PayByLink = {
  PayByLink: attributes({
    channelId: {
      type: String,
      required: true,
    },
    amountValue: {
      type: Number,
      required: true,
    },
    description: {
      type: String,
    },
    emailAddress: {
      type: String,
    },
    mobileNumber: {
      type: String,
    },
    notificationPreference: {
      type: Array,
      itemType: String,
      default: [],
    },
  })(
    class PayByLink {
      validateNotificationPreferences() {
        let result = {
          valid: true,
          errors: [],
        };

        const validValues = ['whatsapp', 'email', 'viber'];
        const isPayloadValid = this.notificationPreference.every((element) => validValues.includes(element));

        if (!isPayloadValid) {
          result.valid = false;
          result.errors.push({
            message: 'notificationPreference must be one of [whatsapp, email, viber]',
          });
        }

        return result;
      }
    }
  ),
};

module.exports = PayByLink;
