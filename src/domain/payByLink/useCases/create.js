const types = require('../../types');

module.exports = (payload) => {
  const payByLink = new types.PayByLink({ ...payload });
  const { valid, errors } = payByLink.validate();

  if (!valid) {
    const error = new Error('PayByLinkCreationError');
    error.message = errors.map((err) => err.message);
    throw error;
  }

  const notificationPreferencesValidation = payByLink.validateNotificationPreferences();

  if (!notificationPreferencesValidation.valid) {
    const error = new Error('PayByLinkCreationError');
    error.message = notificationPreferencesValidation.errors.map((err) => err.message);
    throw error;
  }

  return payByLink.toJSON();
};
