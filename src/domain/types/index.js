// Payment types
const UserTypes = require('../users/types');
const ChannelTypes = require('../channels/types');
const RolesTypes = require('../role/types');
const AclTypes = require('../acl/types');
const AuditTypes = require('../audit/types');
const ReportTypes = require('../transactions/types');
const MidTypes = require('../mid/types');
const BankTypes = require('../bank/types');
const VoucherTypes = require('../voucher/types');
const InstallmentMIdTypes = require('../installmentMid/types');
const ConfigurationTypes = require('../config/types');
const BillLinerConfigTypes = require('../billLiner/types');
const PostPaymentConfig = require('../postPaymentConfig/types');
const ConvenienceFee = require('../convenienceFee/types');
const ConvenienceFeeBrand = require('../convenienceFeeBrand/types');
const PayByLink = require('../payByLink/types');
module.exports = {
  ...UserTypes,
  ...ChannelTypes,
  ...RolesTypes,
  ...AclTypes,
  ...AuditTypes,
  ...ReportTypes,
  ...MidTypes,
  ...BankTypes,
  ...VoucherTypes,
  ...InstallmentMIdTypes,
  ...ConfigurationTypes,
  ...BillLinerConfigTypes,
  ...PostPaymentConfig,
  ...ConvenienceFee,
  ...ConvenienceFeeBrand,
  ...PayByLink,
};
