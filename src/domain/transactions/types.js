const config = require('../../../config');
const { attributes } = require('structure');

const Reports = {
  Transactions: attributes({
    reference: String,
    accountNumber: String,
    mobileNumber: String,
    creditCardHolderName: String,
    creditCardCountry: String,
    creditCardType: String,
    creditCardBank: String,
    creditCardNumber: String,
    ipAddress: String,
    channelName: String,
    paymentMethod: String,
    status: String,
    amountValue: String,
    amountCurrency: String,
    createdAt: String,
    recordDate: String,
    grossAmount: {
      type: Number,
      default: 0,
    },
    withholdingtax: {
      type: String,
      default: 0,
    },
    bankDiscount: {
      type: Number,
      default: 0,
    },
    netAmount: {
      type: Number,
      default: 0,
    },
    transId: String,
    mid: String,
    prodDesc: String,
    merchantName: String,
    merchantCompany: String,
    authCode: String,
    depositoryBank: String,
    depositoryBankAccountNo: String,
    threeDFlag: String,
    postPaymentReferenceId: String,
    payModeCode: String,
    payModeDesc: String,
    paymentTypeCode: {
      type: String,
      default: 'HPBILPAY',
    },
    qty: {
      type: Number,
      default: '1',
    },
    emailAddress: String,
    paymentGateway: String,
    splitPayment: Boolean,
    billType: String,
    paymentType: String,
    fundingSource: String,
    fromBatchFile: Boolean,
    costCenter: String,
    channelReference: String,
    postedTimestamp: String,
    billerName: String,
    subMerchantId: String,
    refusalReasonRaw: String,
    postPaymentReason: String,
    postPaymentEsbMessageId: String,
    paymentLinkId: String,
    description: String,
    isOrTransaction: Boolean,
    loadType: String,
    orStatus: String,
    lukeOrTimestamp: String,
    refundAmount: String,
    refundReason: String,
    channelId: String,
    refundApprovalStatus: String,
    paymentStatus: String,
    acquirementId: String,
    transactionId: String,
    timestamp: String,
    refundStatus: String,
    installmentPaymentId: String,
    installmentTerm: String,
    itemPurchased: String,
    orderReference: String,
    isThreeDFlag: Boolean,
    customerContactNumber: String,
    refundId: String,
    refundType: String,
    isRefundable: Boolean,
    refundDate: String,
    budgetProtectValue: String,
    isBinding: Boolean,
    bindingRequestID: String,
    isMiscellaneous: Boolean,
    finalAmount: Number,
    convenienceFee: Number,
  })(class Transactions {}),

  GCashRefundInput: attributes({
    reference: {
      type: String,
      maxLength: 50,
    },
    status: {
      type: String,
      equal: ['POSTED', 'POSTED_LUKE', 'GCASH_AUTHORISED'],
    },
    channelName: {
      type: String,
      maxLength: 25,
    },
    paymentGateway: {
      type: String,
      default: 'gcash',
      maxLength: 5,
    },
    channelId: {
      type: String,
      maxLength: 50,
    },
    refundId: {
      type: String,
    },
    userAssignedChannels: {
      type: Array,
      itemType: String,
      unique: true,
    },
    billType: {
      type: String,
    },
    hasConvenienceFee: {
      type: Boolean,
    },
  })(class GCashRefundInput {}),

  CardRefundInput: attributes({
    reference: {
      type: String,
      maxLength: 50,
    },
    status: {
      type: String,
      equal: ['POSTED', 'POSTED_LUKE', 'ADYEN_AUTHORISED'],
    },
    channelName: {
      type: String,
      maxLength: 25,
    },
    paymentGateway: {
      type: String,
      default: 'adyen',
      maxLength: 5,
    },
    channelId: {
      type: String,
      maxLength: 50,
    },
    refundId: {
      type: String,
    },
    userAssignedChannels: {
      type: Array,
      itemType: String,
      unique: true,
    },
    billType: {
      type: String,
    },
  })(class CardRefundInput {}),

  RefundForApproval: attributes({
    paymentId: {
      type: String,
    },
    requestTimeStamp: {
      type: String,
      default: () => new Date().toISOString(),
    },
    entity: {
      type: String,
      default: 'refund',
    },
    accountNumber: {
      type: String,
    },
    acquirementId: {
      type: String,
    },
    channelId: {
      type: String,
    },
    channelName: {
      type: String,
    },
    timestamp: {
      type: String,
    },
    postedTimestamp: {
      type: String,
    },
    amountValue: {
      type: String,
    },
    status: {
      type: String,
    },
    refundReason: {
      type: String,
      maxLength: 50,
    },
    refundAmount: {
      type: String,
      maxLength: 16,
    },
    transactionId: {
      type: String,
      maxLength: 50,
    },
    paymentGateway: {
      type: String,
      maxLength: 10,
    },
    mobileNumber: {
      type: String,
    },
    refundApprovalStatus: {
      type: String,
      equal: ['For Approval', 'Approved', 'Rejected', 'Declined'],
    },
    billType: {
      type: String,
      equal: ['Bill', 'NonBill'],
    },
    accountType: {
      type: String,
    },
    fundingSource: {
      type: String,
    },
    emailAddress: {
      type: String,
    },
    ttl: {
      type: Number,
      default: () => {
        const now = new Date();
        now.setMonth(now.getMonth() + 6);
        return Math.floor(now / 1000);
      },
    },
    totalAmount: {
      type: Number,
    },
    convenienceFee: {
      type: Number,
    },
    hasConvenienceFee: {
      type: Boolean,
    },
  })(class RefundForApproval {}),

  BulkSearch: attributes({
    reference: {
      type: String,
      maxLength: 20,
    },
    accountNumber: {
      type: String,
      maxLength: 12,
    },
    datefrom: {
      type: String,
      regex: /(0[1-9]|1[012])[/]\d{2}[/]\d{4}$/,
    },
    dateto: {
      type: String,
      regex: /(0[1-9]|1[012])[/]\d{2}[/]\d{4}$/,
    },
  })(class BulkSearch {}),

  EWalletRefundInput: attributes({
    paymentId: {
      type: String,
      maxLength: 50,
    },
    status: {
      type: String,
      equal: JSON.parse(config.paymentGateway.REFUND_REQUEST_EXPECTED_STATUSES),
    },
    channelName: {
      type: String,
      maxLength: 25,
    },
    paymentGateway: {
      type: String,
      default: 'xendit',
    },
    refundId: {
      type: String,
    },
    userAssignedChannels: {
      type: Array,
      itemType: String,
      unique: true,
    },
    hasConvenienceFee: {
      type: Boolean,
    },
    createDateTime: {
      type: Object,
    },
  })(class EWalletRefundInput {}),
};

module.exports = Reports;
