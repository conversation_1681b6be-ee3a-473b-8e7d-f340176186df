const types = require('../../types');

module.exports = (payload) => {
  // Deconstruct payload for type coersions
  const gcashRefundInputPayload = new types.GCashRefundInput(payload.filter);
  // Validate Input Params
  const { valid, errors } = gcashRefundInputPayload.validate();
  if (!valid) {
    const error = new Error('GCash Refund Filter Input Error');
    error.message = errors;
    throw error;
  }
  const validatedPayload = gcashRefundInputPayload.toJSON();
  if (typeof validatedPayload.status === 'undefined') {
    validatedPayload.status = ['POSTED', 'POSTED_LUKE', 'GCASH_AUTHORISED'];
  }
  if (typeof payload.filter.createdAt !== 'undefined') {
    validatedPayload.createdAt = payload.filter.createdAt;
  }
  return {
    filter: {
      ...validatedPayload,
    },
    pagination: payload.pagination,
  };
};
