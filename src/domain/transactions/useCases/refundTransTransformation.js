const transactionCodes = require('../statusCodes');

module.exports = (transaction, transactionState = null) => {
  const {
    paymentId,
    accountNumber,
    channelName,
    timestamp,
    postedTimestamp,
    amountValue,
    status,
    refundAmount,
    refundReason,
    refundApprovalStatus,
    acquirementId,
    refundId,
    mobileNumber,
    refundApprovedTimestamp,
    requestTimeStamp,
    refundRejectedTimestamp,
    isAutoRefund,
    billType,
    paymentMethod,
    refundStatus,
    finalAmount,
    convenienceFee,
    hasConvenienceFee,
    createDateTime,
    transactionId,
  } = transaction;

  // Retrieve Status
  const paymentStatusCodes = Object.keys(transactionCodes);
  let paymentStatus;
  if (paymentStatusCodes.includes(status)) {
    paymentStatus = transactionCodes[status];
  } else {
    paymentStatus = status;
  }

  //
  let refundType;
  if (isAutoRefund) {
    refundType = 'Auto';
  } else if (refundApprovalStatus === 'Approved') {
    refundType = 'Webtool';
  }

  // map payment method display name
  // currently only xendit refund uses "paymentMethod" on refund reports
  const getPaymentMethodDisplayName = (value) => {
    const displayName = {
      // ewallet
      shopeepay: 'Shopee Pay',
      grabpay: 'Grabpay',
      paymaya: 'Maya',
      // direct debit banks
      bpi: 'BPI',
      ubp: 'Unionbank',
      rcbc: 'RCBC',
      // credit card
      cc_dc: 'Online Credit Card',
    };

    if (value) {
      const mappedValue = Object.keys(displayName).find((name) => value === name);
      if (!mappedValue) {
        return value;
      }
      return displayName[mappedValue];
    }

    return value;
  };

  const transformedRefundData = {
    paymentId,
    accountNumber,
    channelName,
    timestamp,
    postedTimestamp: postedTimestamp ?? createDateTime,
    amountValue,
    status: paymentStatus,
    refundAmount,
    refundReason,
    refundApprovalStatus,
    gcashTransId: acquirementId,
    refundId,
    mobileNumber,
    refundDate: refundRejectedTimestamp || refundApprovedTimestamp,
    refundRejectedTimestamp,
    requestTimeStamp,
    refundType,
    billType,
    paymentMethod: getPaymentMethodDisplayName(paymentMethod),
    refundStatus,
    finalAmount,
    convenienceFee,
    hasConvenienceFee,
    createDateTime,
    transactionId,
  };

  return transformedRefundData;
};
