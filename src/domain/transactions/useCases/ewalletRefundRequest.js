const types = require('../../types');

module.exports = (payload) => {
  const {
    paymentId,
    accountNumber,
    channelId,
    channelName,
    createdAt,
    postedTimestamp,
    amountValue,
    paymentStatus,
    refundReason,
    refundAmount,
    refundApprovalStatus,
    mobileNumber,
    paymentGateway,
    transactionId,
    billType,
    accountType,
    fundingSource,
    emailAddress,
    totalAmount,
    convenienceFee,
    convenienceFeeInfo,
  } = payload;
  // Deconstruct payload for type coersions
  let hasConvenienceFee = false;
  if (convenienceFeeInfo?.property !== undefined || typeof convenienceFee !== 'undefined') {
    hasConvenienceFee = convenienceFee > 0 ? true : false;
  }

  const refundApprovalPayload = new types.RefundForApproval({
    paymentId,
    accountNumber,
    channelId,
    channelName,
    timestamp: createdAt,
    postedTimestamp,
    amountValue,
    status: paymentStatus,
    refundReason,
    refundAmount,
    refundApprovalStatus,
    mobileNumber,
    paymentGateway,
    transactionId,
    billType,
    accountType,
    fundingSource,
    emailAddress,
    totalAmount,
    convenienceFee,
    hasConvenienceFee,
  });
  // Validate Input Params
  const { valid, errors } = refundApprovalPayload.validate();
  if (!valid) {
    const error = new Error('Request Refund Error');
    error.message = errors;
    throw error;
  }
  return refundApprovalPayload.toJSON();
};
