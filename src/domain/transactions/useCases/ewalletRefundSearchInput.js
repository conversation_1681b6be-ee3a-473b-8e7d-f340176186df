const config = require('../../../../config');
const { formatDateForQuery } = require('../../../infra/utils/formatter');
const types = require('../../types');

module.exports = (payload) => {
  // Deconstruct payload for type coersions
  const { filter } = payload;
  const refundStatusMapping = {
    PartialRefund: 'Partial Refund',
    FullRefund: 'Full Refund',
    ForApproval: 'For Approval',
    Processing: 'Processing',
    Rejected: 'Rejected',
    Declined: 'Declined',
    Requested: 'REQUESTED',
    ForRequest: 'FOR_REQUEST',
  };

  filter.refundStatus = refundStatusMapping[filter.refundStatus];

  const ewalletRefundInputPayload = new types.EWalletRefundInput(filter);
  // Validate Input Params
  const { valid, errors } = ewalletRefundInputPayload.validate();
  if (!valid) {
    const error = new Error('EWallet Refund Filter Input Error');
    error.message = errors;
    throw error;
  }
  const validatedPayload = ewalletRefundInputPayload.toJSON();
  if (!validatedPayload.status) {
    const parsedStatus = JSON.parse(config.paymentGateway.REFUND_REQUEST_EXPECTED_STATUSES);
    validatedPayload.status = parsedStatus;
  }

  if (filter.createDateTime) {
    validatedPayload.createDateTime = Object.keys(filter.createDateTime).reduce((newObj, key) => {
      newObj[key] = formatDateForQuery(filter.createDateTime[key]);
      return newObj;
    }, {});
  }

  if (payload.filter.refundStatus) {
    validatedPayload.refundStatus = payload.filter.refundStatus;
  }

  return {
    filter: {
      ...validatedPayload,
    },
    pagination: payload.pagination,
  };
};
