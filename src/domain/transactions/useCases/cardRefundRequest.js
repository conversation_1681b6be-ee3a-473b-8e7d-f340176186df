const types = require('../../types');

module.exports = (payload) => {
  const {
    reference,
    accountNumber,
    channelId,
    channelName,
    createdAt,
    postedTimestamp,
    amountValue,
    paymentStatus,
    refundReason,
    refundAmount,
    refundApprovalStatus,
    mobileNumber,
    paymentGateway,
    transactionId,
    billType,
    accountType,
    fundingSource,
    emailAddress,
    finalAmount,
  } = payload;
  // Deconstruct payload for type coersions

  const refundApprovalPayload = new types.RefundForApproval({
    id: reference,
    accountNumber,
    channelId,
    channelName,
    timestamp: createdAt,
    postedTimestamp,
    amountValue,
    status: paymentStatus,
    refundReason,
    refundAmount,
    refundApprovalStatus,
    mobileNumber,
    paymentGateway,
    transactionId,
    billType,
    accountType,
    fundingSource,
    emailAddress,
    finalAmount,
  });
  // Validate Input Params
  const { valid, errors } = refundApprovalPayload.validate();
  if (!valid) {
    const error = new Error('Request Refund Error');
    error.message = errors;
    throw error;
  }
  return refundApprovalPayload.toJSON();
};
