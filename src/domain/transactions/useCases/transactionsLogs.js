const types = require('../../types');
const accountTypes = require('../accountTypes');
const transactionCodes = require('../statusCodes');
const pmMapping = require('../paymentMethod');

module.exports = (transaction) => {
  const data = transaction;
  const state = JSON.parse(transaction.state);
  const {
    cardIssuingBank,
    emailAddress,
    merchantCompany,
    mobileNumber,
    authCode,
    pspReference,
    amountValue,
    cardBankCode,
    cardHolderName,
    cardIssuingCountry,
    withholdingTax,
    netAmount,
    mid,
    threeDAuthenticated,
    postPaymentReferenceId,
    depositoryBankAccount,
    depositoryBankName,
    bankDiscount,
    splitPaymentReferenceId,
    paymentInfo,
    paymentType,
    fromBatchFile,
    costCenter,
    postedTimestamp,
    refusalReasonRaw,
    postPaymentEsbMessageId,
    postPaymentReason,
    paymentLinkId,
    loadType,
    authorisedTimestamp,
    isThreeDFlag,
    transactionType,
    finalAmount,
    convenienceFee,
  } = state;
  let creditCardNumber, prodDesc, creditCardType;
  let payModeCode = 'OCC';
  let payModeDesc = 'Online Credit Card';
  let { paymentMethod } = state;
  let splitPayment = false;
  let dateOfPostedAuthorised = postedTimestamp;
  if (splitPaymentReferenceId) {
    splitPayment = true;
  }
  if (state.cardSummary) {
    creditCardNumber = `**** **** **** ${state.cardSummary.substr(-4)}`;
  }

  // Payment Method & Card Type
  if (!paymentMethod) {
    paymentMethod = data.paymentMethod;
  }

  const exists = Object.keys(pmMapping).includes(paymentMethod);
  if (exists) {
    paymentMethod = pmMapping[paymentMethod];
  } else {
    paymentMethod = pmMapping.occ;
  }

  /**
   * Xendit invoice (pay by link) api - doesn't have credit card details response
   * BAU credit card type (CREDIT) as default
   */
  const isXenditPayByLink = data.gatewayProcessor === 'xendit' && paymentInfo.type === 'PAY_BY_LINK';
  const payByLinkDD = ['dd_bpi', 'dd_rcbc', 'dd_ubp'];

  if (paymentMethod.toLowerCase() === 'mc') {
    creditCardType = 'Master Card';
  } else if (paymentMethod.toLowerCase() === 'visa') {
    creditCardType = 'Visa';
  } else if (isXenditPayByLink && data.paymentMethod === 'cc_dc') {
    creditCardType = 'CREDIT';
  } else if (isXenditPayByLink && payByLinkDD.includes(data.paymentMethod)) {
    creditCardType = 'DEBIT';
  } else if (isXenditPayByLink) {
    creditCardType = 'N/A'; // e-wallets
  } else if (data.gatewayProcessor === 'xendit') {
    creditCardType = data.fundingSource;
  } else {
    creditCardType = paymentMethod;
  }

  // Account Type
  if (state.accountType) {
    prodDesc = accountTypes[state.accountType];
  }
  if (data.gatewayProcessor === 'gcash') {
    payModeCode = 'GCT';
    payModeDesc = 'GCash';
  }
  // Retrieve Status
  const status = Object.keys(transactionCodes);

  let transactionStatus;
  if (status.includes(data.status)) {
    transactionStatus = transactionCodes[data.status];
  } else {
    transactionStatus = data.status;
  }
  const transactionTimestamp = new Date(transaction.timestamp);
  const recordDate = transactionTimestamp.toLocaleString('en-US', { timeZone: 'Asia/Manila' });

  let creditCardBank = cardIssuingBank;
  if (typeof creditCardBank !== 'undefined') {
    creditCardBank = cardIssuingBank.replace(/,/g, '');
    creditCardBank = creditCardBank.replace(/\+/g, ' ');
  }
  let creditCardCountry = cardIssuingCountry;
  if (typeof creditCardCountry !== 'undefined') {
    creditCardCountry = cardIssuingCountry.replace(/\+/g, ' ');
  }
  let creditCardHolderName = cardHolderName;
  if (typeof creditCardHolderName !== 'undefined') {
    creditCardHolderName = cardHolderName.replace(/\+/g, ' ');
  }
  let ipAddress;
  if (paymentInfo.browserInfo) {
    if (typeof paymentInfo.browserInfo.ipAddress !== 'undefined') {
      ipAddress = state.paymentInfo.browserInfo.ipAddress.replace(/,/g, '');
    }
  }

  if (!data.postedTimestamp) {
    dateOfPostedAuthorised = authorisedTimestamp;
  }

  let transactionPaymentType = paymentType;
  if (paymentType === 'ADA') {
    transactionPaymentType = 'Auto Debit';
  }

  let refundStatus, refundAmount, refundDate, refundId;
  let isRefundable = true;
  if (
    ['Approved', 'For Approval', 'Processing', 'Rejected', 'Declined'].includes(data.refundApprovalStatus) &&
    data.refundApprovalStatus
  ) {
    ({ refundStatus, refundAmount, refundId } = data);
    isRefundable = false;
  }

  if (refundId) {
    if (refundId.includes('NULL')) {
      refundId = '';
    }
  }

  if (data.refundApprovalStatus === 'Approved') {
    refundDate = data.refundApprovedTimestamp;
  }

  let billType = 'NonBill';
  if (transactionType === 'G') {
    billType = 'Bill';
  }

  // Validate Transaction Data
  const result = new types.Transactions({
    reference: data.id,
    accountNumber: data.accountNumber,
    creditCardCountry,
    creditCardType,
    creditCardHolderName,
    creditCardBank,
    creditCardNumber,
    creditCardBankCode: cardBankCode,
    ipAddress,
    channelName: data.channelName,
    paymentMethod,
    status: transactionStatus,
    amountValue,
    amountCurrency: paymentInfo.amountCurrency,
    createdAt: data.timestamp,
    recordDate: recordDate.replace(/,/g, ''),
    grossAmount: amountValue,
    withholdingtax: withholdingTax,
    bankDiscount,
    netAmount,
    mid,
    depositoryBank: depositoryBankName,
    depositoryBankAccountNo: depositoryBankAccount,
    transId: pspReference,
    prodDesc,
    authCode,
    mobileNumber,
    merchantName: data.channelName,
    postPaymentReferenceId,
    threeDFlag: threeDAuthenticated,
    payModeCode,
    payModeDesc,
    merchantCompany,
    emailAddress,
    paymentGateway: data.gatewayProcessor,
    splitPayment,
    paymentType: transactionPaymentType,
    billType,
    fundingSource: data.fundingSource,
    fromBatchFile: fromBatchFile === undefined ? false : fromBatchFile,
    costCenter,
    channelReference: data.channelReference,
    accountType: prodDesc,
    postedTimestamp: dateOfPostedAuthorised,
    billerName: data.billerName,
    subMerchantId: data.subMerchantId,
    refusalReasonRaw,
    postPaymentReason,
    postPaymentEsbMessageId,
    orderReference: pspReference,
    paymentLinkId,
    description: paymentInfo.productDescription,
    isOrTransaction: data.isOrTransaction,
    loadType,
    orStatus: data.orStatus,
    lukeOrTimestamp: data.lukeOrTimestamp,
    refundAmount,
    refundReason: data.refundReason,
    channelId: data.channelId,
    refundApprovalStatus: data.refundApprovalStatus,
    paymentStatus: data.status,
    transactionId: data.transactionId,
    acquirementId: paymentInfo.acquirementId,
    timestamp: data.timestamp,
    refundRejectedTimestamp: data.refundRejectedTimestamp,
    refundStatus,
    installmentPaymentId: data.installmentPaymentId,
    installmentTerm: data.installmentTerm,
    itemPurchased: paymentInfo.productDescription,
    customerContactNumber: paymentInfo.customerContactNumber,
    isThreeDFlag,
    refundId,
    isRefundable,
    refundDate,
    transactionType,
    budgetProtectValue: paymentInfo?.miscellaneous?.budgetProtectValue || '',
    isBinding: data.isBinding || false,
    bindingRequestID: data.bindingRequestID || '',
    finalAmount,
    convenienceFee,
  });
  return result.toJSON();
};
