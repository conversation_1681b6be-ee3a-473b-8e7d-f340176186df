const types = require('../../types');

module.exports = (payload) => {
  const {
    reference,
    accountNumber,
    channelId,
    channelName,
    createdAt,
    postedTimestamp,
    amountValue,
    paymentStatus,
    refundReason,
    refundAmount,
    refundApprovalStatus,
    requestTimeStamp,
    acquirementId,
    mobileNumber,
    paymentGateway,
    transactionId,
    billType,
    accountType,
    fundingSource,
    finalAmount,
    convenienceFee,
    convenienceFeeInfo,
  } = payload;

  let hasConvenienceFee = false;
  if (convenienceFeeInfo?.property !== undefined || typeof convenienceFee !== 'undefined') {
    hasConvenienceFee = convenienceFee > 0 ? true : false;
  }

  // Deconstruct payload for type coersions
  const refundApprovalPayload = new types.RefundForApproval({
    id: reference,
    accountNumber,
    channelId,
    channelName,
    timestamp: createdAt,
    postedTimestamp,
    amountValue,
    status: paymentStatus,
    refundReason,
    refundAmount,
    refundApprovalStatus,
    requestTimeStamp,
    acquirementId,
    mobileNumber,
    paymentGateway,
    transactionId,
    billType,
    accountType,
    fundingSource,
    finalAmount,
    convenienceFee,
    hasConvenienceFee,
  });
  // Validate Input Params
  const { valid, errors } = refundApprovalPayload.validate();
  if (!valid) {
    const error = new Error('Request Refund Error');
    error.message = errors;
    throw error;
  }
  return refundApprovalPayload.toJSON();
};
