const types = require('../../types');
const transactionCodes = require('../statusCodes');

module.exports = (transaction) => {
  const data = transaction;
  const state = JSON.parse(transaction.state);
  const {
    amountValue,
    postedTimestamp,
    authorisedTimestamp,
    paymentInfo,
    mobileNumber,
    finalAmount,
    convenienceFee,
    transactionType,
  } = state;

  const {
    id,
    accountNumber,
    channelName,
    timestamp,
    refundAmount,
    refundReason,
    channelId,
    refundApprovalStatus,
    status,
    refundRejectedTimestamp,
    refundStatus,
    refundId,
    isAutoRefund,
    gatewayProcessor,
    transactionId,
    refundValidity,
  } = data;

  let billType = 'NonBill';
  if (transactionType === 'G') {
    billType = 'Bill';
  }

  // Retrieve Status
  const trasactionStatusCodes = Object.keys(transactionCodes);

  // Transform Status Code to Report Status Format
  let transactionStatus;
  if (trasactionStatusCodes.includes(status)) {
    transactionStatus = transactionCodes[status];
  } else {
    transactionStatus = status;
  }

  let dateOfPostedAuthorised = postedTimestamp;
  if (!data.postedTimestamp) {
    dateOfPostedAuthorised = authorisedTimestamp;
  }

  // Refund Type if Webtool or Auto Refund
  let refundType;
  if (isAutoRefund) {
    refundType = 'Auto';
  } else if (refundApprovalStatus === 'Approved') {
    refundType = 'Webtool';
  }

  let isRefundable = true;
  const differeneeInTime = new Date().getTime() - new Date(timestamp).getTime();
  const differenceInDays = differeneeInTime / (1000 * 3600 * 24);
  if (parseInt(differenceInDays.toFixed(2), 10) >= parseInt(refundValidity, 10)) {
    isRefundable = false;
  }

  // Validate Transaction Data
  const result = new types.Transactions({
    status: transactionStatus,
    amountValue,
    postedTimestamp: dateOfPostedAuthorised,
    reference: id,
    accountNumber,
    channelName,
    createdAt: timestamp,
    refundAmount,
    refundReason,
    channelId,
    refundApprovalStatus,
    paymentStatus: status,
    timestamp,
    refundRejectedTimestamp,
    refundStatus,
    refundId,
    refundType,
    paymentGateway: gatewayProcessor,
    transactionId,
    acquirementId: paymentInfo.acquirementId,
    mobileNumber,
    isRefundable,
    finalAmount,
    billType,
    convenienceFee,
  });
  return result.toJSON();
};
