const types = require('../../types');

module.exports = (payload) => {
  // Deconstruct payload for type coersions
  const { filter } = payload;
  const refundStatusMapping = {
    PartialRefund: 'Partial Refund',
    FullRefund: 'Full Refund',
    ForApproval: 'For Approval',
    Processing: 'Processing',
  };
  filter.refundStatus = refundStatusMapping[filter.refundStatus];
  const cardRefundInputPayload = new types.CardRefundInput(filter);
  // Validate Input Params
  const { valid, errors } = cardRefundInputPayload.validate();
  if (!valid) {
    const error = new Error('Card Refund Filter Input Error');
    error.message = errors;
    throw error;
  }
  const validatedPayload = cardRefundInputPayload.toJSON();
  if (!validatedPayload.status) {
    validatedPayload.status = ['POSTED', 'POSTED_LUKE', 'ADYEN_AUTHORISED'];
  }
  if (payload.filter.createdAt) {
    validatedPayload.createdAt = payload.filter.createdAt;
  }
  return {
    filter: {
      ...validatedPayload,
    },
    pagination: payload.pagination,
  };
};
