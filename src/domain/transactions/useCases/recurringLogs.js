const accountTypes = require('../accountTypes');

module.exports = (transaction) => {
  const {
    id,
    accountNumber,
    isActive,
    channelName,
    transId,
    enrollmentType,
    ccNumber,
    ccType,
    bankName,
    tokenId,
    amount,
    status,
    reason,
    entity,
    currency,
    userContact,
    userEmail,
    timestamp,
    productDescription,
    price,
  } = transaction;

  return {
    pspReferenceNumber: id,
    accountNumber,
    isActive,
    channelName,
    transId,
    enrollmentType,
    ccNumber,
    ccType,
    bankName,
    tokenId,
    amount,
    status,
    reason,
    entity: accountTypes[entity],
    currency,
    userContact,
    userEmail,
    timestamp,
    productDescription,
    price,
  };
};
