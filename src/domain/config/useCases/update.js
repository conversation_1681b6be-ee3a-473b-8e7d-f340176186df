const types = require('../../types');

module.exports = (payload) => {
  // Deconstruct payload for type coersions
  const configPayload = new types.Config(payload);

  // Validate Input Params
  const { valid, errors } = configPayload.validate();
  if (!valid) {
    const error = new Error('ConfigValidationError');
    error.message = errors;
    throw error;
  }

  return configPayload.toJSON();
};
