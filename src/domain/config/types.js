const { attributes } = require('structure');

const SubMerchant = attributes({
  serviceType: {
    type: String,
    regex: /^[^=,@,+,-]/,
    minLength: 1,
    maxLength: 32,
  },
  subMerchantId: {
    type: String,
    regex: /^[^=,@,+,-]/,
    minLength: 1,
    maxLength: 32,
  },
  merchant: {
    type: String,
  },
})(class SubMerchant {});

const GcashRefundReason = attributes({
  reason: {
    type: String,
    minLength: 3,
    maxLength: 50,
    regex: /^[^=,@,+,-]/,
  },
})(class GcashRefundReason {});

const CardRefundReason = attributes({
  reason: {
    type: String,
    minLength: 3,
    maxLength: 50,
    regex: /^[^=,@,+,-]/,
  },
})(class CardRefundReason {});

const ORPaymentType = attributes({
  description: {
    type: String,
    minLength: 3,
    maxLength: 25,
    regex: /^[^=,@,+,-]/,
  },
  name: {
    type: String,
    minLength: 3,
    maxLength: 15,
    regex: /^[^=,@,+,-]/,
  },
  or: {
    type: String,
    minLength: 3,
    maxLength: 15,
    regex: /^[^=,@,+,-]/,
  },
  orVat: {
    type: String,
    minLength: 3,
    maxLength: 25,
    regex: /^[^=,@,+,-]/,
  },
})(class ORPaymentType {});

const EmailAddress = attributes({
  email: {
    type: String,
    email: true,
    maxLength: 250,
    required: true,
  },
})(class EmailAddress {});

const SwipeORPaymentType = attributes({
  description: {
    type: String,
    minLength: 3,
    maxLength: 50,
    regex: /^[^=,@,+,-]/,
  },
  name: {
    type: String,
    minLength: 3,
    maxLength: 25,
    regex: /^[^=,@,+,-]/,
  },
  or: {
    type: String,
    minLength: 3,
    maxLength: 25,
    regex: /^[^=,@,+,-]/,
  },
  orVat: {
    type: String,
    minLength: 3,
    maxLength: 25,
    regex: /^[^=,@,+,-]/,
  },
})(class SwipeORPaymentType {});

const ConfigIpWhitelistCallbackStructure = attributes({
  callback: {
    type: Array,
    itemType: String,
    unique: true,
  },
})(class ConfigIpWhitelistCallbackStructure {});

const ConfigIpWhitelistStructure = attributes({
  xendit: {
    type: ConfigIpWhitelistCallbackStructure,
  },
})(class ConfigIpWhitelistStructure {});

const Config = {
  Config: attributes({
    refreshTime: {
      type: String,
    },
    paymentServiceApiMaintenance: {
      type: Boolean,
    },
    globeEmailNotification: {
      type: Boolean,
    },
    globeEmailNotificationPatternId: {
      type: String,
      maxLength: 5,
    },
    innoveEmailNotification: {
      type: Boolean,
    },
    innoveEmailNotificationPatternId: {
      type: String,
      maxLength: 5,
    },
    bayanEmailNotification: {
      type: Boolean,
    },
    bayanEmailNotificationPatternId: {
      type: String,
      maxLength: 5,
    },
    subMerchants: {
      type: Array,
      itemType: SubMerchant,
    },
    paymentMethodGcash: {
      type: String,
      equal: ['mynt'],
    },
    paymentMethodCard: {
      type: String,
      equal: ['adyen', 'ipay88'],
    },
    paymentMethodBank: {
      type: String,
      equal: ['bpi'],
    },
    amaxLoadConsumer: {
      type: String,
      maxLenth: 5,
      empty: true,
    },
    amaxLoadRetailer: {
      type: String,
      maxLenth: 5,
      empty: true,
    },
    gcashRefundRetries: {
      type: String,
    },
    refundReason: {
      type: Array,
      itemType: GcashRefundReason,
    },
    cardRefundReason: {
      type: Array,
      itemType: CardRefundReason,
    },
    PSORPaymentType: {
      type: Array,
      itemType: ORPaymentType,
    },
    swipeORPaymentType: {
      type: Array,
      itemType: SwipeORPaymentType,
    },
    dailyContentGCashReportPatternId: {
      required: true,
      type: String,
      maxLength: 5,
    },
    dailyContentGCashReportRecipient: {
      type: Array,
      itemType: EmailAddress,
      maxLength: 20,
    },
    monthlyContentGCashReportPatternId: {
      required: true,
      type: String,
      maxLength: 5,
    },
    monthlyContentGCashReportRecipient: {
      type: Array,
      itemType: EmailAddress,
      maxLength: 20,
    },
    ecpayReportEmailPatternId: {
      required: true,
      type: String,
      maxLength: 5,
    },
    ecpayReportRecipient: {
      type: Array,
      itemType: EmailAddress,
      maxLength: 20,
    },
    globeOneReportEmailPatternId: {
      required: true,
      type: String,
      maxLength: 5,
    },
    globeOneReportRecipient: {
      type: Array,
      itemType: EmailAddress,
      maxLength: 20,
    },
    collectionReportEmailPatternId: {
      required: true,
      type: String,
      maxLength: 5,
    },
    collectionReportRecipient: {
      type: Array,
      itemType: EmailAddress,
      maxLength: 20,
    },
    creditCardReportEmailPatternId: {
      required: true,
      type: String,
      maxLength: 5,
    },
    creditCardReportRecipient: {
      type: Array,
      itemType: EmailAddress,
      maxLength: 20,
    },
    channelReportEmailPatternId: {
      required: true,
      type: String,
      maxLength: 5,
    },
    channelReportRecipient: {
      type: Array,
      itemType: EmailAddress,
      maxLength: 20,
    },
    billingReportPatternId: {
      required: true,
      type: String,
      maxLength: 5,
    },
    billingReportRecipient: {
      type: Array,
      itemType: EmailAddress,
      maxLength: 20,
    },
    orGenerationConfig: {
      type: String,
      equal: ['luke', 'uni'],
    },
    configIpWhitelist: {
      type: ConfigIpWhitelistStructure,
    },
    otcCodeLimit: {
      type: Number,
      min: 1,
      max: 10,
    },
  })(class Config {}),
};

module.exports = Config;
