const { attributes } = require('structure');

const BillLinerConfig = {
  BillLinerConfig: attributes({
    content: {
      type: String,
      regex: /^(?!.*[0-9]-[0-9])[A-Za-z0-9]+(-[A-Za-z0-9]+)?$/,
      minLength: 1,
      maxLength: 50,
    },
    adyenMerchantAccount: {
      type: String,
      regex: /^(?!.*[0-9]-[0-9])[A-Za-z0-9]+(-[A-Za-z0-9]+)?$/,
      minLength: 1,
      maxLength: 50,
    },
    type: {
      type: String,
      default: 'billLinerMid',
    },
    name: {
      type: String,
      minLength: 4,
      maxLength: 100,
    },
    createdAt: {
      type: String,
      default: new Date().toISOString(),
    },
    updatedAt: {
      type: String,
      default: new Date().toISOString(),
    },
  })(class BillLinerConfig {}),
};

module.exports = BillLinerConfig;
