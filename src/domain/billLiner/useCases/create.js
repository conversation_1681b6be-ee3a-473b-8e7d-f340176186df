const types = require('../../types');

module.exports = (payload) => {
  // Deconstruct payload for type coersions
  const configPayload = new types.BillLinerConfig(payload);
  // Validate Input Params
  const { valid, errors } = configPayload.validate();
  if (!valid) {
    const error = new Error('BillLinerValidationError');
    error.message = errors;
    throw error;
  }

  return configPayload.toJSON();
};
