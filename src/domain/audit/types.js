const { attributes } = require('structure');

const Audit = {
  Audit: attributes({
    id: String,
    sortKey: {
      type: String,
      default: 'audit',
    },
    ipAddress: String,
    userId: String,
    roleId: String,
    roleName: String,
    userEmail: String,
    userName: String,
    userData: Object,
    oldValue: Object,
    newValue: Object,
    userAgent: String,
    category: String,
    reasonToDelete: String,
    reasonToUpdate: String,
    isViewed: {
      type: Boolean,
      default: false,
    },
    ttl: {
      type: Number,
      default: () => {
        const now = new Date();
        now.setMonth(now.getMonth() + 6);
        return Math.floor(now / 1000);
      },
    },
    createdAt: String,
    updatedAt: String,
  })(class Audit {}),
};

module.exports = Audit;
