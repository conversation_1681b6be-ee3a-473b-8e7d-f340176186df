const { attributes } = require('structure');

const Reports = {
  Vouchers: attributes({
    psReferenceNo: {
      type: String,
      nullable: true,
      empty: true,
    },
    gcashReferenceNo: {
      type: String,
      nullable: true,
      empty: true,
    },
    accountNo: {
      type: String,
      nullable: true,
      empty: true,
    },
    srn: {
      type: String,
      nullable: true,
      empty: true,
    },
    channelName: {
      type: String,
      nullable: true,
      empty: true,
    },
    channelId: {
      type: String,
      nullable: true,
      empty: true,
    },
    paymentMethod: {
      type: String,
      nullable: true,
      empty: true,
    },
    paymentStatus: {
      type: String,
      nullable: true,
      empty: true,
    },
    amount: {
      type: Number,
      nullable: true,
      default: 0,
    },
    currency: {
      type: String,
      nullable: true,
      empty: true,
    },
    date: {
      type: String,
      nullable: true,
      empty: true,
    },
    msisdn: {
      type: String,
      nullable: true,
      empty: true,
    },
    productDescription: {
      type: String,
      nullable: true,
      empty: true,
    },
    emailAddress: {
      type: String,
      nullable: true,
      empty: true,
    },
    paymentGateway: {
      type: String,
      nullable: true,
      empty: true,
    },
    customerSegment: {
      type: String,
      nullable: true,
      empty: true,
    },
    customerSubType: {
      type: String,
      nullable: true,
      empty: true,
    },
    brand: {
      type: String,
      nullable: true,
      empty: true,
    },
    entity: {
      type: String,
      nullable: true,
      empty: true,
    },
    sku: {
      type: String,
      nullable: true,
      empty: true,
    },
    modeOfPayment: {
      type: String,
      nullable: true,
      empty: true,
    },
    subscriberType: {
      type: String,
      nullable: true,
      empty: true,
    },
    contentPartnerShortName: {
      type: String,
      nullable: true,
      empty: true,
    },
    voucherDispenseStatus: {
      type: String,
      nullable: true,
      empty: true,
    },
    refundStatus: {
      type: String,
      nullable: true,
      empty: true,
    },
    refundAmount: {
      type: String,
      nullable: true,
      default: 0,
    },
  })(class Vouchers {}),
};

module.exports = Reports;
