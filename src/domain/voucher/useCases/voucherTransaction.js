const types = require('../../types');

module.exports = (payload) => {
  const {
    id,
    gcashReferenceNo,
    accountNo,
    srn,
    channelName,
    channelId,
    paymentMethod,
    paymentStatus,
    amount,
    currency,
    date,
    msisdn,
    productDescription,
    emailAddress,
    paymentGateway,
    customerSegment,
    customerSubType,
    brand,
    entity,
    sku,
    modeOfPayment,
    subscriberType,
    contentPartnerShortName,
    voucherDispenseStatus,
    refundStatus,
    refundAmount,
  } = payload;

  let pgateway = paymentGateway;
  if (pgateway === 'gcash') {
    pgateway = 'GCash';
  } else if (pgateway === 'ipay88') {
    pgateway = 'iPay88';
  } else if (pgateway === 'adyen') {
    pgateway = 'Adyen';
  }

  const newVoucherPayload = {
    psReferenceNo: id,
    gcashReferenceNo,
    accountNo,
    srn,
    channelName,
    channelId,
    paymentMethod,
    paymentStatus,
    amount: parseFloat(amount).toFixed(2),
    currency,
    date,
    msisdn,
    productDescription,
    emailAddress,
    paymentGateway: pgateway,
    customerSegment,
    customerSubType,
    brand,
    entity,
    sku,
    modeOfPayment,
    subscriberType,
    contentPartnerShortName,
    voucherDispenseStatus,
    refundStatus,
    refundAmount,
  };

  const voucherPayload = new types.Vouchers(newVoucherPayload);
  const { valid, errors } = voucherPayload.validate();

  if (!valid) {
    const error = new Error('RegistrationValidationError');
    error.message = errors;
    throw error;
  }

  return voucherPayload.toJSON();
};
