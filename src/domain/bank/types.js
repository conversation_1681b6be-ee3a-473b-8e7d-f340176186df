const { attributes } = require('structure');

const Bank = {
  Bank: attributes({
    name: {
      type: String,
      required: true,
      maxLength: 100,
      regex: /^[^=,@,+,-]/,
    },
    code: {
      type: String,
      required: true,
      maxLength: 10,
      regex: /^[^=,@,+,-]/,
    },
    gateway: {
      type: String,
      required: true,
      equal: ['adyen', 'ipay88', 'bpi'],
    },
    createdAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
    updatedAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
  })(class Bank {}),

  UpdateBank: attributes({
    name: {
      type: String,
      maxLength: 100,
    },
    code: {
      type: String,
      maxLength: 10,
    },
    gateway: {
      type: String,
      equal: ['adyen', 'ipay88', 'bpi'],
    },
    updatedAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
  })(class Bank {}),
};

module.exports = Bank;
