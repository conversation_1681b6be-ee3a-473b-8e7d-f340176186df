module.exports = (data) => {
  const { id, oldPayload, newPayload } = data;
  let updatedPayload = {};
  Object.keys(newPayload).forEach((key) => {
    if (key in oldPayload && Object.entries(oldPayload[key]).length !== 0) {
      Object.keys(newPayload[key]).forEach((item) => {
        if (key === 'payloadTransaction' && typeof oldPayload.payloadTransaction[item] !== 'undefined') {
          oldPayload.payloadTransaction[item].revenue += newPayload[key][item].revenue;
          oldPayload.payloadTransaction[item].transactions += newPayload[key][item].transactions;
        }
        if (key === 'payloadCMid' && typeof oldPayload.payloadTransaction[item] !== 'undefined') {
          oldPayload[key][item].transAmount += newPayload[key][item].transAmount;
          oldPayload[key][item].transCount += newPayload[key][item].transCount;
          oldPayload[key][item].bankCharge += newPayload[key][item].bankCharge;
        }
        if (key === 'payloadCCompany' && typeof oldPayload.payloadCCompany[item] !== 'undefined') {
          oldPayload[key][item].transAmount += newPayload[key][item].transAmount;
          oldPayload[key][item].transCount += newPayload[key][item].transCount;
          oldPayload[key][item].bankCharge += newPayload[key][item].bankCharge;
        }
        if (key === 'payloadCreditDebit' && typeof oldPayload.payloadCreditDebit[item] !== 'undefined') {
          oldPayload[key][item].transAmount += newPayload[key][item].transAmount;
          oldPayload[key][item].transCount += newPayload[key][item].transCount;
        }
        if (key === 'payloadCardBrand' && typeof oldPayload.payloadCardBrand[item] !== 'undefined') {
          oldPayload[key][item].transAmount += newPayload[key][item].transAmount;
          oldPayload[key][item].transCount += newPayload[key][item].transCount;
        }
        if (key === 'payloadGcash' && typeof oldPayload.payloadGcash[item] !== 'undefined') {
          oldPayload[key][item].transAmount += newPayload[key][item].transAmount;
          oldPayload[key][item].transCount += newPayload[key][item].transCount;
        }
        if (key === 'payloadChannelReport' && typeof oldPayload.payloadChannelReport[item] !== 'undefined') {
          oldPayload[key][item].transAmount += newPayload[key][item].transAmount;
          oldPayload[key][item].transCount += newPayload[key][item].transCount;
        } else {
          oldPayload[key][item] = newPayload[key][item];
        }
      });
    } else {
      oldPayload[key] = newPayload[key];
    }
  });

  updatedPayload = {
    id,
    payload: JSON.stringify(oldPayload),
    updatedAt: new Date().toISOString(),
  };

  return updatedPayload;
};
