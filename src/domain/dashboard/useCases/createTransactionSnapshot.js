const t = require('../types');

module.exports = (data) => {
  // Deconstruct payload for type coersions
  const createTransactionSnapshotPayload = new t.Snapshot({
    ...data,
    type: 'transactions',
  });
  // Validate Input Params
  const { valid, errors } = createTransactionSnapshotPayload.validate();
  if (!valid) {
    const error = new Error('TransactionSnapshotValidationError');
    error.message = errors;
    throw error;
  }
  return createTransactionSnapshotPayload.toJSON();
};
