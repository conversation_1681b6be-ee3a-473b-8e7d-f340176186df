const t = require('../types');

module.exports = (data) => {
  // Deconstruct payload for type coersions
  const createOverAllSnapshotPayload = new t.Snapshot({
    ...data,
    type: 'overall',
  });
  // Validate Input Params
  const { valid, errors } = createOverAllSnapshotPayload.validate();
  if (!valid) {
    const error = new Error('OverAllTransactionValidation');
    error.message = errors;
    throw error;
  }
  return createOverAllSnapshotPayload.toJSON();
};
