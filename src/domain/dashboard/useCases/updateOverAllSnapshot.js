const t = require('../types');

module.exports = (data) => {
  const { id, payload, month, year } = data;
  // Deconstruct payload for type coersions
  const updateOverAllSnapshotPayload = new t.Snapshot(data);
  // Validate Input Params
  const { valid, errors } = updateOverAllSnapshotPayload.validate();
  if (!valid) {
    const error = new Error('OverAllTransactionValidation');
    error.message = errors;
    throw error;
  }
  return {
    id,
    payload,
    month,
    year,
    updatedAt: new Date().toISOString(),
    type: 'overall',
  };
};
