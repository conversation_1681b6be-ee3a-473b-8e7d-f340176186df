const { attributes } = require('structure');

const Snapshot = {
  Snapshot: attributes({
    id: String,
    payload: Object,
    month: String,
    year: String,
    day: String,
    type: String,
    createdAt: {
      type: String,
      default: new Date().toISOString(),
    },
    updatedAt: {
      type: String,
      default: new Date().toISOString(),
    },
    ttl: {
      type: Number,
      default: () => {
        const now = new Date();
        now.setMonth(now.getMonth() + 6);
        return Math.floor(now / 1000);
      },
    },
  })(class Snapshot {}),
};

module.exports = Snapshot;
