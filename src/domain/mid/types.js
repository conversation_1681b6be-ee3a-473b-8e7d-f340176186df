const { attributes } = require('structure');

const Installment = attributes({
  bankMid: {
    type: String,
    maxLength: 50,
    minLength: 1,
    required: true,
    regex: /^[^=,@,+,-]/,
  },
  bankTerm: {
    type: Number,
    equal: [3, 6, 9, 12, 18, 24, 36],
    required: true,
  },
  bank: {
    type: String,
    min: 2,
    max: 20,
    required: true,
    regex: /^[^=,@,+,-]/,
  },
})(class Installment {});

const ADA = attributes({
  adaMid: {
    type: String,
    maxLength: 50,
    minLength: 1,
    required: true,
    regex: /^[^=,@,+,-]/,
  },
  enrollmentType: {
    type: String,
    equal: ['Straight', 'PreAuth'],
    required: true,
  },
})(class ADA {});

const MerchantIdDetails = {
  BillMid: attributes({
    name: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    merchantId: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    previousMerchantId: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    paymentType: {
      type: String,
      equal: ['AutoDebit', 'Straight', 'Advance'],
    },
    depositoryBankName: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    depositoryBankAccount: {
      type: String,
      regex: /^[0-9.-]+$/,
      maxLength: 50,
    },
    company: {
      type: String,
      required: true,
      equal: ['Globe', 'Innove', 'Bayan'],
    },
    billType: {
      type: String,
      equal: ['Bill'],
    },
    channelId: {
      type: String,
      maxLength: 50,
    },
    bankDiscount: {
      type: String,
      maxLength: 5,
    },
    withholdingTax: {
      type: String,
      maxLength: 5,
    },
    costCenter: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    businessUnit: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    updatedAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
    createdAt: {
      type: String,
    },
    ada: {
      type: Array,
      itemType: ADA,
    },
    bank: {
      type: String,
      empty: true,
      default: '',
    },
    bankTerm: {
      type: String,
      empty: true,
      default: '',
    },
    enrollmentType: {
      type: String,
      empty: true,
      default: '',
    },
  })(class BillMid {}),

  UpdateBillStraight: attributes({
    id: {
      type: String,
      maxLength: 50,
    },
    name: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    merchantId: {
      type: String,
      required: true,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    previousMerchantId: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    paymentType: {
      type: String,
      equal: ['Straight'],
    },
    depositoryBankName: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    depositoryBankAccount: {
      type: String,
      regex: /^[0-9.-]+$/,
      maxLength: 50,
    },
    company: {
      type: String,
      required: true,
      equal: ['Globe', 'Innove', 'Bayan'],
    },
    billType: {
      type: String,
      equal: ['Bill'],
    },
    channelId: {
      type: String,
      maxLength: 50,
    },
    bankDiscount: {
      type: String,
      maxLength: 5,
    },
    withholdingTax: {
      type: String,
      maxLength: 5,
    },
    costCenter: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    businessUnit: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    updatedAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
    createdAt: {
      type: String,
    },
    ada: {
      type: Array,
      itemType: ADA,
    },
    bank: {
      type: String,
      empty: true,
      default: '',
    },
    bankTerm: {
      type: String,
      empty: true,
      default: '',
    },
    enrollmentType: {
      type: String,
      empty: true,
      default: '',
    },
  })(class UpdateBillStraight {}),

  NonBillMid: attributes({
    channelId: {
      type: String,
      maxLength: 50,
      required: true,
    },
    name: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    merchantId: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    previousMerchantId: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    paymentType: {
      type: String,
      equal: ['Straight', 'Installment'],
    },
    depositoryBankName: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    depositoryBankAccount: {
      type: String,
      regex: /^[0-9.-]+$/,
      maxLength: 50,
    },
    company: {
      type: String,
      equal: ['Globe', 'Innove', 'Bayan'],
    },
    billType: {
      type: String,
      equal: ['NonBill'],
    },
    bankDiscount: {
      type: String,
      maxLength: 5,
    },
    withholdingTax: {
      type: String,
      maxLength: 5,
    },
    updatedAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
    createdAt: {
      type: String,
    },
    costCenter: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    businessUnit: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    installments: {
      type: Array,
      itemType: Installment,
    },
  })(class NonBillMid {}),

  MIdInstallment: attributes({
    id: {
      type: String,
      maxLength: 50,
    },
    name: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    merchantId: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    previousMerchantId: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    paymentType: {
      type: String,
      equal: ['Installment'],
    },
    depositoryBankName: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    depositoryBankAccount: {
      type: String,
      regex: /^[0-9.-]+$/,
      maxLength: 50,
    },
    company: {
      type: String,
      equal: ['Globe', 'Innove', 'Bayan'],
    },
    billType: {
      type: String,
      equal: ['NonBill'],
    },
    channelId: {
      type: String,
      maxLength: 50,
    },
    bankDiscount: {
      type: String,
      maxLength: 5,
    },
    withholdingTax: {
      type: String,
      maxLength: 5,
    },
    costCenter: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    businessUnit: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    updatedAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
    createdAt: {
      type: String,
    },
    bankTerm: {
      type: Number,
      equal: [3, 6, 9, 12, 18, 24, 36],
    },
  })(class MIdInstallment {}),

  MerchantIdPrimaryInput: attributes({
    id: {
      type: String,
      maxLength: 50,
    },
  })(class MerchantIdPrimaryInput {}),

  UpdateNonBillStraight: attributes({
    id: {
      type: String,
      maxLength: 50,
    },
    channelId: {
      type: String,
      maxLength: 50,
      required: true,
    },
    name: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    merchantId: {
      type: String,
      required: true,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    previousMerchantId: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    paymentType: {
      type: String,
      equal: ['Straight', 'Installment'],
    },
    depositoryBankName: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    depositoryBankAccount: {
      type: String,
      regex: /^[0-9.-]+$/,
      maxLength: 50,
    },
    company: {
      type: String,
      equal: ['Globe', 'Innove', 'Bayan'],
    },
    billType: {
      type: String,
      equal: ['NonBill'],
    },
    bankDiscount: {
      type: String,
      maxLength: 5,
    },
    withholdingTax: {
      type: String,
      maxLength: 5,
    },
    updatedAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
    createdAt: {
      type: String,
    },
    costCenter: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    businessUnit: {
      type: String,
      maxLength: 50,
      regex: /^[^=,@,+,-]/,
    },
    installments: {
      type: Array,
      itemType: Installment,
    },
  })(class UpdateNonBillStraight {}),
};

module.exports = MerchantIdDetails;
