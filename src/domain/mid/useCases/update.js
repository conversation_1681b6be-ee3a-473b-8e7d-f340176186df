/* eslint no-param-reassign: ["error", { "props": false }] */
const types = require('../../types');

module.exports = (payload) => {
  let midPayload;
  const { billType, merchantId, previousMerchantId, paymentType } = payload;

  if (merchantId && !previousMerchantId) {
    payload.previousMerchantId = merchantId;
  }

  if (billType === 'Bill' && paymentType === 'Straight') {
    midPayload = new types.UpdateBillStraight(payload);
  } else if (billType === 'Bill') {
    midPayload = new types.BillMid(payload);
  } else if (billType === 'NonBill' && paymentType === 'Straight') {
    midPayload = new types.UpdateNonBillStraight(payload);
  } else if (billType === 'NonBill') {
    midPayload = new types.NonBillMid(payload);
  } else {
    throw new Error('Bill Type and Payment Type must be indicate');
  }

  // Validate Input Params
  const { valid, errors } = midPayload.validate();
  if (!valid) {
    const error = new Error('MidUpdateError');
    error.message = errors;
    throw error;
  }

  return midPayload.toJSON();
};
