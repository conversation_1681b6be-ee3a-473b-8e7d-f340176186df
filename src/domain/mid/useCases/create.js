const types = require('../../types');

module.exports = (payload) => {
  let midPayload;
  const { billType, paymentType } = payload;
  if (billType === 'Bill' && paymentType === 'Straight') {
    midPayload = new types.UpdateBillStraight({
      createdAt: new Date().toISOString(),
      ...payload,
    });
  } else if (billType === 'Bill') {
    midPayload = new types.BillMid({
      createdAt: new Date().toISOString(),
      ...payload,
    });
  } else if (billType === 'NonBill' && paymentType === 'Straight') {
    midPayload = new types.UpdateNonBillStraight({
      createdAt: new Date().toISOString(),
      ...payload,
    });
  } else if (billType === 'NonBill') {
    midPayload = new types.NonBillMid({
      createdAt: new Date().toISOString(),
      ...payload,
    });
  } else {
    throw new Error('Bill Type and Payment Type must be indicate');
  }
  // Validate Input Params
  const { valid, errors } = midPayload.validate();

  if (!valid) {
    const error = new Error('MidCreationError');
    error.message = errors;
    throw error;
  }

  return midPayload.toJSON();
};
