const types = require('../../types');

module.exports = (payload) => {
  const validatePayload = new types.CreateInstallment(payload);
  // Validate Input Params
  const { valid, errors } = validatePayload.validate();

  if (!valid) {
    const error = new Error('Installment Merchant Id Creation Error');
    error.message = errors;
    throw error;
  }

  const result = validatePayload.toJSON();
  result.term = result.term.toString();
  result.paymentId = result.paymentId.toString();
  return result;
};
