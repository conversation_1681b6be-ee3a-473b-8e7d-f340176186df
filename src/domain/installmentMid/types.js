const { attributes } = require('structure');

const InstallmentMid = {
  CreateInstallment: attributes({
    bank: {
      type: String,
      min: 2,
      max: 20,
      required: true,
    },
    term: {
      type: Number,
      equal: [3, 6, 9, 12, 18, 24, 36],
      required: true,
    },
    paymentId: {
      type: Number,
      postive: true,
      min: 1,
      max: 999,
      required: true,
    },
    createdAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
    updatedAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
  })(class CreateInstallment {}),

  DownloadInstallment: attributes({
    bank: {
      type: String,
    },
    term: {
      type: Number,
    },
    merchantId: {
      type: String,
    },
    paymentId: {
      type: Number,
    },
    createdAt: {
      type: String,
    },
    updatedAt: {
      type: String,
    },
  })(class DownloadInstallment {}),
};

module.exports = InstallmentMid;
