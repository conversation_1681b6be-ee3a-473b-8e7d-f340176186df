const { attributes } = require('structure');

const Channel = {
  Channel: attributes({
    id: String,
    name: {
      type: String,
      maxLength: 32,
      required: true,
      regex: /^[^=,@,+,-]/,
    },
    channelCode: {
      type: String,
      maxLength: 5,
      required: true,
      regex: /^[^=,@,+,-]/,
    },
    callbackUrl: {
      type: String,
      maxLength: 25000,
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    isEnabled: {
      type: Boolean,
      default: false,
    },
    email: {
      type: String,
      email: true,
    },
    ipAddress: {
      type: String,
      maxLength: 64,
      regex: /\b((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.|$)){4}\b/,
    },
    clientSecret: String,
    clientId: String,
    emailNotif: {
      type: Boolean,
      default: true,
    },
    smsNotif: {
      type: Boolean,
      default: true,
    },
    enablePaymentSession: {
      type: Boolean,
      default: true,
    },
    createDateTime: String,
    updateDateTime: String,
    merchantCode: {
      type: String,
      maxLength: 250,
      nullable: true,
    },
    merchantKey: {
      type: String,
      maxLength: 250,
      nullable: true,
    },
    xApiKey: {
      type: String,
      maxLength: 250,
      nullable: true,
    },
    cardPaymentMethod: {
      type: String,
      default: 'off',
      equal: ['off', 'adyen', 'ipay88', 'xendit'],
    },
    gcashPaymentMethod: {
      type: String,
      default: 'mynt',
      equal: ['mynt'],
    },
    bankPaymentMethod: {
      type: String,
      default: 'off',
      equal: ['off', 'bpi'],
    },
    ewalletPaymentMethod: {
      type: String,
      default: 'xendit',
      equal: ['xendit'],
    },
    bpiEnabled: {
      type: Boolean,
    },
    ipay88Enabled: {
      type: Boolean,
    },
    gcashEnabled: {
      type: Boolean,
    },
    adyenEnabled: {
      type: Boolean,
    },
    globeEmailNotification: {
      type: Boolean,
    },
    globeEmailNotificationPatternId: {
      type: String,
      nullable: true,
      empty: true,
      maxLength: 5,
    },
    innoveEmailNotification: {
      type: Boolean,
    },
    innoveEmailNotificationPatternId: {
      type: String,
      nullable: true,
      empty: true,
      maxLength: 5,
    },
    bayanEmailNotification: {
      type: Boolean,
    },
    bayanEmailNotificationPatternId: {
      type: String,
      nullable: true,
      empty: true,
      maxLength: 5,
    },
    failedEmailNotification: {
      type: Boolean,
    },
    billType: {
      type: String,
      equal: ['Bill', 'NonBill', 'Both'],
    },
    serviceType: {
      type: String,
    },
    enableCardHolderName: {
      type: Boolean,
    },
    isCallbackEncrypted: {
      type: Boolean,
      default: false,
    },
    callbackPassPhrase: {
      type: String,
    },
    isOrEnabled: {
      type: Boolean,
      default: false,
    },
    gcreditSubMerchantId: {
      type: String,
    },
    isForPayByLink: {
      type: Boolean,
    },
    isSecureConnection: {
      type: Boolean,
      default: true,
    },
    gateways: {
      type: Object,
      schema: {
        xendit: {
          type: Object,
          schema: {
            cards: {
              type: Array,
              schema: [String],
            },
            otc: {
              type: Array,
              schema: [String],
            },
          },
        },
      },
    },
  })(class Channel {}),
  UpdateChannel: attributes({
    id: String,
    name: {
      type: String,
      maxLength: 32,
      regex: /^[^=,@,+,-]/,
    },
    channelCode: {
      type: String,
      maxLength: 5,
      regex: /^[^=,@,+,-]/,
    },
    callbackUrl: {
      type: String,
      maxLength: 2500,
      minLength: 10,
    },
    isVerified: Boolean,
    isEnabled: Boolean,
    email: {
      type: String,
      email: true,
    },
    emailNotif: Boolean,
    smsNotif: Boolean,
    ipAddress: {
      type: String,
      regex: /\b((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.|$)){4}\b/,
    },
    // cannot be updated
    // clientSecret: String,
    // clientId: String,
    enablePaymentSession: Boolean,
    createDateTime: String,
    updateDateTime: String,
    merchantCode: {
      type: String,
      empty: true,
    },
    merchantKey: {
      type: String,
      empty: true,
    },
    xApiKey: {
      type: String,
      empty: true,
    },
    paymentGateway: {
      type: String,
      empty: true,
      equal: ['adyen', 'ipay88'],
    },
    ipay88Enabled: {
      type: Boolean,
    },
    gcashEnabled: {
      type: Boolean,
    },
    adyenEnabled: {
      type: Boolean,
    },
    globeEmailNotification: {
      type: Boolean,
    },
    globeEmailNotificationPatternId: {
      type: String,
      nullable: true,
      empty: true,
    },
    innoveEmailNotification: {
      type: Boolean,
    },
    innoveEmailNotificationPatternId: {
      type: String,
      nullable: true,
      empty: true,
    },
    bayanEmailNotification: {
      type: Boolean,
    },
    bayanEmailNotificationPatternId: {
      type: String,
      nullable: true,
      empty: true,
    },
    failedEmailNotification: {
      type: Boolean,
    },
    billType: {
      type: String,
      equal: ['Bill', 'NonBill', 'Both'],
    },
    serviceType: {
      type: String,
      nullable: true,
      empty: true,
    },
    enableCardHolderName: {
      type: Boolean,
    },
    isCallbackEncrypted: {
      type: Boolean,
    },
    callbackPassPhrase: {
      type: String,
    },
    cardHolderNameType: {
      type: String,
      equal: ['OPTIONAL', 'REQUIRED'],
    },
    cardPaymentMethod: {
      type: String,
      nullable: true,
      equal: ['off', 'adyen', 'ipay88'],
    },
    bankPaymentMethod: {
      type: String,
      nullable: true,
      equal: ['off', 'bpi'],
    },
    ewalletPaymentMethod: {
      type: String,
      nullable: true,
      equal: ['xendit'],
    },
    bpiEnabled: {
      type: Boolean,
    },
    ecpayServiceType: {
      type: String,
      nullable: true,
      empty: true,
    },
    isOrEnabled: {
      type: Boolean,
    },
    gcreditSubMerchantId: {
      type: String,
      min: 1,
      maxLength: 25,
      regex: /^[0-9]*$/,
    },
    isForPayByLink: {
      type: Boolean,
    },
    gcashOneClickEnabled: {
      type: Boolean,
    },
    gcashOneClickMerchantId: {
      type: String,
      empty: true,
    },
    gcashOneClickClientId: {
      type: String,
      empty: true,
    },
    gcashOneClickClientSecret: {
      type: String,
      empty: true,
    },
    gcashOneClickProductCode: {
      type: String,
      empty: true,
    },
    gcashOneClickRedirectUrl: {
      type: String,
      empty: true,
    },
    gcashOneClickValidity: {
      type: Number,
      empty: true,
    },
    gcashOneClickBindingIdPrefix: {
      type: String,
      length: 3,
      regex: /^[a-z]+$/,
      empty: true,
    },
    bindCallbackUrl: {
      type: String,
      maxLength: 2500,
      minLength: 10,
      regex: /.*\{bindingRequestId\}.*/,
      empty: true,
    },
    isSecureConnection: {
      type: Boolean,
    },
    gateways: {
      type: Object,
      schema: {
        xendit: {
          type: Object,
          schema: {
            card_straight: {
              type: Array,
              schema: [String],
            },
            card_installment: {
              type: Array,
              schema: [String],
            },
            otc: {
              type: Array,
              schema: [String],
            },
          },
        },
      },
    },
  })(class Channel {}),
};

module.exports = Channel;
