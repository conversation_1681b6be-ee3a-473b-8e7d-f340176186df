const types = require('../../types');

module.exports = (payload) => {
  // Deconstruct payload for type coersions
  const channelPayload = new types.UpdateChannel(payload);

  // Validate Input Params
  const { valid, errors } = channelPayload.validate();

  if (!valid) {
    const error = new Error('UpdateValidationError');
    error.message = JSON.stringify(errors);
    throw error;
  }

  return channelPayload.toJSON();
};
