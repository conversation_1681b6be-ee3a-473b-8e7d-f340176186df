const types = require('../../types');

module.exports = (payload) => {
  // Deconstruct payload for type coersions
  const channelPayload = new types.Channel(payload);

  // Validate Input Params
  const { valid, errors } = channelPayload.validate();

  if (!valid) {
    const error = new Error('RegistrationValidationError');
    error.details = errors;
    throw error;
  }

  return channelPayload.toJSON();
};
