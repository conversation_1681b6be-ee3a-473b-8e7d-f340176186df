class Permissions {
  constructor() {
    this.whitelist = [
      'login',
      'updateNotificationStatus',
      'validateToken',
      'rolesLoose',
      'verifyChannel',
      'channelsLoose',
      'notifications',
      'notifIsNotViewCount',
      'searchNotifications',
      'transactionCount',
      'configs',
      'subMerchants',
      'refundReason',
      'channelSubMerchant',
      'refundApprovalModuleHistory',
      'cardRefundApprovalModuleHistory',
      'channelsBillType',
      'refundValidity',
      'listInstallmentBank',
      'listPSORPaymentType',
      'payByLinkChannels',
    ];
    this.user = null;
    this.action = null;
    this.keyPermission = null;
    this.permission = null;

    this.viewAction = {
      Dashboard: [
        'transactions',
        'onlineCCGCash',
        'revenuePerChannel',
        'notifications',
        'transactionsPerChannel',
        'transactionsPercentage',
        'gatewayStatus',
        'performance',
        'adyen',
        'userMgmt',
      ],
      User: ['user', 'users'],
      Role: ['role', 'roles'],
      Channel: ['channel', 'channels'],
      Mid: ['mid', 'mids'],
      Provider: ['providers'],
      Bank: ['bank', 'banks'],
      Audit: ['audit', 'audits'],
      Transaction: ['reports'],
      Failed: ['failedReports'],
      Billing: ['billingReports'],
      Gateway: ['gatewayTransactionReports'],
      Collection: ['gatewayCollectionSummary'],
      Wireline: ['revenueWireline'],
      Treasury: ['treasuryYTD', 'monthlyTreasury', 'treasuryReports'],
      MonthlyGenerated: ['monthlyReports'],
      Archive: ['paybillReferenceCodes', 'paybillTransactionLogs', 'archiveTransactionLogs', 'archiveAuditLogs'],
      LukeBatchFile: ['batchfiles'],
      ChannelReport: ['channelReports'],
      GotsReport: ['gotsReports'],
      ECPay: ['ecpayReports'],
      GlobeOne: ['globeOneReport'],
      PayByLink: ['payByLink'],
      LoadORReport: ['loadORReport'],
      ContentGcashReport: ['contentGcashReport'],
      ContentFraudReport: ['contentFraudReport'],
      GcashRefundRequest: ['refundRequestModule'],
      GcashRefundApproval: ['refundApprovalModule'],
      GcashRefundDetailedReport: ['gcashRefundDetailedReport'],
      GcashRefundSummaryReport: ['gcashRefundSummaryReport'],
      InstallmentReport: ['installmentReport'],
      Installmentmid: ['installmentMid'],
      ADADeclinedReport: ['adaDeclinedDetailedReport'],
      ADASummaryReport: ['adaSummaryReport'],
      CardRefundRequest: ['cardRefundRequestModule'],
      CardRefundApproval: ['cardRefundApprovalModule'],
      EndGameReport: ['endGameTransactionReport'],
      CardRefundDetailedReport: ['cardRefundDetailedReport'],
      CardRefundSummaryReport: ['cardRefundSummaryReport'],
      DropinSimulator: ['channelDropInSimulator'],
      BillLinerConfig: ['listBillLinerConfig'],
      XenditRefundRequest: ['xenditRefundRequest'],
      XenditRefundApproval: ['xenditRefundApproval'],
      XenditRefundDetailedReport: ['xenditRefundDetailedReport'],
      XenditRefundSummaryReport: ['xenditRefundSummaryReport'],
      PostPaymentConfig: ['postPaymentConfig'],
      PayByLinkModule: ['payByLinkModule'],
      PayByLinkReport: ['payByLinkReport'],
      GCashBindingReport: ['gCashBindingReport'],
      ConvenienceFee: ['convenienceFee', 'convenienceFees'],
      ConvenienceFeeBrand: ['convenienceFeeBrand'],
    };

    this.actions = ['view', 'create', 'update', 'delete', 'export', 'import', 'deactivate'];

    this.reportType = {
      transaction: 'Transaction',
      failed: 'Failed',
      billing: 'Billing',
      creditcard: 'Gateway',
      collection: 'Collection',
      rawireline: 'Wireline',
      gatewaycc: 'MonthlyGenerated',
      collectionmid: 'MonthlyGenerated',
      collectioncompany: 'MonthlyGenerated',
      treasury: 'Treasury',
      revenuewirelesspaytype: 'MonthlyGenerated',
      revenuewirelesspaymode: 'MonthlyGenerated',
      lukebatch: 'LukeBatchFile',
      channelreport: 'ChannelReport',
      gots: 'GotsReport',
      ecpay: 'ECPay',
      globeone: 'GlobeOne',
      paybylink: 'PayByLink',
      loadorreport: 'LoadORReport',
      contentgcashreport: 'ContentGcashReport',
      contentfraudreport: 'ContentFraudReport',
      gcashrefunddetailedreport: 'GcashRefundDetailedReport',
      gcashrefundsummaryreport: 'GcashRefundSummaryReport',
      installmentreport: 'InstallmentReport',
      adadeclinedreport: 'ADADeclinedReport',
      adasummaryreport: 'ADASummaryReport',
      endgamereport: 'EndGameReport',
      cardrefunddetailedreport: 'CardRefundDetailedReport',
      cardrefundsummaryreport: 'CardRefundSummaryReport',
      xenditrefunddetailedreport: 'XenditRefundDetailedReport',
      xenditrefundsummaryreport: 'XenditRefundSummaryReport',
      paybylinkreport: 'PayByLinkReport',
      gCashBindingReport: 'GCashBindingReport',
    };

    this.userImport = ['UsersStatusUpdate'];
  }

  check(data) {
    const { user, action, args } = data;

    this.user = user;
    this.action = action;
    this.payload = args;

    // Check if in Whitelist
    if (!this.whitelist.includes(this.action)) {
      // Check if User Authenticated
      if (this.user === null) {
        // Unauthorized
        return false;
      }
      return this.matchPermission();
    }
    // Authorized by Whitelisted
    return true;
  }

  actionHasUpperase() {
    return /[A-Z]/.test(this.action);
  }

  matchPermission() {
    this.checkPermission();
    if (this.permission === null || !this.actions.includes(this.permission)) {
      return true;
    }
    const rolePermission = this.user.role.permissions;
    if (Object.prototype.hasOwnProperty.call(rolePermission, this.keyPermission)) {
      return rolePermission[this.keyPermission].includes(this.permission);
    }
    return false;
  }

  checkPermission() {
    Object.entries(this.viewAction).forEach((entry) => {
      const key = entry[0];
      const value = entry[1];
      if (value.includes(this.action)) {
        this.permission = 'view';
        this.keyPermission = key;
      }
    });
    if (this.permission === null) {
      [this.permission, this.keyPermission] = this.action.split(/(?=[A-Z])/);
      this.keyPermission = this.action.substring(this.permission.length, this.action.length);
    }
    if (this.permission === 'download') {
      if (this.keyPermission === 'S3Reports') {
        this.keyPermission = 'MonthlyGenerated';
      } else if (this.keyPermission === 'S3LukeBatchFiles') {
        this.keyPermission = 'LukeBatchFile';
      } else if (this.keyPermission === 'Reports') {
        this.keyPermission = this.reportType[this.payload.data.type];
      } else {
        this.permission = 'export';
        if (this.keyPermission.slice(-1) === 's') {
          this.keyPermission = this.keyPermission.slice(0, -1);
        }
      }
    }
    if (this.permission === 'upload') {
      this.permission = 'import';
      if (this.userImport.includes(this.keyPermission)) {
        this.keyPermission = 'User';
      } else {
        this.keyPermission = this.keyPermission.slice(0, -1);
      }
    }
    if (this.permission === 'delete') {
      if (this.keyPermission.slice(-1) === 's') {
        this.keyPermission = this.keyPermission.slice(0, -1);
      }
    }
  }
}

module.exports = Permissions;
