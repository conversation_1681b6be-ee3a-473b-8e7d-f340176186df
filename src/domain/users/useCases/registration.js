const types = require('../../types');

module.exports = (payload) => {
  // Deconstruct payload for type coersions
  const userPayload = new types.CreateUser(payload);

  // Validate Input Params
  const { valid, errors } = userPayload.validate();
  if (!valid) {
    const error = new Error('RegistrationValidationError');
    error.message = JSON.stringify(errors);
    throw error;
  }
  return userPayload.toJSON();
};
