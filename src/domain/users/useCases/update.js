const types = require('../../types');

module.exports = (payload) => {
  // Deconstruct payload for type coersions
  const userPayload = new types.UpdateUser(payload);

  // Validate Input Params
  const { valid, errors } = userPayload.validate();
  if (!valid) {
    const error = new Error('UpdateValidationError');
    error.message = JSON.stringify(errors);
    throw error;
  }

  return userPayload.toJSON();
};
