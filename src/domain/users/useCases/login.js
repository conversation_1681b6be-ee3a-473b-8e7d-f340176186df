const t = require('../../types');

const allowedDomains = ['stratpoint.com', 'globe.com', 'globe.com.ph', 'gsupport.com.ph'];

module.exports = (payload) => {
  const LoginPayload = new t.<PERSON>gin(payload);
  const { idToken, email, hostedDomain } = LoginPayload;

  // Validate Input Params
  const { valid, errors } = LoginPayload.validate();

  if (!valid) {
    const error = new Error('LoginValidationError');
    error.message = JSON.stringify(errors);
    throw error;
  }

  if (allowedDomains.indexOf(hostedDomain) === -1) {
    const error = new Error('DomainHostedNotAllowedError');
    error.message = 'Email used does not belong to the organization.';
    throw error;
  }

  return {
    idToken,
    email,
    hostedDomain,
  };
};
