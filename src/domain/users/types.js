const { attributes } = require('structure');

const User = {
  CreateUser: attributes({
    id: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      maxLength: 100,
      required: true,
      regex: /^[^=,@,+,-]/,
    },
    email: {
      type: String,
      email: true,
      maxLength: 250,
      required: true,
    },
    roleId: {
      type: String,
      required: true,
      maxLength: 50,
    },
    mobileNumber: {
      type: String,
      maxLength: 10,
      required: true,
    },
    group: {
      type: String,
      maxLength: 250,
      required: true,
      regex: /^[^=,@,+,-]/,
    },
    division: {
      type: String,
      maxLength: 250,
      required: true,
      regex: /^[^=,@,+,-]/,
    },
    department: {
      type: String,
      maxLength: 250,
      required: true,
      regex: /^[^=,@,+,-]/,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    emailNotif: {
      type: Boolean,
      default: true,
    },
    smsNotif: {
      type: Boolean,
      default: true,
    },
    isActiveAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
    createdAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
    updatedAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
  })(class CreateUser {}),

  UpdateUser: attributes({
    id: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      maxLength: 100,
      regex: /^[^=,@,+,-]/,
    },
    email: {
      type: String,
      email: true,
      maxLength: 320,
    },
    roleId: {
      type: String,
      maxLength: 50,
    },
    mobileNumber: {
      type: String,
      maxLength: 10,
    },
    group: {
      type: String,
      maxLength: 250,
      regex: /^[^=,@,+,-]/,
    },
    division: {
      type: String,
      maxLength: 250,
      regex: /^[^=,@,+,-]/,
    },
    department: {
      type: String,
      maxLength: 250,
      regex: /^[^=,@,+,-]/,
    },
    emailNotif: Boolean,
    smsNotif: Boolean,
    loginTime: String,
    isActive: Boolean,
    reasonToDeactivate: String,
    reasonToDelete: String,
    isActiveAt: String,
    createdAt: String,
    updatedAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
    assignedChannels: {
      type: Array,
      itemType: String,
      maxLength: 50,
      sparse: false,
      unique: true,
    },
    cardAssignedChannels: {
      type: Array,
      itemType: String,
      maxLength: 50,
      sparse: false,
      unique: true,
    },
    ewalletAssignedChannels: {
      type: Array,
      itemType: String,
      maxLength: 50,
      sparse: false,
      unique: true,
    },
    postPaymentConfigChannels: {
      type: Array,
      itemType: String,
      maxLength: 50,
      sparse: false,
      unique: true,
    },
    billType: {
      type: String,
      equal: ['Bill', 'NonBill', 'Both'],
      required: true,
    },
  })(class UpdateUser {}),

  UpdateUserStatus: attributes({
    email: {
      type: String,
      email: true,
      maxLength: 320,
      required: true,
    },
    status: {
      type: String,
      equal: ['inactive', 'active'],
      required: true,
    },
  })(class UpdateUserStatus {}),
};

module.exports = User;
