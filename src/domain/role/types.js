const { attributes } = require('structure');

const Permissions = attributes({
  Dashboard: {
    type: Array,
    itemType: String,
    default: [],
  },
  User: {
    type: Array,
    itemType: String,
    default: [],
  },
  Role: {
    type: Array,
    itemType: String,
    default: [],
  },
  Channel: {
    type: Array,
    itemType: String,
    default: [],
  },
  Mid: {
    type: Array,
    itemType: String,
    default: [],
  },
  Provider: {
    type: Array,
    itemType: String,
    default: [],
  },
  Bank: {
    type: Array,
    itemType: String,
    default: [],
  },
  Transaction: {
    type: Array,
    itemType: String,
    default: [],
  },
  Failed: {
    type: Array,
    itemType: String,
    default: [],
  },
  Billing: {
    type: Array,
    itemType: String,
    default: [],
  },
  Gateway: {
    type: Array,
    itemType: String,
    default: [],
  },
  Collection: {
    type: Array,
    itemType: String,
    default: [],
  },
  Wireline: {
    type: Array,
    itemType: String,
    default: [],
  },
  MonthlyGenerated: {
    type: Array,
    itemType: String,
    default: [],
  },
  Audit: {
    type: Array,
    itemType: String,
    default: [],
  },
  Archive: {
    type: Array,
    itemType: String,
    default: [],
  },
  Config: {
    type: Array,
    itemType: String,
    default: [],
  },
  Treasury: {
    type: Array,
    itemType: String,
    default: [],
  },
  LukeBatchFile: {
    type: Array,
    itemType: String,
    default: [],
  },
  ChannelReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  GotsReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  ECPay: {
    type: Array,
    itemType: String,
    default: [],
  },
  GlobeOne: {
    type: Array,
    itemType: String,
    default: [],
  },
  PayByLink: {
    type: Array,
    itemType: String,
    default: [],
  },
  LoadORReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  ContentGcashReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  ContentFraudReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  GcashRefundRequest: {
    type: Array,
    itemType: String,
    default: [],
  },
  GcashRefundApproval: {
    type: Array,
    itemType: String,
    default: [],
  },
  GcashRefundDetailedReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  GcashRefundSummaryReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  InstallmentReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  InstallmentMid: {
    type: Array,
    itemType: String,
    default: [],
  },
  ADADeclinedReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  ADASummaryReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  CardRefundRequest: {
    type: Array,
    itemType: String,
    default: [],
  },
  CardRefundApproval: {
    type: Array,
    itemType: String,
    default: [],
  },
  EndGameReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  CardRefundDetailedReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  CardRefundSummaryReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  DropinSimulator: {
    type: Array,
    itemType: String,
    default: [],
  },
  BillLinerConfig: {
    type: Array,
    itemType: String,
    default: [],
  },
  XenditRefundRequest: {
    type: Array,
    itemType: String,
    default: [],
  },
  XenditRefundApproval: {
    type: Array,
    itemType: String,
    default: [],
  },
  XenditRefundDetailedReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  XenditRefundSummaryReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  PostPaymentConfig: {
    type: Array,
    itemType: String,
    default: [],
  },
  PayByLinkModule: {
    type: Array,
    itemType: String,
    default: [],
  },
  PayByLinkReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  GCashBindingReport: {
    type: Array,
    itemType: String,
    default: [],
  },
  ConvenienceFee: {
    type: Array,
    itemType: String,
    default: [],
  },
  ConvenienceFeeBrand: {
    type: Array,
    itemType: String,
    default: [],
  },
})(class Permissions {});

const Role = {
  Role: attributes({
    id: String,
    isActive: {
      type: Boolean,
      default: true,
    },
    code: {
      type: String,
      maxLength: 100,
      required: true,
      regex: /^[^=,@,+,-]/,
    },
    permissions: {
      type: Permissions,
      default: new Permissions(),
    },
    name: {
      type: String,
      maxLength: 100,
      required: true,
      regex: /^[^=,@,+,-]/,
    },
    notes: {
      type: String,
      maxLength: 250,
      empty: true,
    },
    emailNotif: {
      type: Boolean,
      default: true,
    },
    smsNotif: {
      type: Boolean,
      default: true,
    },
    createdAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
    updatedAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
  })(class Role {}),

  UpdateRole: attributes({
    id: String,
    isActive: Boolean,
    code: {
      type: String,
      maxLength: 100,
      regex: /^[^=,@,+,-]/,
    },
    permissions: {
      type: Permissions,
    },
    name: {
      type: String,
      maxLength: 100,
      regex: /^[^=,@,+,-]/,
    },
    notes: {
      type: String,
      maxLength: 250,
      empty: true,
    },
    emailNotif: Boolean,
    smsNotif: Boolean,
    updatedAt: {
      type: String,
      default: () => new Date().toISOString(),
    },
  })(class UpdateRole {}),
};

module.exports = Role;
