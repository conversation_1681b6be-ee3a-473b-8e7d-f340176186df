const XENDIT_URL = process.env.XENDIT_URL;
const XENDIT_SECRET_KEY = process.env.XENDIT_SECRET_KEY;

class XenditApiClient {
  constructor(baseURL, secretKey) {
    this.baseURL = baseURL;
    this.authorizationToken = btoa(secretKey + ':');
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;

    const config = {
      ...options,
      headers: {
        Authorization: `Basic ${this.authorizationToken}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}

module.exports = new XenditApiClient(XENDIT_URL, XENDIT_SECRET_KEY);
