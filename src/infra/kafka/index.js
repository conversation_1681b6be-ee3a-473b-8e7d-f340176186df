const kafka = require('node-rdkafka');
const { SchemaRegistry } = require('@kafkajs/confluent-schema-registry');
const { transformOptions } = require('../utils/formatter');

class Kafka {
  constructor({ config, logger }) {
    this.brokers = config.kafka.brokers;
    this.log = logger;
    this.maxRetries = 3;
    this.retryInterval = 15000;
    this.retryCount = 0;
    this.isRetrying = false;

    const opts = transformOptions(config);
    this.log.info(`Kafka Config: ${JSON.stringify(opts)}`);
    this.producer = new kafka.Producer(opts.globalConfig, opts.topicConfig || {});

    if (Object.keys(opts.schemaRegistry).length > 0) {
      this.registry = new SchemaRegistry(opts.schemaRegistry);
    }

    this.setupEventHandlers();
    this.connectProducer();
  }

  setupEventHandlers() {
    this.producer.on('ready', () => {
      this.log.info('Producer instance ready.');
      this.retryCount = 0; // Reset retry count on successful connection
      this.isRetrying = false;
    });

    this.producer.on('event.error', (err) => {
      this.log.warn('Producer error: ', JSON.stringify(err));
    });

    this.producer.on('event.log', (_log) => {
      const { fac, message } = _log;
      this.log.info(`${fac}: ${message}`);
    });

    this.producer.on('connection.failure', () => {
      this.log.warn('Kafka Connection Failure');
      this.handleRetry();
    });

    this.producer.setPollInterval(100);
  }

  connectProducer() {
    this.producer.connect();
  }

  handleRetry() {
    if (this.retryCount < this.maxRetries && !this.isRetrying) {
      this.isRetrying = true;

      ++this.retryCount;

      this.log.error(`Unable to connect to kafka brokers: ${this.brokers}`);
      this.log.error(`Retrying connection attempt [${this.retryCount} out of ${this.maxRetries}]`);

      // Wait for 15 seconds before the next connection attempt
      setTimeout(() => {
        this.isRetrying = false; // Reset the retrying flag after timeout
        this.connectProducer(); // Attempt to reconnect
      }, this.retryInterval);
    } else if (this.retryCount >= this.maxRetries) {
      throw new Error(
        `[CONNECTION ERROR] Could not connect to Kafka broker ${this.brokers} after ${this.maxRetries} attempts.`
      );
    }
  }

  async encodeMessage(data, topic, schema) {
    if (!schema) {
      return Buffer.from(this.convertDataToString(data));
    }

    const { version = 'latest' } = schema;
    const { mode = 2 } = schema;
    const suffix = mode === 2 ? 'value' : 'key';

    const subject = `${topic}-${suffix}`;
    // Use a particular version of the schema for encoding
    const id = await this.registry.getRegistryId(subject, version);
    return await this.registry.encode(id, data);
  }

  convertDataToString(data) {
    return typeof data === 'object' ? JSON.stringify(data) : data.toString();
  }

  async produce(body) {
    const { payload, topic, headers, schema } = body;

    const date = new Date().toLocaleString('en-US', { timeZone: 'Asia/Manila' });
    const timestamp = new Date(date).getTime();
    try {
      const serializedMessage = this.registry
        ? await this.encodeMessage(payload, topic, schema)
        : Buffer.from(this.convertDataToString(payload));

      // The DR event handler is put here to resolve the report after produce()
      const report = await new Promise((resolve, reject) => {
        this.producer.once('delivery-report', (err, report) => {
          if (err) {
            this.log.error('Error producing message:', err);
            err.headers = headers;
            return reject(err);
          }
          this.log.info('Message sent successfully.');
          report.headers = headers;
          const res = {
            code: 'SUCCESS',
            'Delivery Report': report,
          };
          resolve(res);
        });

        // Send the message
        this.producer.produce(topic, null, serializedMessage, null, timestamp, undefined, headers);
      });

      this.log.debug(`Delivery Report: ${report}`);
      return report;
    } catch (error) {
      this.log.error(error.toString());
      throw {
        code: 'ERROR',
        error,
      };
    }
  }

  stop(done) {
    this.log.debug('Stopping Producer instance.');
    this.producer.disconnect(done);
    return;
  }
}

module.exports = Kafka;
