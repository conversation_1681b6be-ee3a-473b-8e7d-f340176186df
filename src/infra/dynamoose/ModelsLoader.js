const fs = require('fs');
const path = require('path');
const inflection = require('inflection');
const { asValue } = require('awilix');

const singularizeToUpper = (str) => inflection.singularize(str.replace(/^./, (f) => f.toUpperCase()));

module.exports = {
  load({ dynamoose, baseFolder, indexFile = 'index.js' }) {
    const loaded = {
      models: {},
    };

    fs.readdirSync(baseFolder)
      .filter((file) => file.indexOf('.') !== 0 && file !== indexFile && file.slice(-3) === '.js')
      .forEach((file) => {
        try {
          const model = require(path.join(baseFolder, file))(dynamoose);
          model.table();
          const modelName = `${singularizeToUpper(file.split('.')[0])}Model`;
          loaded.models[modelName] = asValue(model);
        } catch (error) {
          console.error(`Error loading model ${file}: ${error.message}`);
        }
      });
    return loaded;
  },
};
