const fs = require('fs');
const path = require('path');
const inflection = require('inflection');
const { asValue } = require('awilix');

const singularizeToUpper = (str) => inflection.singularize(str.replace(/^./, (f) => f.toUpperCase()));

module.exports = {
  load({ dynamoose, modelsFolder, schemaFolder, indexFile = 'index.js' }) {
    const loaded = {
      models: {},
      schemas: {},
    };

    fs.readdirSync(modelsFolder)
      .filter((file) => file.indexOf('.') !== 0 && file !== indexFile && file.slice(-3) === '.js')
      .forEach((file) => {
        // TODO: move all models to use schema param
        // try catch on schema loading to support legacy models
        let schema;
        try {
          // schema expects same filename as the model
          schema = require(path.join(schemaFolder, file))(dynamoose);
        } catch (error) {
          console.error(`[WARN] Error loading schema ${file} - Will use legacy implementation`);
        }
        try {
          let model;
          if (schema) {
            model = require(path.join(modelsFolder, file))(dynamoose, schema);
          } else {
            // legacy implementation
            model = require(path.join(modelsFolder, file))(dynamoose);
          }

          model.table();
          const modelName = `${singularizeToUpper(file.split('.')[0])}Model`;
          loaded.models[modelName] = asValue(model);
          if (schema) {
            const schemaName = `${singularizeToUpper(file.split('.')[0])}Schema`;
            loaded.schemas[schemaName] = asValue(schema);
          }
        } catch (error) {
          console.error(`Error loading model ${file}: ${error.message}`);
        }
      });
    return loaded;
  },
};
