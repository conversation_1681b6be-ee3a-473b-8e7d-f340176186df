module.exports = (dynamoose) => {
  return new dynamoose.Schema({
    paymentId: {
      type: String,
      hashKey: true,
    },
    createDateTime: {
      type: String,
      rangeKey: true,
    },
    customerId: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'CustomerIdIndex',
        project: true,
      },
    },
    status: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'StatusIndex',
        project: true,
      },
    },
    sessionId: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'SessionIdIndex',
        project: true,
      },
    },
    channelId: {
      type: String,
      required: true,
    },
    customerName: {
      type: String,
    },
    gatewayProcessor: {
      type: String,
      required: true,
    },
    paymentMethod: {
      type: String,
      required: true,
    },
    settlementBreakdown: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            accountId: String,
            accountType: String,
            amount: Number,
            amountValue: Number,
            brand: String,
            email: String,
            mobileNumber: Number,
            msisdn: Number,
            transactionType: String,
          },
        },
      ],
    },
    type: {
      type: String,
    },
    totalAmount: {
      type: Number,
      required: true,
    },
    transactionExpiry: {
      type: String,
    },
    updateDateTime: {
      type: String,
    },
    installment: {
      type: Object,
      schema: {
        count: Number,
        interval: String,
      },
    },
    refundId: {
      type: String,
    },
    convenienceFeeAmount: {
      type: Number,
    },
  });
};
