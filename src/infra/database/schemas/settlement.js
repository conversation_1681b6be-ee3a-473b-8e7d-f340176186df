module.exports = (dynamoose) => {
  return new dynamoose.Schema({
    paymentId: {
      type: String,
      hashKey: true,
    },
    transactionId: {
      type: String,
      rangeKey: true,
    },
    accountId: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'AccountIdIndex',
        project: true,
      },
    },
    email: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'EmailIndex',
        project: true,
      },
    },
    mobile: {
      type: Number,
      required: true,
      index: {
        global: true,
        name: 'MobileIndex',
        project: true,
      },
    },
    mobileNumber: {
      type: [String, Number],
    },
    msisdn: {
      type: [String, Number],
    },
    transactionType: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'TransactionTypeIndex',
        project: true,
        rangeKey: 'createDateTime',
      },
    },
    brand: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'BrandIndex',
        project: true,
      },
    },
    createDateTime: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'CreateDateTimeIndex',
        project: true,
      },
    },
    updateDateTime: {
      type: String,
    },
    amount: {
      type: Number,
      required: true,
    },
    status: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'StatusIndex',
        project: true,
        rangeKey: 'createDateTime',
      },
    },
    postPaymentReason: {
      type: String,
    },
    postPaymentEsbMessageId: {
      type: String,
    },
    amountCurrency: {
      type: String,
    },
    paymentMethod: {
      type: String,
    },
    channelId: {
      type: String,
    },
    refundId: {
      type: String,
    },
  });
};
