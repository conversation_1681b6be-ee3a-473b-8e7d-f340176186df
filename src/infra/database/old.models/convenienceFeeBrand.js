module.exports = (dynamoose) => {
  const tableName = `pgw${process.env.STAGE}-convenience-fee-brands`;
  const convenienceFeeBrandSchema = new dynamoose.Schema({
    id: {
      type: String,
      hashKey: true,
    },
    name: {
      type: String,
      required: true,
    },
    createdAt: {
      type: String,
      required: true,
    },
    updatedAt: {
      type: String,
      required: true,
    },
  });

  return dynamoose.model(tableName, convenienceFeeBrandSchema, { throughput: 'ON_DEMAND' });
};
