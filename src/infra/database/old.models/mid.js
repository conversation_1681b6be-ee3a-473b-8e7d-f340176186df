module.exports = (dynamoose) => {
  const tableName = `pgw${process.env.STAGE}-mids`;
  const midSchema = new dynamoose.Schema({
    id: {
      type: String,
      hashKey: true,
    },
    name: {
      type: String,
    },
    merchantId: {
      type: String,
    },
    previousMerchantId: {
      type: String,
    },
    paymentType: {
      type: String,
    },
    depositoryBankName: {
      type: String,
    },
    depositoryBankAccount: {
      type: String,
    },
    company: {
      type: String,
      index: {
        global: true,
        name: 'company-billType',
        rangeKey: 'billType',
        project: true,
      },
    },
    channelId: {
      type: String,
      index: {
        global: true,
        name: 'channelId-billType',
        rangeKey: 'billType',
        project: true,
      },
    },
    bankDiscount: {
      type: String,
    },
    withholdingTax: {
      type: String,
    },
    billType: {
      type: String,
    },
    createdAt: {
      type: String,
    },
    updatedAt: {
      type: String,
    },
    costCenter: {
      type: String,
    },
    bankTerm: {
      type: String,
    },
    bank: {
      type: String,
    },
    enrollmentType: {
      type: String,
    },
    businessUnit: {
      type: String,
    },
  });

  return dynamoose.model(tableName, midSchema, {
    throughput: 'ON_DEMAND',
  });
};
