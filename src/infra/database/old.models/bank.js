module.exports = (dynamoose) => {
  const tableName = `pgw${process.env.STAGE}-banks`;
  const bankSchema = new dynamoose.Schema({
    name: {
      type: String,
      hashKey: true,
    },
    code: {
      type: String,
    },
    gateway: {
      type: String,
    },
    createdAt: {
      type: String,
    },
    updatedAt: {
      type: String,
    },
  });

  return dynamoose.model(tableName, bankSchema, {
    throughput: 'ON_DEMAND',
  });
};
