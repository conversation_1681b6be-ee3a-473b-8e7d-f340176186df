module.exports = (dynamoose) => {
  const tableName = process.env.DDB_TABLE_CHANNELS;
  const channelSchema = new dynamoose.Schema({
    id: {
      type: String,
    },
    name: {
      type: String,
    },
    channelId: {
      type: String,
    },
    channelCode: {
      type: String,
    },
    email: {
      type: String,
    },
    ipAddress: {
      type: String,
    },
    isVerified: {
      type: Boolean,
    },
    clientSecret: {
      type: String,
    },
    clientId: {
      type: String,
    },
    isEnabled: {
      type: Boolean,
    },
    reasonToDelete: {
      type: String,
    },
    callbackUrl: {
      type: String,
    },
    emailNotif: {
      type: Boolean,
    },
    smsNotif: {
      type: Boolean,
    },
    enablePaymentSession: {
      type: Boolean,
    },
    createdAt: {
      type: String,
    },
    updatedAt: {
      type: String,
    },
    merchantCode: {
      type: String,
      nullable: true,
    },
    merchantKey: {
      type: String,
      nullable: true,
    },
    xApiKey: {
      type: String,
      nullable: true,
    },
    cardPaymentMethod: {
      type: String,
      nullable: true,
    },
    gcashPaymentMethod: {
      type: String,
    },
    bankPaymentMethod: {
      type: String,
    },
    ewalletPaymentMethod: {
      type: String,
    },
    gcashEnabled: {
      type: Boolean,
    },
    ipay88Enabled: {
      type: Boolean,
    },
    adyenEnabled: {
      type: Boolean,
    },
    bpiEnabled: {
      type: Boolean,
    },
    xenditEnabled: {
      type: Boolean,
    },
    globeEmailNotification: {
      type: Boolean,
    },
    globeEmailNotificationPatternId: {
      type: String,
      nullable: true,
    },
    innoveEmailNotification: {
      type: Boolean,
    },
    innoveEmailNotificationPatternId: {
      type: String,
      nullable: true,
    },
    bayanEmailNotification: {
      type: Boolean,
    },
    bayanEmailNotificationPatternId: {
      type: String,
      nullable: true,
    },
    failedEmailNotification: {
      type: Boolean,
    },
    billType: {
      type: String,
    },
    serviceType: {
      type: String,
    },
    enableCardHolderName: {
      type: Boolean,
    },
    isCallbackEncrypted: {
      type: Boolean,
    },
    callbackPassPhrase: {
      type: String,
    },
    cardHolderNameType: {
      type: String,
    },
    ecpayServiceType: {
      type: String,
    },
    isOrEnabled: {
      type: Boolean,
    },
    gcreditSubMerchantId: {
      type: String,
    },
    isForPayByLink: {
      type: Boolean,
    },
    gcashOneClickEnabled: {
      type: Boolean,
    },
    gcashOneClickMerchantId: {
      type: String,
    },
    gcashOneClickClientId: {
      type: String,
    },
    gcashOneClickClientSecret: {
      type: String,
    },
    gcashOneClickProductCode: {
      type: String,
    },
    gcashOneClickRedirectUrl: {
      type: String,
    },
    gcashOneClickValidity: {
      type: Number,
    },
    gcashOneClickBindingIdPrefix: {
      type: String,
    },
    bindCallbackUrl: {
      type: String,
    },
    isSecureConnection: {
      type: Boolean,
    },
  });
  return dynamoose.model(tableName, channelSchema, {
    throughput: 'ON_DEMAND',
  });
};
