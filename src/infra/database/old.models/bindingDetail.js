const getBindingStatus = require('../../utils/getBindingStatus');

module.exports = (dynamoose) => {
  const tableName = `pgw${process.env.STAGE}-binddetails`;
  const auditSchema = new dynamoose.Schema({
    uuid: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'uuidGlobalIndex',
        project: true,
      },
    },
    bindingRequestID: {
      type: String,
      hashKey: true,
      required: true,
    },
    bindingTokenId: {
      type: String,
    },
    channel: {
      type: String,
      required: true,
    },
    channelId: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: getBindingStatus(),
      default: 'PROCESSING',
      index: {
        global: true,
        name: 'statusGlobalIndex',
      },
    },
    phoneNumber: {
      type: String,
    },
    validUntil: {
      type: Date,
      alias: 'validity',
    },
    gatewayProcessor: {
      type: String,
      default: 'gcash',
      index: {
        global: true,
        name: 'gatewayProcessorGlobalIndex',
        project: true,
      },
    },
    paymentMethod: {
      type: String,
      default: 'gcash',
      index: {
        global: true,
        name: 'paymentMethodGlobalIndex',
        project: true,
      },
    },
    unbindDate: {
      type: String,
    },
    ttl: {
      type: Number,
    },
    createdAt: {
      type: String,
    },
    updatedAt: {
      type: String,
    },
    sortKey: {
      type: String,
      required: true,
      default: 'binding',
      index: {
        global: true,
        name: 'sortKeyIndex',
        rangeKey: 'createdAt',
        project: true,
      },
    },
  });

  return dynamoose.model(tableName, auditSchema, {
    throughput: 'ON_DEMAND',
    expires: {
      attribute: 'ttl',
    },
  });
};
