module.exports = (dynamoose) => {
  const tableName = `pgw${process.env.STAGE}-gateways`;
  const gatewaySchema = new dynamoose.Schema({
    channelId: {
      type: String,
      hashKey: true,
    },
    paymentMethod: {
      type: String,
      rangeKey: true,
      default: 'None',
    },
    status: {
      type: Boolean,
    },
    updatedAt: {
      type: String,
    },
  });

  return dynamoose.model(tableName, gatewaySchema, {
    throughput: 'ON_DEMAND',
  });
};
