module.exports = (dynamoose) => {
  const tableName = `pgw${process.env.STAGE}-generatedreports`;
  const generatedSchema = new dynamoose.Schema({
    id: {
      type: String,
      hashKey: true,
    },
    year: {
      type: String,
    },
    fileName: {
      type: String,
    },
    fileS3Path: {
      type: String,
    },
    type: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'gsi-Type',
        project: true,
      },
    },
    createdAt: {
      type: String,
    },
    month: {
      type: String,
    },
    ttl: {
      type: Number,
      default: () => {
        const now = new Date();
        now.setMonth(now.getMonth() + 6);
        return Math.floor(now / 1000);
      },
    },
  });

  return dynamoose.model(tableName, generatedSchema, {
    throughput: 'ON_DEMAND',
  });
};
