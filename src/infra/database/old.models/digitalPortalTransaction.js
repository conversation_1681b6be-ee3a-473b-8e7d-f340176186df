module.exports = (dynamoose) => {
  const tableName = `pgw${process.env.STAGE}-digital-portal-transactions`;
  const transactionSchema = new dynamoose.Schema({
    id: {
      type: String,
      hashKey: true,
    },
    transactionId: {
      type: String,
      rangeKey: true,
    },
    accountNo: {
      type: String,
      index: {
        global: true,
        name: 'accountNo-date-dpt',
        rangeKey: 'date',
        project: true,
      },
    },
    amount: String,
    brand: {
      type: String,
      index: {
        global: true,
        name: 'brand-date-dpt',
        rangeKey: 'date',
        project: true,
      },
    },
    channelId: {
      type: String,
      index: {
        global: true,
        name: 'channelId-date-dpt',
        rangeKey: 'date',
        project: true,
      },
    },
    channelName: String,
    content: {
      type: String,
      index: {
        global: true,
        name: 'content-date-dpt',
        rangeKey: 'date',
        project: true,
      },
    },
    contentPartnerShortName: String,
    currency: String,
    customerSegment: String,
    customerSubType: String,
    dailyReportDate: {
      type: String,
      index: {
        global: true,
        name: 'dailyReportDate-date-dpt',
        rangeKey: 'date',
        project: true,
      },
    },
    date: String,
    emailAddress: String,
    entity: String,
    gcashReferenceNo: {
      type: String,
      index: {
        global: true,
        name: 'gcashReferenceNo-date-dpt',
        rangeKey: 'date',
        project: true,
      },
    },
    modeOfPayment: String,
    monthlyReportDate: {
      type: String,
      index: {
        global: true,
        name: 'monthlyReportDate-date-dpt',
        rangeKey: 'date',
        project: true,
      },
    },
    msisdn: {
      type: String,
      index: {
        global: true,
        name: 'msisdn-date-dpt',
        rangeKey: 'date',
        project: true,
      },
    },
    paymentMethod: String,
    paymentStatus: {
      type: String,
      index: {
        global: true,
        name: 'paymentStatus-date-dpt',
        rangeKey: 'date',
        project: true,
      },
    },
    productDescription: String,
    refundAmount: Number,
    refundStatus: {
      type: String,
      index: {
        global: true,
        name: 'refundStatus-date-dpt',
        rangeKey: 'date',
        project: true,
      },
    },
    sku: {
      type: String,
      index: {
        global: true,
        name: 'sku-date-dpt',
        rangeKey: 'date',
        project: true,
      },
    },
    srn: String,
    subscriberType: String,
    voucherDispenseStatus: String,
    paymentGateway: {
      type: String,
      index: {
        global: true,
        name: 'paymentGateway-date-dpt',
        rangeKey: 'date',
        project: true,
      },
    },
  });
  return dynamoose.model(tableName, transactionSchema, {
    throughput: 'ON_DEMAND',
  });
};
