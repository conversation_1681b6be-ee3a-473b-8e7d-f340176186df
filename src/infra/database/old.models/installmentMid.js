module.exports = (dynamoose) => {
  const tableName = `pgw${process.env.STAGE}-installment-mid`;
  const midSchema = new dynamoose.Schema({
    bank: {
      type: String,
      hashKey: true,
    },
    term: {
      type: String,
      rangeKey: true,
    },
    paymentId: {
      type: String,
    },
    billType: {
      type: String,
    },
    createdAt: {
      type: String,
    },
    updatedAt: {
      type: String,
    },
  });

  return dynamoose.model(tableName, midSchema, {
    throughput: 'ON_DEMAND',
  });
};
