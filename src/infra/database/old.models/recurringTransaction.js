module.exports = (dynamoose) => {
  const tableName = `pgw${process.env.STAGE}-recurring-transactions`;
  const transactionSchema = new dynamoose.Schema({
    id: {
      type: String,
      hashKey: true,
    },
    accountNumber: {
      type: String,
      index: {
        global: true,
        name: 'accountNumberGlobalIndex',
        rangeKey: 'timestamp',
        project: true,
      },
    },
    isActive: {
      type: Boolean,
    },
    channelId: {
      type: String,
      index: {
        global: true,
        name: 'channelIdGlobalIndex',
        rangeKey: 'timestamp',
        project: true,
      },
    },
    transId: {
      type: String,
    },
    enrollmentType: {
      type: String,
    },
    ccNumber: {
      type: String,
    },
    ccType: {
      type: String,
    },
    bankName: {
      type: String,
    },
    tokenId: {
      type: String,
    },
    entity: {
      type: String,
      index: {
        global: true,
        name: 'entityGlobalIndex',
        rangeKey: 'timestamp',
        project: true,
      },
    },
    amount: {
      type: String,
    },
    status: {
      type: String,
      index: {
        global: true,
        name: 'statusGlobalIndex',
        rangeKey: 'timestamp',
        project: true,
      },
    },
    entityName: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'entityNameGlobalIndex',
        rangeKey: 'timestamp',
        project: true,
      },
    },
    reason: {
      type: String,
    },
    currency: {
      type: String,
    },
    userContact: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'userContactGlobalIndex',
        rangeKey: 'timestamp',
        project: true,
      },
    },
    userEmail: {
      type: String,
    },
    timestamp: {
      type: String,
    },
    productDescription: {
      type: String,
    },
    price: {
      type: String,
    },
  });

  return dynamoose.model(tableName, transactionSchema, {
    throughput: 'ON_DEMAND',
  });
};
