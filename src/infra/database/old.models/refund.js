module.exports = (dynamoose) => {
  const tableName = `pgw${process.env.STAGE}-refundtransaction`;
  const refundSchema = new dynamoose.Schema({
    id: {
      type: String,
      hashKey: true,
    },
    requestTimeStamp: {
      type: String,
      rangeKey: true,
    },
    transactionId: {
      type: String,
    },
    entity: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'pgwRefundEntity',
        rangeKey: 'timestamp',
        project: true,
      },
    },
    accountNumber: {
      type: String,
    },
    channelId: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'pgwRefundChannelId',
        rangeKey: 'timestamp',
        project: true,
      },
    },
    channelName: {
      type: String,
    },
    postedTimestamp: {
      type: String,
    },
    amountValue: {
      type: String,
    },
    status: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'pgwRefundStatus',
        rangeKey: 'timestamp',
        project: true,
      },
    },
    refundReason: {
      type: String,
    },
    refundAmount: {
      type: String,
    },
    refundApprovedTimestamp: {
      type: String,
    },
    refundRejectedTimestamp: {
      type: String,
    },
    refundId: {
      type: String,
      index: {
        global: true,
        name: 'pgwRefundId',
        rangeKey: 'timestamp',
        project: true,
      },
    },
    acquirementId: {
      type: String,
    },
    refundApprovalStatus: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'pgwRefundApprovalStatus',
        rangeKey: 'timestamp',
        project: true,
      },
    },
    refundStatus: {
      type: String,
    },
    timestamp: {
      type: String,
    },
    paymentGateway: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'pgwPaymentGateway',
        rangeKey: 'timestamp',
        project: true,
      },
    },
    ttl: {
      type: Number,
    },
    mobileNumber: {
      type: String,
    },
    approverRemarks: {
      type: String,
    },
    billType: {
      type: String,
    },
    paymentMethod: {
      type: String,
    },
    finalAmount: {
      type: Number,
    },
    convenienceFee: {
      type: Number,
    },
    hasConvenienceFee: {
      type: Boolean,
    },
  });
  return dynamoose.model(tableName, refundSchema, {
    throughput: 'ON_DEMAND',
  });
};
