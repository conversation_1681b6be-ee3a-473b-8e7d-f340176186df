module.exports = (dynamoose) => {
  const tableName = `pgw${process.env.STAGE}-snapshots`;
  const snapshotSchema = new dynamoose.Schema({
    id: {
      type: String,
      hashKey: true,
    },
    type: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'snapshotTypeGSI',
        project: true,
        rangeKey: 'createdAt',
      },
    },
    day: {
      type: String,
    },
    month: {
      type: String,
    },
    year: {
      type: String,
    },
    payload: {
      type: String,
    },
    createdAt: {
      type: String,
    },
    updatedAt: {
      type: String,
    },
    ttl: {
      type: Number,
    },
  });

  return dynamoose.model(tableName, snapshotSchema, {
    throughput: 'ON_DEMAND',
  });
};
