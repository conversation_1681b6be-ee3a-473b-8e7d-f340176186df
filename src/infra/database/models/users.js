module.exports = (dynamoose) => {
  const tableName = process.env.DDB_TABLE_USERS;
  const userSchema = new dynamoose.Schema({
    id: {
      type: String,
    },
    name: {
      type: String,
    },
    email: {
      type: String,
    },
    roleId: {
      type: String,
    },
    mobileNumber: {
      type: [String, Number],
    },
    group: {
      type: String,
    },
    division: {
      type: String,
    },
    department: {
      type: String,
    },
    loginTime: {
      type: String,
    },
    isActive: {
      type: Boolean,
    },
    reasonToDeactivate: {
      type: String,
    },
    reasonToDelete: {
      type: String,
    },
    emailNotif: {
      type: Boolean,
    },
    smsNotif: {
      type: Boolean,
    },
    isActiveAt: {
      type: String,
    },
    createdAt: {
      type: String,
    },
    updatedAt: {
      type: String,
    },
    billType: {
      type: String,
    },
    assignedChannels: {
      type: String,
    },
    cardAssignedChannels: {
      type: String,
    },
    ewalletAssignedChannels: {
      type: String,
    },
    postPaymentConfigChannels: {
      type: String,
    },
    refreshToken: {
      type: String,
    },
    accessToken: {
      type: String,
    },
  });

  return dynamoose.model(tableName, userSchema, {
    throughput: 'ON_DEMAND',
  });
};
