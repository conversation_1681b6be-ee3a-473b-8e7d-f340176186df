module.exports = (dynamoose) => {
  const tableName = process.env.DDB_TABLE_CONVENIENCEFEEBRANDS;
  const convenienceFeeBrandSchema = new dynamoose.Schema({
    id: {
      type: String,
      hashKey: true,
    },
    name: {
      type: String,
      required: true,
    },
    createdAt: {
      type: String,
      required: true,
    },
    updatedAt: {
      type: String,
      required: true,
    },
  });

  return dynamoose.model(tableName, convenienceFeeBrandSchema, { throughput: 'ON_DEMAND' });
};
