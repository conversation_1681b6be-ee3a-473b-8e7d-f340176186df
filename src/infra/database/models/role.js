module.exports = (dynamoose) => {
  const tableName = process.env.DDB_TABLE_ROLES;
  const roleSchema = new dynamoose.Schema(
    {
      id: {
        type: String,
      },
      isActive: {
        type: Boolean,
      },
      code: {
        type: String,
      },
      name: {
        type: String,
      },
      notes: {
        type: String,
      },
      permissions: {
        type: [String, Object],
        schema: {
          Dashboard: {
            type: Array,
            schema: [String],
          },
          User: {
            type: Array,
            schema: [String],
          },
          Role: {
            type: Array,
            schema: [String],
          },
          Channel: {
            type: Array,
            schema: [String],
          },
          Mid: {
            type: Array,
            schema: [String],
          },
          Provider: {
            type: Array,
            schema: [String],
          },
          Bank: {
            type: Array,
            schema: [String],
          },
          Transaction: {
            type: Array,
            schema: [String],
          },
          Failed: {
            type: Array,
            schema: [String],
          },
          Billing: {
            type: Array,
            schema: [String],
          },
          Gateway: {
            type: Array,
            schema: [String],
          },
          Collection: {
            type: Array,
            schema: [String],
          },
          Treasury: {
            type: Array,
            schema: [String],
          },
          Wireline: {
            type: Array,
            schema: [String],
          },
          MonthlyGenerated: {
            type: Array,
            schema: [String],
          },
          Audit: {
            type: Array,
            schema: [String],
          },
          Archive: {
            type: Array,
            schema: [String],
          },
          Config: {
            type: Array,
            schema: [String],
          },
          LukeBatchFile: {
            type: Array,
            schema: [String],
          },
          ChannelReport: {
            type: Array,
            schema: [String],
          },
          GotsReport: {
            type: Array,
            schema: [String],
          },
          ECPay: {
            type: Array,
            schema: [String],
          },
          GlobeOne: {
            type: Array,
            schema: [String],
          },
          PayByLink: {
            type: Array,
            schema: [String],
          },
          LoadORReport: {
            type: Array,
            schema: [String],
          },
          ContentGcashReport: {
            type: Array,
            schema: [String],
          },
          ContentFraudReport: {
            type: Array,
            schema: [String],
          },
          GcashRefundRequest: {
            type: Array,
            schema: [String],
          },
          GcashRefundApproval: {
            type: Array,
            schema: [String],
          },
          GcashRefundDetailedReport: {
            type: Array,
            schema: [String],
          },
          GcashRefundSummaryReport: {
            type: Array,
            schema: [String],
          },
          InstallmentReport: {
            type: Array,
            schema: [String],
          },
          InstallmentMid: {
            type: Array,
            schema: [String],
          },
          ADADeclinedReport: {
            type: Array,
            schema: [String],
          },
          ADASummaryReport: {
            type: Array,
            schema: [String],
          },
          CardRefundRequest: {
            type: Array,
            schema: [String],
          },
          CardRefundApproval: {
            type: Array,
            schema: [String],
          },
          EndGameReport: {
            type: Array,
            schema: [String],
          },
          CardRefundDetailedReport: {
            type: Array,
            schema: [String],
          },
          CardRefundSummaryReport: {
            type: Array,
            schema: [String],
          },
          DropinSimulator: {
            type: Array,
            schema: [String],
          },
          BillLinerConfig: {
            type: Array,
            schema: [String],
          },
          XenditRefundRequest: {
            type: Array,
            schema: [String],
          },
          XenditRefundApproval: {
            type: Array,
            schema: [String],
          },
          XenditRefundDetailedReport: {
            type: Array,
            schema: [String],
          },
          XenditRefundSummaryReport: {
            type: Array,
            schema: [String],
          },
          PostPaymentConfig: {
            type: Array,
            schema: [String],
          },
          PayByLinkModule: {
            type: Array,
            schema: [String],
          },
          PayByLinkReport: {
            type: Array,
            schema: [String],
          },
          GCashBindingReport: {
            type: Array,
            schema: [String],
          },
          ConvenienceFee: {
            type: Array,
            schema: [String],
          },
          ConvenienceFeeBrand: {
            type: Array,
            schema: [String],
          },
        },
      },
      reasonToDelete: {
        type: String,
      },
      emailNotif: {
        type: Boolean,
      },
      smsNotif: {
        type: Boolean,
      },
      createdAt: {
        type: String,
      },
      updatedAt: {
        type: String,
      },
    },
    { useDocumentTypes: true, useNativeBooleans: true }
  );

  return dynamoose.model(tableName, roleSchema, {
    throughput: 'ON_DEMAND',
  });
};
