module.exports = (dynamoose) => {
  const tableName = process.env.DDB_TABLE_CONVENIENCEFEE;
  const convenienceFeeSchema = new dynamoose.Schema({
    id: {
      type: String,
      hashKey: true,
    },
    channelId: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    brand: {
      type: String,
      required: true,
    },
    gatewayProcessor: {
      type: String,
      required: true,
    },
    paymentMethod: {
      type: String,
      required: true,
    },
    transactionType: {
      type: String,
      required: true,
    },
    convenienceFeeType: {
      type: String,
      default: 'Off',
      required: true,
    },
    convenienceFeeValue: {
      type: Number,
      default: 0,
    },
    convenienceFeeThreshold: {
      type: Number,
      default: 0,
    },
    convenienceFeeTieredScheme: {
      type: Number,
      default: 0,
    },
    createdAt: {
      type: String,
      required: true,
    },
    updatedAt: {
      type: String,
      required: true,
    },
    compositeKey: {
      type: String,
      index: {
        name: 'compositeKeyIndex',
        global: true,
      },
      required: true,
    },
  });

  return dynamoose.model(tableName, convenienceFeeSchema, { throughput: 'ON_DEMAND' });
};
