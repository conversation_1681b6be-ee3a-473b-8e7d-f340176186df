module.exports = (dynamoose) => {
  const tableName = process.env.DDB_TABLE_REFUND;
  const refundSchema = new dynamoose.Schema({
    paymentId: {
      type: String,
      hashKey: true,
    },
    createDateTime: {
      type: String,
      rangeKey: true,
    },
    transactionId: {
      type: String,
    },
    entity: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'RefundEntityIndex',
        rangeKey: 'createDateTime',
        project: true,
      },
    },
    accountNumber: {
      type: String,
    },
    channelId: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'ChannelIdIndex',
        rangeKey: 'createDateTime',
        project: true,
      },
    },
    channelName: {
      type: String,
    },
    postedTimestamp: {
      type: String,
    },
    amountValue: {
      type: String,
    },
    status: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'StatusIndex',
        rangeKey: 'createDateTime',
        project: true,
      },
    },
    refundReason: {
      type: String,
    },
    refundAmount: {
      type: String,
    },
    refundApprovedTimestamp: {
      type: String,
    },
    refundRejectedTimestamp: {
      type: String,
    },
    refundId: {
      type: String,
      index: {
        global: true,
        name: 'RefundIdIndex',
        rangeKey: 'createDateTime',
        project: true,
      },
    },
    acquirementId: {
      type: String,
    },
    refundApprovalStatus: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'RefundApprovalStatusIndex',
        rangeKey: 'createDateTime',
        project: true,
      },
    },
    refundStatus: {
      type: String,
    },
    timestamp: {
      type: String,
    },
    paymentGateway: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'PaymentGatewayIndex',
        rangeKey: 'timestamp',
        project: true,
      },
    },
    ttl: {
      type: Number,
    },
    mobileNumber: {
      type: String,
    },
    approverRemarks: {
      type: String,
    },
    billType: {
      type: String,
    },
    paymentMethod: {
      type: String,
    },
    finalAmount: {
      type: Number,
    },
    convenienceFee: {
      type: Number,
    },
    hasConvenienceFee: {
      type: Boolean,
    },
    isAutoRefund: {
      type: String,
      index: {
        global: true,
        name: 'IsAutoRefundIndex',
        project: true,
      },
    },
  });
  return dynamoose.model(tableName, refundSchema, {
    throughput: 'ON_DEMAND',
  });
};
