module.exports = (dynamoose) => {
  const tableName = process.env.DDB_TABLE_POST_PAYMENT_CONFIG;
  const configSchema = new dynamoose.Schema({
    channelId: {
      type: String,
      hashKey: true,
    },
    compositeKey: {
      type: String,
      rangeKey: true,
    },
    gatewayProcessor: {
      type: String,
      // enum: ['adyen', 'gcash', 'xendit'],
      enum: ['xendit'],
      required: true,
    },
    paymentMethod: {
      type: String,
      // enum: ['cc_dc', 'gcash', 'bpi', 'rcbc', 'ubp', 'paymaya', 'grabpay', 'shopeepay', 'visa', 'mastercard'],
      enum: ['card_installment', 'card_straight'],
      required: true,
    },
    billRealTime: {
      type: String,
      required: true,
    },
    nonBill: {
      type: Number,
      required: true,
    },
    billFallout: {
      type: Number,
      required: true,
    },
    createDateTime: {
      type: String,
    },
    updateDateTime: {
      type: String,
    },
  });

  return dynamoose.model(tableName, configSchema, {
    throughput: 'ON_DEMAND',
  });
};
