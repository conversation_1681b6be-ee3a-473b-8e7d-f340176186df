module.exports = (dynamoose) => {
  const tableName = process.env.DDB_TABLE_CONFIGURATION;
  const configSchema = new dynamoose.Schema({
    name: {
      type: String,
      hashKey: true,
    },
    value: {
      type: [String, Number, Array, Boolean],
      schema: dynamoose.type.ANY,
    },
    type: {
      type: String,
      index: {
        global: true,
        name: 'configTypeGlobalIndex',
        rangeKey: 'updatedAt',
        project: true,
      },
    },
    serviceType: {
      type: String,
    },
    subMerchantId: {
      type: String,
    },
    createdAt: {
      type: String,
    },
    updatedAt: {
      type: String,
    },
    merchant: {
      type: String,
    },
    content: {
      type: String,
    },
    adyenMerchantAccount: {
      type: String,
    },
  });

  return dynamoose.model(tableName, configSchema, {
    throughput: 'ON_DEMAND',
  });
};
