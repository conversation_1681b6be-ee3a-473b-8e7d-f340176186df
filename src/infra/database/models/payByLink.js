module.exports = (dynamoose) => {
  const tableName = process.env.DDB_TABLE_PAYBYLINK;
  const payByLinkSchema = new dynamoose.Schema({
    id: {
      type: String,
      hashKey: true,
    },
    sortKey: {
      type: String,
      required: true,
      default: 'paybylink',
      index: {
        global: true,
        name: 'sortKeyIndex',
        rangeKey: 'createdAt',
        project: true,
      },
    },
    status: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'statusIndex',
        project: true,
      },
    },
    channelId: {
      type: String,
      required: true,
    },
    paymentGateway: {
      type: String,
      required: true,
    },
    paymentLink: {
      type: String,
      required: true,
    },
    merchantAccount: {
      type: String,
    },
    merchantReference: {
      type: String,
    },
    amount: {
      type: Number,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    expiredAt: {
      type: String,
    },
    linkType: {
      type: String,
    },
    customer: {
      type: String,
    },
    createdAt: {
      type: String,
      default: new Date().toISOString(),
    },
    updatedAt: {
      type: String,
      default: new Date().toISOString(),
    },
    ttl: {
      type: Number,
    },
  });

  return dynamoose.model(tableName, payByLinkSchema, {
    throughput: 'ON_DEMAND',
    ttl: {
      attribute: 'ttl',
    },
  });
};
