const dynamoose = require('dynamoose');
const { db } = require('../../../../config');
const { ModelsLoader } = require('../../dynamoose');

if (db) {
  const { dynamooseCore, dynamooseTable } = db;

  dynamoose.Table.defaults.set(dynamooseTable);

  const ddb = new dynamoose.aws.ddb.DynamoDB(dynamooseCore);
  dynamoose.aws.ddb.set(ddb);

  // Pass initializeTables flag from config or default to false for safety
  const { models } = ModelsLoader.load({
    dynamoose,
    baseFolder: __dirname,
  });

  module.exports = {
    dynamoose,
    models,
  };
} else {
  console.error('Database config file log found, disabling database.');
}
