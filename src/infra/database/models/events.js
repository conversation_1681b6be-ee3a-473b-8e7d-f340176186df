module.exports = (dynamoose) => {
  const tableName = process.env.DDB_TABLE_EVENTS;
  const configSchema = new dynamoose.Schema({
    paymentId: {
      type: String,
      hashKey: true,
      required: true,
    },
    eventId: {
      type: String,
      rangeKey: true,
      required: true,
    },
    channelId: {
      type: String,
    },
    createDateTime: {
      type: String,
      required: true,
    },
    errorMessage: {
      type: String,
    },
    eventDetails: {
      type: Object,
    },
    eventName: {
      type: String,
    },
    eventSource: {
      type: String,
    },
    eventStatus: {
      type: String,
    },
    paymentMethod: {
      type: String,
    },
    traceparent: {
      type: String,
    },
  });

  return dynamoose.model(tableName, configSchema, {
    throughput: 'ON_DEMAND',
  });
};
