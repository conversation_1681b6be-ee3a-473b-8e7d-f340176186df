module.exports = (dynamoose) => {
  const tableName = process.env.DDB_TABLE_BATCHFILE;
  const batchFileSchema = new dynamoose.Schema({
    filename: {
      type: String,
      hashKey: true,
    },
    location: {
      type: String,
    },
    status: {
      type: String,
    },
    createDateTime: {
      type: String,
    },
  });

  return dynamoose.model(tableName, batchFileSchema, {
    throughput: 'ON_DEMAND',
  });
};
