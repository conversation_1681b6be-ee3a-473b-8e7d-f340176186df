module.exports = (dynamoose) => {
  const tableName = process.env.DDB_TABLE_AUDITLOGS;
  const auditSchema = new dynamoose.Schema({
    id: {
      type: String,
      hashKey: true,
    },
    sortKey: {
      type: String,
      required: true,
      index: {
        global: true,
        name: 'sortKey',
        rangeKey: 'createdAt',
        project: true,
      },
    },
    ipAddress: {
      type: String,
    },
    userId: {
      type: String,
    },
    roleId: {
      type: String,
    },
    roleName: {
      type: String,
    },
    userName: {
      type: String,
    },
    userEmail: {
      type: String,
    },
    userAgent: {
      type: String,
    },
    category: {
      type: String,
    },
    oldValue: {
      type: [String, Object],
    },
    newValue: {
      type: [String, Object],
    },
    reasonToDelete: {
      type: String,
    },
    reasonToUpdate: {
      type: String,
    },
    isViewed: {
      type: Boolean,
    },
    ttl: Number,
    createdAt: {
      type: String,
    },
    updatedAt: {
      type: String,
    },
    reasonToDeactivate: {
      type: String,
    },
  });
  return dynamoose.model(tableName, auditSchema, {
    throughput: 'ON_DEMAND',
  });
};
