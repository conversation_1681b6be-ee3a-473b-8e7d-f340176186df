const crypto = require('crypto');

class jwt {
  constructor({ config, base64 }) {
    this.time = Math.round(new Date().getTime() / 1000);
    const minutes = 60;

    this.time60MinutesAhead = Math.round((new Date().getTime() + minutes * 60000) / 1000);

    this.headers = {
      alg: 'aes-256-cbc',
      typ: 'JWT',
      exp: this.time60MinutesAhead,
    };

    this.key = Buffer.from(config.auth.JWT_KEY);
    this.iv = Buffer.alloc(16, 0);
    this.cipher = crypto.createCipheriv('aes-256-cbc', this.key, this.iv);
    this.base64 = base64;
  }

  encode(data) {
    const token = `${this.base64.encode(JSON.stringify(this.headers))}.${this.base64.encode(data)}`;
    let encrypted = this.cipher.update(token, 'utf8', 'base64');
    encrypted += this.cipher.final('base64');
    encrypted = this.base64.encodeBase64Url(encrypted);
    return encrypted;
  }

  decode(encoded) {
    try {
      const decipher = crypto.createDecipheriv('aes-256-cbc', this.key, this.iv);
      let decoded = this.base64.decodeBase64Url(encoded);
      decoded = decipher.update(decoded, 'base64', 'utf8');
      decoded += decipher.final('utf8');
      decoded = decoded.split('.');
      return {
        headers: this.base64.decode(decoded[0]),
        data: this.base64.decode(decoded[1]),
      };
    } catch (error) {
      throw new Error('Token could not be decoded.');
    }
  }

  validate(token) {
    const { headers, data } = this.decode(token);
    const parsedHeader = JSON.parse(headers);
    const parsedData = JSON.parse(data);
    if (typeof parsedHeader.exp === 'undefined' || this.checkExpiry(parsedHeader.exp)) {
      return false;
    }
    return parsedData;
  }

  checkExpiry(tokenExpiry) {
    if (tokenExpiry > this.time) {
      return false;
    }
    return true;
  }
}

module.exports = jwt;
