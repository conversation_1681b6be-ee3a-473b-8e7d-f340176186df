const Client = require('./client');

module.exports = async (params, config) => {
  const { name, email } = params;

  this.client = new Client({
    hostname: config.auth.COGNITO_ENDPOINT,
  });
  const registrationPayload = {
    username: email,
    name,
  };
  const response = await this.client.post('/register', registrationPayload);

  if (typeof response.results === 'undefined') {
    throw new Error(`Cognito Error: ${response.message}`);
  }
  return response.results;
};
