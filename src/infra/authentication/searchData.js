const Client = require('./client');

module.exports = async (params, config, accessToken) => {
  this.client = new Client({
    hostname: config.auth.PAYMENT_SERVICE_ENDPOINT,
    headers: {
      authorization: `Bearer ${accessToken}`,
    },
  });

  const searchData = {
    command: {
      name: 'SearchData',
      payload: {
        data: params,
      },
    },
  };

  return this.client.post('/command', searchData);
};
