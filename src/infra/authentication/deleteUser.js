const Client = require('./client');

module.exports = async (params, config) => {
  const { clientId, clientSecret } = params;

  this.client = new Client({
    hostname: config.auth.COGNITO_ENDPOINT,
  });

  const deleteUserPayload = {
    clientId,
    clientSecret,
  };
  const response = await this.client.post('/delete-user', deleteUserPayload);

  if (typeof response.results === 'undefined') {
    throw new Error(`Cognito Error: ${response.message}`);
  }
  return {
    response: 200,
    message: response.message,
  };
};
