const Client = require('./client');

module.exports = async (params, config, accessToken) => {
  const { bindingRequestID, uuid } = params;

  this.client = new Client({
    hostname: config.auth.PAYMENT_SERVICE_ENDPOINT,
    headers: {
      authorization: `Bearer ${accessToken}`,
    },
  });

  const gCashUnbindRequest = {
    command: {
      name: 'GcashUnbindingRequest',
      payload: {
        uuid,
        bindingRequestID,
      },
    },
  };

  return this.client.post('/command', gCashUnbindRequest);
};
