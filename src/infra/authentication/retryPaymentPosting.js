const Client = require('./client');

module.exports = async (params, config, accessToken) => {
  const { id, accountNumber } = params;

  this.client = new Client({
    hostname: config.auth.PAYMENT_SERVICE_ENDPOINT,
    headers: {
      authorization: `Bearer ${accessToken}`,
    },
  });

  try {
    const retryPaymentPosting = {
      command: {
        name: 'RetryPaymentPosting',
        payload: {
          paymentId: id,
          accountNumber,
        },
      },
    };
    const response = await this.client.post('/command', retryPaymentPosting);
    if (response.message === 'Internal server error') {
      throw response;
    }
    return response;
  } catch (e) {
    throw new Error(`Payment Posting Error: ${e.message}`);
  }
};
