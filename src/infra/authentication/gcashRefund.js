const Client = require('./client');

module.exports = async (params, config, accessToken) => {
  const { paymentId, transactionId, requestTimeStamp, mobileNumber, refundAmount, refundReason, acquirementId } =
    params;

  this.client = new Client({
    hostname: config.auth.PAYMENT_SERVICE_ENDPOINT,
    headers: {
      authorization: `Bear<PERSON> ${accessToken}`,
    },
  });

  const createGCashRefund = {
    command: {
      name: 'CreateRefundGcash',
      payload: {
        paymentId,
        transactionId,
        requestTimeStamp,
        mobileNumber,
        refundAmount: parseFloat(refundAmount),
        refundReason,
        acquirementId,
      },
    },
  };
  return this.client.post('/command', createGCashRefund);
};
