// const fetch = require('node-fetch');

class Client {
  constructor(config) {
    this.config = config;
  }

  get(path, payload) {
    const getPayload = (data) => {
      const str = [];
      Object.entries(data).forEach((entries) => {
        const [key, value] = entries;
        str.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      });
      return str.join('&');
    };
    return this.makeRequest(`${this.config.hostname}${path}?${getPayload(payload)}`);
  }

  post(path, payload) {
    return this.makeRequest(`${this.config.hostname}${path}`, payload);
  }

  async makeRequest(path, payload) {
    const res = await fetch(path, {
      method: payload ? 'post' : 'get',
      body: payload ? JSON.stringify(payload) : null,
      headers: {
        ...this.config.headers,
        'Content-Type': 'application/json',
      },
    });

    return res.json();
  }
}

module.exports = Client;
