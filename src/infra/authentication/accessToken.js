const Client = require('./client');

module.exports = async (params, config) => {
  const { clientId, clientSecret } = params;
  this.client = new Client({
    hostname: config.auth.COGNITO_ENDPOINT,
  });

  try {
    const accessTokenPayload = {
      clientId,
      clientSecret,
    };

    const response = await this.client.get('/accesstoken', accessTokenPayload);

    if (typeof response.results === 'undefined') {
      throw new Error(`Cognito Error: ${response.message}`);
    }
    return response.results;
  } catch (e) {
    return {
      status: 500,
      error: e,
    };
  }
};
