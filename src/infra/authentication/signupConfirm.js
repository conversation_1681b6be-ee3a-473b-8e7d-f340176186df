const Client = require('./client');

module.exports = async (params, config) => {
  const { clientId, confirmationCode } = params;

  this.client = new Client({
    hostname: config.auth.COGNITO_ENDPOINT,
  });

  const deleteUserPayload = {
    clientId,
    code: confirmationCode,
  };

  const response = await this.client.post('/confirm', deleteUserPayload);

  if (typeof response.results === 'undefined') {
    throw new Error(`Cognito Error: ${response.message}`);
  }
  return response.results;
};
