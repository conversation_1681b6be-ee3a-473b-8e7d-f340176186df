const Client = require('./client');

module.exports = async (params, config, accessToken) => {
  const { paymentId, refundAmount, refundReason, emailAddress, requestTimeStamp } = params;

  this.client = new Client({
    hostname: config.auth.PAYMENT_SERVICE_ENDPOINT,
    headers: {
      authorization: `Bearer ${accessToken}`,
    },
  });

  const createCardRefund = {
    command: {
      name: 'ManualPaymentRefund',
      payload: {
        paymentId,
        amount: parseFloat(refundAmount),
        reason: refundReason,
        emailAddress,
        requestTimeStamp,
      },
    },
  };
  return this.client.post('/command', createCardRefund);
};
