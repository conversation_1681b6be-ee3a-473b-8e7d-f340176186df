const Category = (name, event) => {
  const cagetories = {
    User: {
      login: 'Login',
      create: 'User Mgt > Accounts > Create',
      update: 'User Mgt > Accounts > Update',
      delete: 'User Mgt > Accounts > Delete',
      download: 'User Mgt > Accounts > Export CSV',
      deactivate: 'User Mgt > Accounts > Deactivate',
    },
    Role: {
      create: 'User Mgt > Roles > Create',
      update: 'User Mgt > Roles > Update',
      delete: 'User Mgt > Roles > Delete',
    },
    Channel: {
      create: 'Channel Mgt > Create',
      update: 'Channel Mgt > Update',
      delete: 'Channel Mgt > Delete',
    },
    Provider: {
      update: 'Provider Mgt > Update',
    },
    Config: {
      update: 'System Config > Update',
    },
    GCashRefund: {
      request: 'GCash Refund > GCash Refund Request > For Approval',
      approved: 'GCash Refund > GCash Refund Approval > Rejected',
      rejected: 'GCash Refund > GCash Refund Approval > Approved',
    },
    CardRefund: {
      request: 'Card Refund > Card Refund Request > For Approval',
      approved: 'Card Refund > Card Refund Approval > Rejected',
      rejected: 'Card Refund > Card Refund Approval > Approved',
    },
    XenditRefund: {
      request: 'Xendit Refund > Xendit Refund Request > For Approval',
      approved: 'Xendit Refund > Xendit Refund Approval > Rejected',
      rejected: 'Xendit Refund > Xendit Refund Approval > Approved',
    },
    Reports: {
      gCashBindingReport: 'Reports > GCash Binding Report > Export CSV',
      transaction: 'Reports > Transaction Logs > Export CSV',
      failed: 'Reports > Failed Posting > Export CSV',
      billing: 'Reports > Biling Reports > Export CSV',
      creditcard: 'Reports > Gateway for Online Payments > Export CSV',
      collection: 'Reports > Gateway Collection > Export CSV',
      rawireline: 'Reports > Revenue Accounting Wireline > Export CSV',
      gatewaycc: 'Reports > Monthly Generated > CC Detailed Report > Export CSV',
      collectioncompany: 'Reports > Monthly Generated > CC Summary Report > Company > Export CSV',
      collectionmid: 'Reports > Monthly Generated > CC Summary Report > MID > Export CSV',
      treasury: 'Reports > Treasury > Export CSV',
      revenuewirelesspaytype: 'Reports > Monthly Generated > RA Wireless > PayType > Export CSV',
      revenuewirelesspaymode: 'Reports > Monthly Generated > RA Wireless > PayMode > Export CSV',
      lukeBatch: 'Reports > Luke Batch Files > Export CSV',
      channelreport: 'Reports > Channel Reports > Export CSV',
      gots: 'Reports > GOTS Reports > Export CSV',
      ecpay: 'Reports > ECPay Reports > Export CSV',
      globeone: 'Reports > GlobeOne Logs > Export CSV',
      paybylink: 'Reports > PayByLink > Export CSV',
      loadorreport: 'Reports > LoadORReport > Export CSV',
      contentgcashreport: 'Reports > ContentGCashReport > Export CSV',
      contentfraudreport: 'Reports > SwipeTransactionLogs > Export CSV',
      gcashrefunddetailedreport: 'Report > GCash Refund Detailed Report > Export CSV',
      gcashrefundsummaryreport: 'Report > Gcash Refund Summary Report > Export CSV',
      installmentreport: 'Report > Installment Report > Export CSV',
      adadeclinedreport: 'Report > ADA Declined Detailed Report > Export CSV',
      adasummaryreport: 'Report > ADA Summary Report > Export CSV',
      endgamereport: 'Report > EndGame Transaction Logs > Export CSV',
      cardrefunddetailedreport: 'Report > Card Refund Detailed Report > Export CSV',
      cardrefundsummaryreport: 'Report > Card Refund Summary Report > Export CSV',
      xenditrefunddetailedreport: 'Report > Xendit Refund Detailed Report > Export CSV',
      xenditrefundsummaryreport: 'Report > Xendit Refund Summary Report > Export CSV',
    },
    InstallmentMid: {
      download: 'Installment Mid > Export CSV',
      create: 'Installment Mid > Create',
      update: 'Installment Mid > Update',
      delete: 'Installment Mid > Delete',
    },
    Archive: {
      transaction: 'System > Archive > RUDY DB > Transaction Tracking Logs > Export CSV',
      paybill: 'System > Archive > RUDY DB > Paybill Reference > Export CSV',
    },
    Mid: {
      create: 'Mid Mgt > Create',
      update: 'Mid Mgt > Update',
      delete: 'Mid Mgt > Delete',
    },
    Bank: {
      create: 'Bank Mgt > Create',
      update: 'Bank Mgt > Update',
      delete: 'Bank Mgt > Delete',
    },
    BillLinerConfig: {
      create: 'System > Bill Liner Config > Create',
      delete: 'System > Bill Liner Config > Delete',
    },
    PostPaymentConfig: {
      create: 'PostPayment Configuration > Create',
      update: 'PostPayment Configuration > Update',
      delete: 'PostPayment Configuration > Delete',
    },
    GCashBinding: {
      unbind: 'GCash Binding > Unbind',
    },
    ConvenienceFee: {
      create: 'Convenience Fee > Create',
      update: 'Convenience Fee > Update',
      delete: 'Convenience Fee Brand > Delete',
    },
    ConvenienceFeeBrand: {
      create: 'Convenience Fee Brand > Create',
      update: 'Convenience Fee Brand > Update',
    },
  };
  return cagetories[name][event];
};

module.exports = Category;
