module.exports = {
  monthNames() {
    return [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
  },
  monthName(monthIndex) {
    return this.monthNames()[monthIndex];
  },
  pstTime(date) {
    const now = new Date(date);
    return new Date(now.setHours(8, 0, 0, 0));
  },
  getDateYesterday() {
    const now = new Date();
    const yesterday = new Date(now.setDate(now.getDate() - 1));
    const start = new Date(yesterday.setHours(8, 0, 0, 0));
    const end = new Date(yesterday.setHours(31, 59, 59, 999));
    return {
      date: yesterday,
      start,
      end,
    };
  },
  addPadding(date) {
    const dateNumber = `0${date}`;
    return `${dateNumber.slice(-2)}`;
  },
  formatDate(date) {
    const y = date.getFullYear();
    const m = date.getMonth();
    const startDate = new Date(y, m, 1);
    const endDate = new Date(y, m + 1, 0);

    return {
      month: this.monthNames[m],
      date,
      startDate,
      endDate,
      startTimestamp: startDate.getTime(),
      endTimestamp: endDate.getTime(),
    };
  },

  formatDateYYYYMMDD(date) {
    const year = date.getFullYear();
    const month = this.addPadding(date.getMonth() + 1);
    const day = this.addPadding(date.getDate());

    return `${year}${month}${day}`;
  },

  formatDateMMDDYYYY(date) {
    const year = date.getFullYear();
    const month = this.addPadding(date.getMonth() + 1);
    const day = this.addPadding(date.getDate());
    return `${month}${day}${year}`;
  },

  monthsCovered(startMonth, endMonth) {
    const today = new Date();
    const currentYear = today.getFullYear();
    const todayMonth = today.getMonth();
    const months = [];

    let count = 0;
    let currentMonth = null;
    do {
      let date = new Date(currentYear, this.monthNames().indexOf(startMonth) + count);
      const start = this.monthNames().indexOf(startMonth);
      const end = this.monthNames().indexOf(endMonth);
      if (start > end || todayMonth < end) {
        date = new Date(currentYear - 1, this.monthNames().indexOf(startMonth) + count);
      }
      months.push({
        month: this.monthNames()[date.getMonth()],
        year: date.getFullYear().toString(),
      });
      currentMonth = this.monthNames()[date.getMonth()];
      count += 1;
    } while (endMonth !== currentMonth);

    return months;
  },

  monthListFromCurrentDate() {
    const now = new Date();
    const numberOfPastMonth = 5;
    const months = [];
    // Push current month
    months.push(now);

    // Push remaining months
    for (let index = 1; index <= numberOfPastMonth; index += 1) {
      months.push(new Date(now.getFullYear(), now.getMonth() - index, 1));
    }
    return months;
  },
  formattedMonthListFromCurrentDate() {
    const months = this.monthListFromCurrentDate();
    return months.map((month) => this.formatDate(month));
  },

  dateRange(startDate, endDate) {
    const start = startDate.split('-');
    const end = endDate.split('-');
    const startYear = parseInt(start[0], 10);
    const endYear = parseInt(end[0], 10);
    const dates = [];

    for (let i = startYear; i <= endYear; i += 1) {
      const endMonth = i !== endYear ? 11 : parseInt(end[1], 10) - 1;
      const startMon = i === startYear ? parseInt(start[1], 10) - 1 : 0;
      for (let j = startMon; j <= endMonth; j = j > 12 ? j % 12 || 11 : j + 1) {
        const month = j + 1;
        const displayMonth = month < 10 ? `0${month}` : month;
        const date = new Date([i, displayMonth, '01'].join('-'));
        dates.push({
          month: this.monthNames()[month - 1],
          start: date,
          end: new Date(date.getFullYear(), date.getMonth() + 1, 1),
        });
      }
    }
    dates[0].start = startDate;
    dates[dates.length - 1].end = endDate;

    return dates;
  },

  monthNumber() {
    return {
      January: '01',
      February: '02',
      March: '03',
      April: '04',
      May: '05',
      June: '06',
      July: '07',
      August: '08',
      September: '09',
      October: '10',
      November: '11',
      December: '12',
    };
  },
  endMonthNumber() {
    return {
      January: 0,
      February: 1,
      March: 2,
      April: 3,
      May: 4,
      June: 5,
      July: 6,
      August: 7,
      September: 8,
      October: 9,
      November: 10,
      December: 11,
    };
  },
  monthNumberConverted(monthName, type, year) {
    if (type === 'start') {
      const monthNumber = this.endMonthNumber()[monthName];
      const monthDate = new Date(year, monthNumber, 1, 0, 0, 0, 0);
      return monthDate.toISOString();
    }
    if (type === 'end') {
      const monthNumber = this.endMonthNumber()[monthName];
      const monthDate = new Date(year, monthNumber + 1, 0, 23, 59, 59, 59);
      return monthDate.toISOString();
    }
    return this.monthNumber()[monthName];
  },
  getPhDateTime() {
    const PH_OFFSET_MS = 28_800_000;
    return new Date(Date.now() + PH_OFFSET_MS).toISOString();
  },
};
