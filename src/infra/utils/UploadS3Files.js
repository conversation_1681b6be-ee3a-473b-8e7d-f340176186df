const fs = require('fs');
const { S3Client, GetObjectCommand, PutObjectCommand } = require('@aws-sdk/client-s3');
const { Upload } = require('@aws-sdk/lib-storage');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');

let s3Config = {
  region: process.env.AWS_REGION,
  httpOptions: { timeout: 10 * 120 * 1000 },
  forcePathStyle: true,
};

if (process.env.STAGE === 'local') {
  s3Config = {
    ...s3Config,
    endpoint: process.env.AWS_ENDPOINT,
  };
}

const client = new S3Client(s3Config);
const S3UploadFile = async (fileName, filePath, s3BucketPath) => {
  const s3BucketName = process.env.S3_BUCKET_NAME;
  const readStream = fs.createReadStream(`${filePath}/${fileName}`);
  let uploadResult;
  try {
    const streamUpload = new Upload({
      client,
      params: {
        Bucket: s3BucketName,
        Key: `${s3BucketPath}/${fileName}`,
        Body: readStream,
      },
      queueSize: 10,
      partSize: 1024 * 1024 * 5,
      leavePartsOnError: false,
    });
    uploadResult = await streamUpload.done();
  } catch (e) {
    throw new Error(`Failed to upload file to S3 - ${e}`);
  }

  if (typeof uploadResult.ETag !== 'undefined') {
    const urlparams = {
      Bucket: s3BucketName,
      Key: `${s3BucketPath}/${fileName}`,
    };
    const command = new PutObjectCommand(urlparams);
    const url = await getSignedUrl(client, command, { expiresIn: 14400 });
    return {
      status: true,
      url,
      message: `Successful to Upload in S3 Bucket - ${fileName}`,
    };
  }
  return {
    status: false,
    message: `Failed to Upload in S3 Bucket - ${fileName}`,
  };
};

const S3DownloadFile = async (fileName, s3BucketPath, s3BucketName) => {
  try {
    const command = new GetObjectCommand({
      Bucket: s3BucketName,
      Key: `${s3BucketPath}/${fileName}`,
      ResponseContentDisposition: `attachment; filename="${fileName}"`,
    });

    await client.send(command); // checker if file exist
    const urlLink = await getSignedUrl(client, command, { expiresIn: 300 });

    return { urlLink };
  } catch (err) {
    throw new Error(`File not Found ERROR : ${err}`);
  }
};

module.exports = {
  S3UploadFile,
  S3DownloadFile,
};
