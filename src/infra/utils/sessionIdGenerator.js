const microtime = require('microtime');
const crypto = require('crypto');

module.exports = {
  generateSessionId() {
    const timestamp = microtime.now().toString(36);
    const randomPart = crypto
      .randomBytes(16)
      .toString('base64')
      .replace(/[+/=]/g, '')
      .substring(0, 26 - timestamp.length);
    const sessionId = (randomPart + timestamp).substring(0, 26);
    return sessionId;
  },
};
