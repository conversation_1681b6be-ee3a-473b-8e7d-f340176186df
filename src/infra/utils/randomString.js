/**
 * Generates a random string of specified length
 * @param {number} length - The length of the string to generate
 * @returns {string} A random string
 */
const generateRandomString = (length = 24) => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()-_=+';
  let result = '';
  const charactersLength = characters.length;

  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }

  return result;
};

module.exports = {
  generateRandomString,
};
