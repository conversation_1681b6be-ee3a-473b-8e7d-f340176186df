module.exports = {
  consolidateSettelmentBreakdown(reportData) {
    reportData.forEach((record) => {
      record.amountValue = record.settlementBreakdown.reduce(
        (totalAmt, d) => (totalAmt += parseFloat(d.amountValue)),
        0
      );
      record.accountNumber = record.settlementBreakdown[0].accountNumber;
    });
  },

  async withRefundData(reportData, query, filter) {
    const queryPromises = reportData.map(({ paymentId, transactionId }) => {
      return paymentId ? query({ paymentId, transactionId }) : null;
    });

    const results = (await Promise.all(queryPromises)).flat();

    const refundMap = new Map();
    results.forEach((refund) => {
      if (refund) {
        const refundCompositeKey = `${refund.paymentId}-${refund.transactionId ?? ''}`;
        refundMap.set(refundCompositeKey, refund);
      }
    });

    reportData.forEach((record) => {
      const settlementCompositeKey = `${record.paymentId}-${record.transactionId ?? ''}`;
      if (record && refundMap.has(settlementCompositeKey)) {
        const refundData = refundMap.get(settlementCompositeKey);
        if (refundData) {
          const { refundApprovalStatus, refundStatus, refundReason, refundAmount, createDateTime } = refundData;

          record.refundApprovalStatus = refundApprovalStatus ?? '-';
          record.refundStatus = refundStatus ?? '-';
          record.refundReason = refundReason ?? '-';
          record.refundAmount = refundAmount ?? '-';
          record.refundDate = createDateTime;
        }
      }
    });

    let filteredReportData = reportData;
    if (filter) {
      const { refundStatus } = filter;
      if (refundStatus) {
        filteredReportData = reportData.filter((data) => data.refundApprovalStatus === refundStatus);
      }
    }
    return filteredReportData;
  },

  async injectChannelName(reportData, query) {
    const uniqueStringChannelIds = [
      ...new Set(reportData.filteredData.map((record) => record.channelId).filter((id) => id)),
    ];
    const keysForBatchGet = uniqueStringChannelIds.map((id) => ({ id }));
    const channels = keysForBatchGet && keysForBatchGet.length >= 1 ? await query(keysForBatchGet) : [];
    const channelInfoMap = new Map();
    if (channels && Array.isArray(channels)) {
      channels.forEach((channel) => {
        if (channel && channel.id) {
          channelInfoMap.set(channel.id, {
            channelName: channel.name,
          });
        }
      });
    }
    reportData.filteredData.forEach((record) => {
      if (record && record.channelId) {
        const correspondingChannelInfo = channelInfoMap.get(record.channelId);
        record.channelName = correspondingChannelInfo ? correspondingChannelInfo.channelName : '';
      }
      record.postedTimestamp = record.createDateTime;
    });
    return reportData;
  },

  async injectTransLogData(reportData, filters, repo) {
    const transLogPromises = reportData.map(({ paymentId }) => {
      return paymentId ? repo.getByPaymentId(paymentId) : Promise.resolve(null);
    });

    const results = await Promise.allSettled(transLogPromises);
    reportData.forEach((report, index) => {
      const promiseResult = results[index];
      if (promiseResult.status === 'fulfilled' && promiseResult.value) {
        report.channelId = promiseResult.value[0]?.channelId;
        report.postedTimestamp = promiseResult.value[0]?.createDateTime;
      } else {
        report.channelId = null;
      }
      report.finalAmount = report.amount;
    });
    if (filters) {
      const { channelId, userAssignedChannels } = filters;
      reportData = reportData.filter((record) => {
        if (channelId) {
          return record.channelId === channelId;
        } else if (userAssignedChannels) {
          return userAssignedChannels.includes(record.channelId);
        }
      });
    }
    return reportData;
  },

  async withSettlementData(reportData, query) {
    const queryPromises = reportData.map(({ paymentId }) => {
      return paymentId ? query(paymentId) : null;
    });
    const results = (await Promise.all(queryPromises)).flat();
    const formattedReport = [];
    reportData.forEach((record, index) => {
      if (record.settlementBreakdown && Array.isArray(record.settlementBreakdown)) {
        const [{ transactionType, amountValue }] = record.settlementBreakdown;
        switch (transactionType) {
          case 'N':
            formattedReport.push({ ...record, finalAmount: amountValue });
            break;
          case 'G': {
            const filtered = results.filter((d) => d.paymentId === record.paymentId);
            // iterate filtered
            filtered.forEach((f) => {
              const newRecord = {
                ...record,
                ...f, // You might want to customize what to take from `f`
                settlementAmountValue: f.amount ?? '0',
                settlementEmailAddress: f.emailAddress,
                settlementBrand: f.brand,
                settlementAccountType: f.accountType,
                settlementAccountNumber: f.accountNumber,
                settlementMobileNumber: f.mobileNumber,
                settlementTransactionType: f.transactionType,
                finalAmount: f.amount || 0,
                accountNumber: f.accountId,
              };
              formattedReport.push(newRecord);
            });

            break;
          }
        }
      }
    });
    return formattedReport;
  },
};
