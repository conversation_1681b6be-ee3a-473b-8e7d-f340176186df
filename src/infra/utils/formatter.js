const { KAFKA_DEFAULT_CONFIG } = require('./constants');

module.exports = {
  formatNumberToDecimal(num) {
    return Number(num.toFixed(2));
  },

  transformPayloadToLowerCase(payload = {}, excluded = []) {
    const lowercaseObject = {};
    Object.entries(payload).forEach(([key, value]) => {
      let data = value;
      if (typeof data === 'string' && !excluded.includes(key)) {
        data = value.toLowerCase();
      }
      lowercaseObject[key] = data;
    });
    return lowercaseObject;
  },

  createCompositeKey(payload = {}, excludeTransform = []) {
    return JSON.stringify(this.transformPayloadToLowerCase(payload, [...excludeTransform]));
  },

  transformOptions(config) {
    const { globalConfig, topicConfig } = KAFKA_DEFAULT_CONFIG;
    const options = config.kafka;
    if (config.env === 'local') {
      return {
        globalConfig: {
          'metadata.broker.list': options.brokers,
          ...globalConfig,
          ...options.globalConfig.topics,
        },
        schemaRegistry: {},
      };
    }

    return {
      globalConfig: {
        'ssl.endpoint.identification.algorithm': options.ssl.identificationAlgo,
        ...globalConfig, // default global configs
        ...options.globalConfig, // include additional properties
        ...options.sasl,
      },
      topicConfig: {
        ...topicConfig, // default topic configs
        ...options.topicConfig, // include additional properties
      },
      schemaRegistry: {
        ...options.schemaRegistry,
      },
    };
  },

  kafkaMessageMapper(topic, eventType, paymentId, event, schemaVersion) {
    const message = {
      topic,
      headers: [{ eventType }],
      payload: { eventType, event, paymentId },
    };

    if (schemaVersion) {
      message.schema = {
        version: schemaVersion,
      };
    }

    return message;
  },

  async refundApprovedPayloadLoop(payload, refundStats, refundPayload) {
    // sample usage
    // const refundApprovedStats = async (snapshots) => {
    //   await snapshots.forEach(async (snapshot) => {
    //     await refundApprovedPayloadLoop(payload, refundStats,JSON.parse(snapshot.payload));
    //   });
    // };

    if (payload.channelId) {
      const { channelId } = payload;
      if (refundPayload[channelId]) {
        if (refundPayload[channelId].xendit) {
          if (refundPayload[channelId].xendit.bill) {
            if (refundStats.bill[channelId]) {
              refundStats.bill[channelId].totalApprovedRefundAmount +=
                refundPayload[channelId].xendit.bill.totalApprovedRefundAmount;
              refundStats.bill[channelId].totalApprovedRefundCount +=
                refundPayload[channelId].xendit.bill.totalApprovedRefundCount;
            } else if (
              refundPayload[channelId].xendit.bill.totalApprovedRefundAmount !== 0 ||
              refundPayload[channelId].xendit.bill.totalApprovedRefundCount !== 0
            ) {
              refundStats.bill[channelId] = {
                ...refundPayload[channelId].xendit.bill,
                channelName: refundPayload[channelId].name,
                totalForApprovalAmount: 0,
                totalForApprovalCount: 0,
                totalAutoRefundAmount: 0,
                totalAutoRefundCount: 0,
              };
            }
          }
          if (refundPayload[channelId].xendit.nonbill) {
            if (refundStats.nonbill[channelId]) {
              refundStats.nonbill[channelId].totalApprovedRefundAmount +=
                refundPayload[channelId].xendit.nonbill.totalApprovedRefundAmount;
              refundStats.nonbill[channelId].totalApprovedRefundCount +=
                refundPayload[channelId].xendit.nonbill.totalApprovedRefundCount;
            } else if (
              refundPayload[channelId].xendit.nonbill.totalApprovedRefundAmount !== 0 ||
              refundPayload[channelId].xendit.nonbill.totalApprovedRefundCount !== 0
            ) {
              refundStats.nonbill[channelId] = {
                ...refundPayload[channelId].xendit.nonbill,
                channelName: refundPayload[channelId].name,
                totalForApprovalAmount: 0,
                totalForApprovalCount: 0,
                totalAutoRefundAmount: 0,
                totalAutoRefundCount: 0,
              };
            }
          }
        }
      }
    } else {
      const keys = Object.keys(refundPayload);
      keys.forEach((key) => {
        if (refundPayload[key].xendit) {
          if (refundPayload[key].xendit.bill) {
            if (refundStats.bill[key]) {
              refundStats.bill[key].totalApprovedRefundAmount +=
                refundPayload[key].xendit.bill.totalApprovedRefundAmount;
              // eslint-disable-next-line max-len
              refundStats.bill[key].totalApprovedRefundCount += refundPayload[key].xendit.bill.totalApprovedRefundCount;
            } else if (
              refundPayload[key].xendit.bill &&
              (refundPayload[key].xendit.bill.totalApprovedRefundAmount !== 0 ||
                refundPayload[key].xendit.bill.totalApprovedRefundCount !== 0)
            ) {
              refundStats.bill[key] = {
                ...refundPayload[key].xendit.bill,
                channelName: refundPayload[key].name,
                totalForApprovalAmount: 0,
                totalForApprovalCount: 0,
                totalAutoRefundAmount: 0,
                totalAutoRefundCount: 0,
              };
            }
          }
          if (refundPayload[key].xendit.nonbill) {
            if (refundStats.nonbill[key]) {
              refundStats.nonbill[key].totalApprovedRefundAmount +=
                refundPayload[key].xendit.nonbill.totalApprovedRefundAmount;
              refundStats.nonbill[key].totalApprovedRefundCount +=
                refundPayload[key].xendit.nonbill.totalApprovedRefundCount;
            } else if (
              refundPayload[key].xendit.nonbill &&
              (refundPayload[key].xendit.nonbill.totalApprovedRefundAmount !== 0 ||
                refundPayload[key].xendit.nonbill.totalApprovedRefundCount !== 0)
            ) {
              refundStats.nonbill[key] = {
                ...refundPayload[key].xendit.nonbill,
                channelName: refundPayload[key].name,
                totalForApprovalAmount: 0,
                totalForApprovalCount: 0,
                totalAutoRefundAmount: 0,
                totalAutoRefundCount: 0,
              };
            }
          }
        }
      });
    }
  },

  async refundAutoPayloadLoop(payload, refundStats, refundPayload) {
    // sample usage
    // const refundAutoStats = async (snapshots) => {
    //   await snapshots.forEach(async (snapshot) => {
    //     await refundAutoPayloadLoop(JSON.parse(snapshot.payload));
    //   });
    // };
    if (payload.channelId) {
      const { channelId } = payload;
      if (refundPayload[channelId]) {
        if (refundPayload[channelId].xendit) {
          if (refundPayload[channelId].xendit.bill) {
            if (refundStats.bill[channelId]) {
              refundStats.bill[channelId].totalAutoRefundAmount +=
                refundPayload[channelId].xendit.bill.totalApprovedRefundAmount;
              refundStats.bill[channelId].totalAutoRefundCount +=
                refundPayload[channelId].xendit.bill.totalApprovedRefundCount;
            } else if (
              refundPayload[channelId].xendit.bill.totalApprovedRefundAmount !== 0 ||
              refundPayload[channelId].xendit.bill.totalApprovedRefundCount !== 0
            ) {
              // eslint-disable-next-line max-len
              const { totalApprovedRefundAmount, totalApprovedRefundCount } = refundPayload[channelId].xendit.bill;
              refundStats.bill[channelId] = {
                totalAutoRefundAmount: totalApprovedRefundAmount,
                totalAutoRefundCount: totalApprovedRefundCount,
                channelName: refundPayload[channelId].name,
                totalApprovedRefundAmount: 0,
                totalApprovedRefundCount: 0,
                totalForApprovalAmount: 0,
                totalForApprovalCount: 0,
              };
            }
          }
          if (refundPayload[channelId].xendit.nonbill) {
            if (refundStats.nonbill[channelId]) {
              refundStats.nonbill[channelId].totalAutoRefundAmount +=
                refundPayload[channelId].xendit.nonbill.totalApprovedRefundAmount;
              refundStats.nonbill[channelId].totalAutoRefundCount +=
                refundPayload[channelId].xendit.nonbill.totalApprovedRefundCount;
            } else if (
              refundPayload[channelId].xendit.nonbill.totalApprovedRefundAmount !== 0 ||
              refundPayload[channelId].xendit.nonbill.totalApprovedRefundCount !== 0
            ) {
              /* eslint max-len: ["error", { "code": 110 }] */
              const AtRfndAmnt = refundPayload[channelId].xendit.nonbill.totalApprovedRefundAmount;
              const AtRfundCnt = refundPayload[channelId].xendit.nonbill.totalApprovedRefundCount;
              refundStats.nonbill[channelId] = {
                totalAutoRefundAmount: AtRfndAmnt,
                totalAutoRefundCount: AtRfundCnt,
                channelName: refundPayload[channelId].name,
                totalApprovedRefundAmount: 0,
                totalApprovedRefundCount: 0,
                totalForApprovalAmount: 0,
                totalForApprovalCount: 0,
              };
            }
          }
        }
      }
    } else {
      const keys = Object.keys(refundPayload);
      keys.forEach((key) => {
        if (refundPayload[key].xendit) {
          if (refundPayload[key].xendit.bill) {
            if (refundStats.bill[key]) {
              // eslint-disable-next-line max-len
              refundStats.bill[key].totalAutoRefundAmount += refundPayload[key].xendit.bill.totalApprovedRefundAmount;
              // eslint-disable-next-line max-len
              refundStats.bill[key].totalAutoRefundCount += refundPayload[key].xendit.bill.totalApprovedRefundCount;
            } else if (
              refundPayload[key].xendit.bill &&
              (refundPayload[key].xendit.bill.totalApprovedRefundAmount !== 0 ||
                refundPayload[key].xendit.bill.totalApprovedRefundCount !== 0)
            ) {
              const AtRefundAmnt = refundPayload[key].xendit.bill.totalApprovedRefundAmount;
              const AtRefundCount = refundPayload[key].xendit.bill.totalApprovedRefundCount;
              refundStats.bill[key] = {
                totalAutoRefundAmount: AtRefundAmnt,
                totalAutoRefundCount: AtRefundCount,
                channelName: refundPayload[key].name,
                totalForApprovalAmount: 0,
                totalForApprovalCount: 0,
              };
            }
          }
          if (refundPayload[key].xendit.nonbill) {
            if (refundStats.nonbill[key]) {
              refundStats.nonbill[key].totalAutoRefundAmount +=
                refundPayload[key].xendit.nonbill.totalApprovedRefundAmount;
              refundStats.nonbill[key].totalAutoRefundCount +=
                refundPayload[key].xendit.nonbill.totalApprovedRefundCount;
            } else if (
              refundPayload[key].xendit.nonbill &&
              (refundPayload[key].xendit.nonbill.totalApprovedRefundAmount !== 0 ||
                refundPayload[key].xendit.nonbill.totalApprovedRefundCount !== 0)
            ) {
              const AtRefundAmnt = refundPayload[key].xendit.nonbill.totalApprovedRefundAmount;
              const AtRefundCount = refundPayload[key].xendit.nonbill.totalApprovedRefundCount;
              refundStats.nonbill[key] = {
                totalAutoRefundAmount: AtRefundAmnt,
                totalAutoRefundCount: AtRefundCount,
                channelName: refundPayload[key].name,
                totalForApprovalAmount: 0,
                totalForApprovalCount: 0,
                totalApprovedRefundAmount: 0,
                totalApprovedRefundCount: 0,
              };
            }
          }
        }
      });
    }
  },

  formatDateForQuery(dateInput) {
    // Return null or handle invalid input gracefully
    if (!dateInput) {
      return null;
    }

    // The built-in Date constructor can handle ISO strings and Date objects
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) {
      return null;
    }

    // PH Time options
    const options = {
      timeZone: 'Asia/Manila',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false, // Use 24-hour format
    };

    return date.toLocaleString('sv-SE', options);
  },

  determineRefundStatus(amountValue, refundAmount, remainingBalance) {
    return parseFloat(amountValue) > parseFloat(refundAmount) &&
      parseFloat(refundAmount) !== parseFloat(remainingBalance)
      ? 'Partial Refund'
      : 'Full Refund';
  },

  calculateRefundBalance(transAmount, totalRefund) {
    return parseFloat(transAmount) - parseFloat(totalRefund);
  },
};
