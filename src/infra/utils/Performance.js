const os = require('os');
const osu = require('node-os-utils');

module.exports = {
  async cpuUsage() {
    const cpuUsage = osu.cpu;
    return cpuUsage.usage().then((info) => `${info} %`);
  },
  totalmem() {
    return os.totalmem();
  },
  freemem() {
    return os.freemem();
  },

  usedMemory() {
    const totalMem = this.totalmem();
    const freeMem = this.freemem();
    const usedMem = ((totalMem - freeMem) / 1024 / 1024 / 1024).toFixed(2);
    return `${usedMem} GB`;
  },
};
