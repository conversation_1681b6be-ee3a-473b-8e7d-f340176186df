module.exports = {
  encode(str) {
    return Buffer.from(str).toString('base64');
  },
  decode(str) {
    return Buffer.from(str, 'base64').toString('ascii');
  },
  encodeBase64Url(base64) {
    // eslint-disable-next-line no-useless-escape
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/\=+$/, '');
  },
  decodeBase64Url(base64url) {
    const base64 = `${base64url}===`.slice(0, base64url.length + (base64url.length % 4));
    return base64.replace(/-/g, '+').replace(/_/g, '/');
  },
};
