module.exports = {
  CREATE_REFUND_SESSION: 'CREATE_REFUND_SESSION',
  PARTIALLY_REFUNDED: 'PARTIALLY_REFUNDED',
  FULLY_REFUNDED: 'FULLY_REFUNDED',
  SUCCESS_CREATE_REFUND_SESSION: 'SUCCESS_CREATE_REFUND_SESSION',
  REFUND_ACTION_STATUSES: {
    REJECTED: 'Rejected',
    APPROVED: 'Approved',
    REQUESTED: 'REQUESTED',
    FOR_APPROVAL: 'For Approval',
  },
  KAFKA_DEFAULT_CONFIG: {
    globalConfig: {
      'enable.idempotence': true,
      retries: 3,
      'retry.backoff.ms': 1000,
      'max.in.flight': 1,
      'queuing.strategy': 'fifo',
      debug: 'broker,msg',
      dr_cb: true,
    },
    topicConfig: {
      acks: -1,
      'delivery.timeout.ms': 50000,
      'request.timeout.ms': 30000,
    },
  },
};
