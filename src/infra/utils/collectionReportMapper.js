/**
 * Collection Record Transformation
 * - consolidate redundant month records
 * - accumulate amount for same types (company and mid)
 * @param {array} collectionDetails
 * @param {object} data
 * @param {string} type
 * @returns {array}
 */
const collectionReportMapper = (collectionDetails, data, type) => {
  const collectionDetailsCopy = [...collectionDetails];
  const detailIndex = collectionDetailsCopy.findIndex((detail) => {
    const isSameMonth = data.month === detail.month;
    const isSameYear = data.year === detail.year;

    return isSameMonth && isSameYear;
  });

  if (detailIndex === -1) {
    collectionDetailsCopy.push(data);
  }

  if (detailIndex !== -1) {
    data.payload.forEach((record) => {
      const recordIndex = collectionDetailsCopy[detailIndex].payload.findIndex((detail) => {
        /* Company */
        const isSameRecord =
          record.name === detail.name &&
          record.depositoryBankAccount === detail.depositoryBankAccount &&
          record.costCenter === detail.costCenter;

        /* MID */
        if (type === 'mid') {
          return isSameRecord && record.merchantCompany === detail.merchantCompany;
        }

        return isSameRecord;
      });

      if (recordIndex !== -1) {
        collectionDetailsCopy[detailIndex].payload[recordIndex].transAmount += record.transAmount;
        collectionDetailsCopy[detailIndex].payload[recordIndex].transCount += record.transCount;
        collectionDetailsCopy[detailIndex].payload[recordIndex].bankCharge += record.bankCharge;
      } else {
        collectionDetailsCopy[detailIndex].payload.push(record);
      }
    });
  }

  return collectionDetailsCopy;
};

module.exports = collectionReportMapper;
