/**
 *
 * @param {*} source ex.
 * @param {*} attributeName  ex. Role
 * @param {*} target
 */
const Mapper = (sources, targets, attributeName, attributeId) =>
  sources.map((source) => {
    for (let index = 0; index < targets.length; index += 1) {
      if (typeof source[attributeId] !== 'undefined' && source[attributeId] === targets[index].id) {
        const data = Object.assign(source, { [attributeName]: targets[index] });
        delete data[attributeId];
        return data;
      }
    }
    return Object.assign(source, { [attributeName]: [] });
  });

module.exports = Mapper;
