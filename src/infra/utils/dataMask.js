/**
 * For Binding ID Token Mask all except
 * for the first 8 and last 4 characters
 * @param {string} input e.g. 2023072511111111111111111111111111114528
 * @param {number} firstCharsLength e.g. 8
 * @param {number} lastCharsLength e.g. 4
 * @returns {string} e.g. 20230725****************************4528
 */
const maskMiddleData = (input = '', firstCharsLength = 0, lastCharsLength = 0) => {
  const inputLength = input.length;
  const exposedCharsLength = firstCharsLength + lastCharsLength;

  if (inputLength <= exposedCharsLength) {
    return input;
  }

  const firstChars = input.slice(0, firstCharsLength);
  const lastChars = input.slice(-lastCharsLength);
  const maskedPart = '*'.repeat(inputLength - exposedCharsLength);

  return firstChars + maskedPart + lastChars;
};

module.exports = {
  maskMiddleData,
};
