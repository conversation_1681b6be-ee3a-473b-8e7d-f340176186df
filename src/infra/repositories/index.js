const fs = require('fs');
const { asClass } = require('awilix');

if (!process.env.PWD) {
  process.env.PWD = process.cwd();
}
const repositories = {};
fs.readdirSync(`${process.env.PWD}/src/infra/repositories`)
  .filter(
    (file) => file.indexOf('.') !== 0 && file !== 'index.js' && file !== 'BaseRepository.js' && file.slice(-3) === '.js'
  )
  .forEach((file) => {
    const repositoryName = file.split('.')[0];
    const repository = require(`./${file}`);
    repositories[repositoryName.replace(/^./, (f) => f.toLowerCase())] = asClass(repository).singleton();
  });

module.exports = repositories;
