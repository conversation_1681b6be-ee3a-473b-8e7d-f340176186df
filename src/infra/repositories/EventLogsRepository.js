const { getPhDateTime } = require('../utils/date');
const BaseRepository = require('./BaseRepository');

class EventLogsRepository extends BaseRepository {
  constructor({ EventModel }) {
    super(EventModel);
  }

  async registerEventLog(event) {
    await this.model.create({
      ...event,
      eventSource: 'gpayo-cms-backend-service',
      createDateTime: getPhDateTime(),
    });
  }

  async registerFailedEvent({
    paymentId,
    eventId,
    channelId,
    eventName,
    errorMessage,
    paymentMethod = '',
    traceparent,
  }) {
    await this.registerEventLog({
      paymentId,
      eventId,
      channelId,
      eventName,
      errorMessage,
      traceparent,
      paymentMethod,
    });
  }
}

module.exports = EventLogsRepository;
