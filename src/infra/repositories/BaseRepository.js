class BaseRepository {
  constructor(model) {
    this.model = model;
  }

  async batchGet(ids) {
    return this.model.batchGet(ids);
  }

  async batchDelete(ids) {
    return this.model.batchDelete(ids);
  }

  async batchPut(data) {
    const max = 25;
    const groupedResult = [];
    for (let i = 0; i < data.length; i += max) {
      const items = data.slice(i, i + max);
      const result = this.model.batchPut(items);
      groupedResult.push(result);
    }

    const batchResult = await Promise.all(groupedResult);
    return batchResult.flat();
  }

  async getAll(args) {
    return this.model.scan(args).exec();
  }

  async whereIn(ids) {
    const max = 100;
    const groupedData = [];
    for (let i = 0; i < ids.length; i += max) {
      const x = ids.slice(i, i + max);
      const data = this.model.scan('id').in(x).exec();
      groupedData.push(data);
    }

    const batchResult = await Promise.all(groupedData);
    return batchResult.flat();
  }

  async getById(id) {
    return this.model.get(id);
  }

  async add(data) {
    return this.model.create(data);
  }

  async put(data) {
    return this.model.batchPut([data]);
  }

  async remove(id) {
    return this.model.delete(id);
  }

  async update(id, newData) {
    if (newData?.id) {
      delete newData.id;
    }
    return this.model.update(id, newData);
  }

  async count() {
    return this.model.query.count();
  }

  searchScan(data) {
    const scan = this.model.scan();
    Object.keys(data.filter).forEach((key) => {
      scan.filter(key).contains(data.filter[key]);
    });
    return scan;
  }

  async searchFilter(data) {
    const scan = this.searchScan(data);
    if (typeof data.pagination.start !== 'undefined' && data.pagination.start !== '') {
      return scan.startAt({ id: { S: data.pagination.start } }).exec();
    }
    return scan.exec();
  }

  async searchFilterNoPaginate(data) {
    const scan = this.searchScan(data);
    return scan.attributes(['id']).exec();
  }

  async getAllData() {
    const scan = this.model.scan();
    return scan.exec();
  }

  async startAt(start, limit) {
    return this.model
      .scan()
      .startAt({ id: { S: start } })
      .limit(limit)
      .exec();
  }

  async listAll(data) {
    const scan = this.model.scan();
    if (typeof data.pagination.startKey !== 'undefined' && data.pagination.startKey !== '') {
      return scan
        .startAt({ id: { S: data.pagination.startKey } })
        .limit(data.pagination.limit)
        .exec();
    }
    return scan.limit(data.pagination.limit).exec();
  }

  async listAllQuery(data, queryName, value) {
    const query = this.model.query(queryName).eq(value);
    if (data.pagination.startKeys !== '') {
      query.startAt(JSON.parse(data.pagination.startKeys));
    }
    return query.limit(data.pagination.limit).sort('descending').exec();
  }

  async getAllId() {
    return this.model.scan().attributes(['id']).exec();
  }
}

module.exports = BaseRepository;
