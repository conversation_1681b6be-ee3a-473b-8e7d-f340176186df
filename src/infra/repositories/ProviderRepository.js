const BaseRepository = require('./BaseRepository');

class ProviderRepository extends BaseRepository {
  constructor({ ChannelModel }) {
    super(ChannelModel);
  }

  providerQuery(data) {
    const scan = this.model.scan().where('isVerified').eq(true);
    Object.keys(data.filter).forEach((key) => {
      if (key === 'createdAt') {
        if (data.filter[key].start === null) {
          scan.where('createdAt').le(data.filter[key].end);
        } else if (data.filter[key].end === null) {
          scan.where('createdAt').ge(data.filter[key].start);
        } else {
          scan.where('createdAt').between(data.filter[key].start, data.filter[key].end);
        }
      } else if (key === 'isEnabled') {
        scan.where('isEnabled').eq(data.filter[key]);
      } else {
        scan.filter(key).contains(data.filter[key]);
      }
    });
    return scan;
  }

  async searchFilter(data) {
    const scan = this.providerQuery(data);
    if (typeof data.pagination.start !== 'undefined' && data.pagination.start !== '') {
      return scan.startAt({ id: { S: data.pagination.start } }).exec();
    }
    return scan.exec();
  }

  async searchFilterNoPaginate(data) {
    const scan = this.providerQuery(data);
    return scan.exec();
  }

  async listAll(data) {
    const scan = this.model.scan('isVerified').eq(true);
    if (typeof data.pagination.start !== 'undefined' && data.pagination.start !== '') {
      return scan.startAt({ id: { S: data.pagination.start } }).exec();
    }
    return scan.exec();
  }

  async getAllId() {
    const scan = this.model.scan().where('isVerified').eq(true);
    return scan.exec();
  }
}

module.exports = ProviderRepository;
