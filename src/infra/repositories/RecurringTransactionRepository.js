const BaseRepository = require('./BaseRepository');

class RecurringTransactionRepository extends BaseRepository {
  constructor({ RecurringTransactionModel }) {
    super(RecurringTransactionModel);
  }

  searchQuery(data, queryName, value) {
    let query;
    const filters = data.filter;
    filters.entityName = value;
    if (filters.id) {
      query = this.model.query('id').eq(filters.id);
      delete filters.id;
    } else if (filters.accountNumber) {
      query = this.model.query('accountNumber').eq(filters.accountNumber);
      delete filters.accountNumber;
    } else if (filters.channelId) {
      query = this.model.query('channelId').eq(filters.channelId);
      delete filters.channelId;
    } else if (filters.entity) {
      query = this.model.query('entity').eq(filters.entity);
      delete filters.entity;
    } else if (filters.status) {
      query = this.model.query('status').eq(filters.status);
      delete filters.status;
    } else if (filters.userContact) {
      query = this.model.query('userContact').eq(filters.userContact);
      delete filters.userContact;
    } else {
      query = this.model.query(queryName).eq(value);
      delete filters.entityName;
    }
    query.sort('descending');
    if (filters.timestamp) {
      const { start, end } = filters.timestamp;
      query.where('timestamp').between(start, end);
      delete filters.timestamp;
    }
    Object.keys(data.filter).forEach((key) => {
      query.filter(key).eq(data.filter[key]);
    });
    return query;
  }

  async searchFilter(data, queryName, value) {
    const query = this.searchQuery(data, queryName, value);
    if (data.pagination.startKeys !== '') {
      query.startAt(JSON.parse(data.pagination.startKeys));
    }
    return query.exec();
  }
}

module.exports = RecurringTransactionRepository;
