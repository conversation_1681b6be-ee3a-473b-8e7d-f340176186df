const BaseRepository = require('./BaseRepository');

class PayByLinkRepository extends BaseRepository {
  constructor({ PayByLinkModel }) {
    super(PayByLinkModel);
  }

  async createPayByLinkReportEntry(data) {
    const {
      channelId,
      paymentId,
      paymentGateway,
      paymentLink,
      status,
      merchantAccount,
      merchantReference,
      amount,
      description,
      linkType,
    } = data;

    return this.model.create({
      id: paymentId,
      channelId,
      paymentGateway,
      paymentLink,
      amount,
      description,
      status,
      merchantAccount,
      merchantReference,
      linkType,
    });
  }

  async getByPaymentId(id, filter) {
    const query = this.model.query('id').eq(id);

    if (filter?.merchantReference) {
      query.filter('merchantReference').eq(filter.merchantReference);
    }

    if (filter?.status) {
      query.filter('status').eq(filter.status);
    }

    return query.exec();
  }

  async getAll(data) {
    const query = this.model.query('sortKey').using('sortKeyIndex').eq('paybylink');

    if (data.filter?.merchantReference) {
      query.filter('merchantReference').eq(data.filter.merchantReference);
    }

    if (data.filter?.status) {
      query.filter('status').using('statusIndex').eq(data.filter.status);
    }

    if (data.pagination.startKey) {
      query.startAt(JSON.parse(data.pagination.startKey));
    }

    return query.sort('descending').limit(data.pagination.limit).exec();
  }
}

module.exports = PayByLinkRepository;
