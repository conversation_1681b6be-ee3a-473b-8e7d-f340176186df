const BaseRepository = require('./BaseRepository');

class DigitalPortalTransactionRepository extends BaseRepository {
  constructor({ DigitalPortalTransactionModel }) {
    super(DigitalPortalTransactionModel);
  }

  searchQuery(data) {
    let query;
    const filters = data.filter;
    if (Object.prototype.hasOwnProperty.call(filters, 'psReferenceNo')) {
      query = this.model.query('id').eq(filters.psReferenceNo);
      delete filters.psReferenceNo;
    } else if (Object.prototype.hasOwnProperty.call(filters, 'gcashRerefenceNo')) {
      query = this.model.query('gcashReferenceNo').eq(filters.gcashRerefenceNo);
      delete filters.gcashRerefenceNo;
    } else if (Object.prototype.hasOwnProperty.call(filters, 'accountNo')) {
      query = this.model.query('accountNo').eq(filters.accountNo);
      delete filters.accountNo;
    } else if (Object.prototype.hasOwnProperty.call(filters, 'msisdn')) {
      query = this.model.query('msisdn').eq(filters.msisdn);
      delete filters.msisdn;
    } else if (Object.prototype.hasOwnProperty.call(filters, 'brand')) {
      query = this.model.query('brand').eq(filters.brand);
      delete filters.brand;
    } else if (Object.prototype.hasOwnProperty.call(filters, 'paymentStatus')) {
      query = this.model.query('paymentStatus').eq(filters.paymentStatus);
      delete filters.paymentStatus;
    } else if (Object.prototype.hasOwnProperty.call(filters, 'paymentGateway')) {
      query = this.model.query('paymentGateway').eq(filters.paymentGateway);
      delete filters.paymentGateway;
    } else {
      query = this.model.query('content').eq('voucher');
    }
    query.sort('descending');
    Object.entries(filters).forEach((filter) => {
      const [filterKey, filterValue] = filter;
      if (filterKey === 'date') {
        const { start, end } = data.filter.date;
        query.where(filterKey).between(start, end);
      } else {
        query.filter(filterKey).eq(filterValue);
      }
    });
    return query;
  }

  async searchFilter(data) {
    const query = this.searchQuery(data);
    if (data.pagination.startKeys !== '') {
      query.startAt(JSON.parse(data.pagination.startKeys));
    }
    query.limit(data.pagination.limit);
    return query.exec();
  }
}

module.exports = DigitalPortalTransactionRepository;
