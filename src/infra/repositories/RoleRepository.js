const BaseRepository = require('./BaseRepository');

class RoleRepository extends BaseRepository {
  constructor({ RoleModel }) {
    super(RoleModel);
  }

  async getById(id) {
    return this.model.query('id').eq(id).exec();
  }

  async getAllName() {
    return this.model.scan().all().attributes(['id', 'name', 'code']).exec();
  }

  async getByRoleCode(code) {
    return this.model.scan('code').all().eq(code).exec();
  }

  rolesQuery(data) {
    const scan = this.model.scan();
    Object.keys(data.filter).forEach((key) => {
      if (key === 'createdAt') {
        if (data.filter[key][0].start === null) {
          scan.filter('createdAt').le(data.filter[key][0].end);
        } else if (data.filter[key][0].end === null) {
          scan.filter('createdAt').ge(data.filter[key][0].start);
        } else {
          scan.filter('createdAt').between(data.filter[key][0].start, data.filter[key][0].end);
        }
      } else if (key === 'numberOfUsers') {
        if (data.filter[key][0].operator === '>') {
          scan.filter('numberOfUsers').gt(data.filter[key][0].number);
        } else if (data.filter[key][0].operator === '<') {
          scan.filter('numberOfUsers').lt(data.filter[key][0].number);
        } else {
          scan.filter('numberOfUsers').eq(data.filter[key][0].number);
        }
      } else if (key === 'isActive') {
        scan.filter('isActive').eq(data.filter[key]);
      } else {
        scan.filter(key).contains(data.filter[key]);
      }
    });
    return scan;
  }

  async searchFilter(data) {
    const scan = this.rolesQuery(data);
    if (typeof data.pagination.start !== 'undefined' && data.pagination.start !== '') {
      return scan.startAt({ id: { S: data.pagination.start } }).exec();
    }
    return scan.exec();
  }

  async searchFilterNoPaginate(data) {
    const scan = this.rolesQuery(data);
    return scan.exec();
  }
}

module.exports = RoleRepository;
