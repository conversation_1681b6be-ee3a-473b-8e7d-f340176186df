const BaseRepository = require('./BaseRepository');

class UserRepository extends BaseRepository {
  constructor({ UserModel, RoleModel, logger }) {
    super(UserModel);
    this.RoleModel = RoleModel;
    this.logger = logger;
  }

  async getByEmail(email) {
    return this.model.scan('email').contains(email).exec();
  }

  async getUserByEmail(email) {
    return this.model
      .scan('email')
      .eq(email)
      .exec()
      .then((user) => {
        if (user.count !== 0) {
          return user[0].populate({
            path: 'roleId',
            model: `pgw${process.env.STAGE}-roles`,
          });
        }
        throw new Error('No Role Found on User');
      });
  }

  async filteredCount(args) {
    const scan = this.model.scan({
      roleId: { eq: args.roleId },
    });
    return scan.count().exec();
  }

  async filterRole(args) {
    const scan = this.model.scan({
      roleId: { eq: args.roleId },
    });
    return scan.exec();
  }

  async getInactiveUser(data) {
    return this.model
      .scan()
      .where('isActive')
      .eq(false)
      .where('isActiveAt')
      .between(new Date(data.start).toISOString(), new Date(data.end).toISOString())
      .count()
      .exec();
  }

  async getActiveUser(data) {
    return this.model
      .scan()
      .where('isActive')
      .eq(true)
      .where('isActiveAt')
      .between(new Date(data.start).toISOString(), new Date(data.end).toISOString())
      .count()
      .exec();
  }

  usersQuery(data) {
    const scan = this.model.scan();
    Object.keys(data.filter).forEach((key) => {
      if (key === 'createdAt') {
        if (data.filter[key][0].start === null) {
          scan.filter('createdAt').le(data.filter[key][0].end);
        } else if (data.filter[key][0].end === null) {
          scan.filter('createdAt').ge(data.filter[key][0].start);
        } else {
          scan.filter('createdAt').between(data.filter[key][0].start, data.filter[key][0].end);
        }
      } else if (key === 'roleId') {
        scan.filter('roleId').in(data.filter[key]);
      } else if (key === 'isActive') {
        scan.filter('isActive').eq(data.filter[key]);
      } else {
        scan.filter(key).contains(data.filter[key]);
      }
    });
    return scan;
  }

  async searchFilter(data) {
    const scan = this.usersQuery(data);
    if (typeof data.pagination.start !== 'undefined' && data.pagination.start !== '') {
      return scan.startAt({ id: { S: data.pagination.start } }).exec();
    }
    return scan.exec();
  }

  async searchFilterNoPaginate(data) {
    const scan = this.usersQuery(data);
    return scan.attributes(['id']).exec();
  }

  async getById(id) {
    return this.model.get(id);
  }

  async extractAllUsers() {
    return this.model
      .scan()
      .all()
      .exec()
      .then((users) =>
        Promise.all(
          users.map(async (user) => {
            try {
              const role = await this.RoleModel.query('id').eq(user.roleId).exec();
              user.roleId = role[0] ?? {};
            } catch (error) {
              user.roleId = {}; // if error occurs return empty object
            }
            return user;
          })
        )
      );
  }

  async getAllName() {
    return this.model.scan().attributes(['id', 'name', 'email', 'isActive']).all().exec();
  }

  async saveOauthTokens(id, refreshToken, accessToken) {
    try {
      return await this.model.update(id, {
        refreshToken,
        accessToken,
      });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async saveAccessToken(id, accessToken) {
    try {
      return await this.model.update(id, {
        accessToken,
      });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}

module.exports = UserRepository;
