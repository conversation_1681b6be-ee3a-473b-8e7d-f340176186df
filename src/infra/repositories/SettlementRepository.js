const BaseRepository = require('./BaseRepository');

class SettlementRepository extends BaseRepository {
  constructor({ SettlementModel, SettlementSchema, logger }) {
    super(SettlementModel);
    this.logger = logger;

    this.hashKey = SettlementSchema.hashKey;
    this.rangeKey = SettlementSchema.rangeKey;
    this.indexAttributes = SettlementSchema.indexAttributes;
    // remove repetitions and remove arrays and objects
    // TODO: remove attributes that will not be used in filtering if necessary
    this.schemaFilters = SettlementSchema.attributes().filter(
      (attribute) =>
        attribute !== this.hashKey &&
        attribute !== this.rangeKey &&
        !this.indexAttributes.includes(attribute) &&
        !attribute.includes('.')
    );
    // this.schemaFilters = ['amount'];
    this.customFilters = [];
    this.validStatus = ['CARD_AUTHORISED', 'ECPAY_AUTHORISED', 'POSTED', 'PAYMENT_POSTING_FAILED', 'FALLOUT_POSTED'];
    this.validFilters = [
      ...this.schemaFilters,
      ...this.customFilters,
      ...this.indexAttributes,
      this.hashKey,
      this.rangeKey,
    ];
  }

  async searchFilter(data) {
    const { filter, pagination } = data;
    const filterFilters = () =>
      Object.keys(filter).filter((filterKey) => {
        if (!this.validFilters.includes(filterKey)) {
          delete filter[filterKey];
          return false;
        }
        if (filterKey === 'status') {
          if (!this.validStatus.includes(filter[filterKey])) {
            delete filter[filterKey];
            return false;
          }
        }
        return true;
      });

    filterFilters();
    const items = [];
    let lastKey = pagination.startKey ? JSON.parse(pagination.startKey) : null;

    do {
      const query = this.searchQuery(filter);
      if (lastKey) {
        query.startAt(lastKey);
      }
      if (pagination.limit) {
        query.limit(pagination.limit);
      }
      const results = await query.exec();
      items.push(...results);
      lastKey = results.lastKey;
    } while (items.length < pagination.limit && lastKey);

    // add metadata to array
    items.count = items.length;
    items.lastKey = lastKey;

    return items;
  }

  // query builder
  searchQuery(filters) {
    // we prioritize the hashKey, then the first available indexed attribute.
    const queryableKeys = [this.hashKey, ...this.indexAttributes];
    const primaryQueryKey = queryableKeys.find((key) => key in filters);

    let query = primaryQueryKey
      ? this.model.query(primaryQueryKey).eq(filters[primaryQueryKey]).sort('descending')
      : this.model.scan();

    for (const [key, rawValue] of Object.entries(filters)) {
      if (key === primaryQueryKey) {
        continue;
      }

      // add special conditions here if not using filter().eq()
      switch (key) {
        default: {
          const intValues = ['amount', 'mobile', 'msisdn'];
          const value = intValues.includes(key.toLowerCase()) ? parseFloat(rawValue) : rawValue;
          query.filter(key).eq(value);
        }
      }
    }

    return query;
  }

  async getByPaymentAndTransactionId(keys) {
    return await this.model.get(keys);
  }

  async getByPaymentId(paymentId) {
    return await this.model.query('paymentId').eq(paymentId).exec();
  }

  async getTotalSettlementAccount(paymentId) {
    const settlements = await this.model.query('paymentId').eq(paymentId).exec();

    return settlements.reduce((totalAmt, d) => (totalAmt += parseFloat(d.amount)), 0);
  }

  async getSettlement(filters) {
    let scan = this.model.scan();
    // this.logger.info({
    //   repositoryFilters: JSON.stringify(filters),
    // });
    for (const [key, value] of Object.entries(filters)) {
      if (key === 'createDateTime') {
        const { start, end } = value;
        scan = scan.where('createDateTime').between(start, end);
      } else {
        let typedValue = value;
        if (key === 'mobileNumber') {
          typedValue = parseInt(value, 10);
        }
        scan = scan.filter(key).eq(typedValue);
      }
    }

    return scan.all().exec();
  }

  async searchFilterV2(filters) {
    let scan = this.model.scan();
    // this.logger.info({
    //   repositoryFilters: JSON.stringify(filters),
    // });

    for (const [key, value] of Object.entries(filters)) {
      if (key === 'createDateTime') {
        const { start, end } = value;
        scan = scan.where('createDateTime').between(start, end);
      } else {
        let filterValue = value;
        let filterKey = key;
        if (key === 'mobileNumber') {
          filterValue = parseInt(value, 10);
        }

        scan = scan.filter(filterKey).eq(filterValue);
      }
    }

    if (typeof scan.sort === 'function') {
      scan = scan.sort('descending');
    }
    const results = await scan.all().exec();

    // this.logger.info({
    //   message: '[Settlement] Search result for Primary Query',
    //   filters,
    //   count: results.count,
    //   data: results,
    // });
    return results;
  }
}

module.exports = SettlementRepository;
