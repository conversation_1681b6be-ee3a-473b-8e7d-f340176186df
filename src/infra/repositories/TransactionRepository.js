const BaseRepository = require('./BaseRepository');

class TransactionRepository extends BaseRepository {
  constructor({ TransactionModel, TransactionSchema, logger }) {
    super(TransactionModel);
    this.logger = logger;
    this.accessKeys = ['id', 'accountNumber'];

    this.hashKey = TransactionSchema.hashKey;
    this.rangeKey = TransactionSchema.rangeKey;
    this.indexAttributes = TransactionSchema.indexAttributes;
    // remove repetitions and remove arrays and objects
    // TODO: remove attributes that will not be used in filtering if necessary
    this.schemaFilters = TransactionSchema.attributes().filter(
      (attribute) =>
        attribute !== this.hashKey &&
        attribute !== this.rangeKey &&
        !this.indexAttributes.includes(attribute) &&
        !attribute.includes('.')
    );

    this.customFilters = ['isInstallment', 'paymentCode'];
    this.validStatus = [
      'WEB_SESSION_CREATED',
      'ECPAY_GENERATED',
      'ECPAY_SESSION_CREATED',
      'XENDIT_CALLBACK_RECEIVED',
      'ECPAY_EXPIRED',
      'CARD_SESSION_CREATED',
      'CARD_REFUSED',
      'REFUND_SESSION_CREATED',
      'REFUND_PROCESSING',
      'REFUND_REQUESTED',
    ];
    this.validFilters = [
      ...this.schemaFilters,
      ...this.customFilters,
      ...this.indexAttributes,
      this.hashKey,
      this.rangeKey,
    ];
  }

  async searchFilter(data) {
    const { filter, pagination } = data;
    const filterFilters = () =>
      Object.keys(filter).filter((filterKey) => {
        if (!this.validFilters.includes(filterKey)) {
          delete filter[filterKey];
          return false;
        }
        if (filterKey === 'status') {
          if (!this.validStatus.includes(filter[filterKey])) {
            delete filter[filterKey];
            return false;
          }
        }
        return true;
      });

    filterFilters();
    const items = [];
    let lastKey = pagination.startKey ? JSON.parse(pagination.startKey) : null;

    do {
      const query = this.searchQuery(filter);
      if (lastKey) {
        query.startAt(lastKey);
      }
      if (pagination.limit) {
        query.limit(pagination.limit);
      }
      const results = await query.exec();

      items.push(...results);
      lastKey = results.lastKey;
    } while (items.length < pagination.limit && lastKey);

    // other transformation

    const transformedItems = items.map((item) => ({
      ...item,
      oona: item.settlementBreakdown.find((i) => i.transactionType === 'O')?.amountValue || 0,
      budgetProtect: item.settlementBreakdown.find((i) => i.transactionType === 'S')?.amountValue || 0,
    }));

    // add metadata to array
    transformedItems.count = transformedItems.length;
    transformedItems.lastKey = lastKey;

    return transformedItems;
  }

  // query builder
  searchQuery(filters) {
    // we prioritize the hashKey, then the first available indexed attribute.
    const queryableKeys = [this.hashKey, ...this.indexAttributes];
    const primaryQueryKey = queryableKeys.find((key) => key in filters);

    let query = primaryQueryKey
      ? this.model.query(primaryQueryKey).eq(filters[primaryQueryKey]).sort('descending')
      : this.model.scan();

    for (const [key, rawValue] of Object.entries(filters)) {
      if (key === primaryQueryKey) {
        continue;
      }

      // add special handling here if not using filter().eq()
      switch (key) {
        case 'isInstallment': {
          query.filter('installment').exists();
          break;
        }
        case 'createDateTime': {
          query.filter(key).between(rawValue.start, rawValue.end);
          break;
        }
        case 'paymentCode': {
          query.filter('paymentId').beginsWith(rawValue);
          break;
        }
        default: {
          const value = key.toLowerCase().includes('amount') ? parseFloat(rawValue) : rawValue;
          query.filter(key).eq(value);
        }
      }
    }

    return query;
  }

  monthlyQuery(data) {
    const query = this.model.query('entityName').eq('payment');

    if (data.filter.month === 'Jan') {
      query.filter('monthlyReportDate').eq(`1-${data.filter.year}`);
    } else if (data.filter.month === 'Feb') {
      query.filter('monthlyReportDate').eq(`2-${data.filter.year}`);
    } else if (data.filter.month === 'Mar') {
      query.filter('monthlyReportDate').eq(`3-${data.filter.year}`);
    } else if (data.filter.month === 'Apr') {
      query.filter('monthlyReportDate').eq(`4-${data.filter.year}`);
    } else if (data.filter.month === 'May') {
      query.filter('monthlyReportDate').eq(`5-${data.filter.year}`);
    } else if (data.filter.month === 'Jun') {
      query.filter('monthlyReportDate').eq(`6-${data.filter.year}`);
    } else if (data.filter.month === 'Jul') {
      query.filter('monthlyReportDate').eq(`7-${data.filter.year}`);
    } else if (data.filter.month === 'Aug') {
      query.filter('monthlyReportDate').eq(`8-${data.filter.year}`);
    } else if (data.filter.month === 'Sep') {
      query.filter('monthlyReportDate').eq(`9-${data.filter.year}`);
    } else if (data.filter.month === 'Oct') {
      query.filter('monthlyReportDate').eq(`10-${data.filter.year}`);
    } else if (data.filter.month === 'Nov') {
      query.filter('monthlyReportDate').eq(`11-${data.filter.year}`);
    } else if (data.filter.month === 'Dec') {
      query.filter('monthlyReportDate').eq(`12-${data.filter.year}`);
    }

    return query;
  }

  async listAll(data) {
    const query = this.model.scan();
    if (data.pagination.startKey && data.pagination.startKey !== '') {
      query.startAt(JSON.parse(data.pagination.startKey));
    }
    return query.limit(data.pagination.limit).exec();
  }

  async revenueWirelessFilter(data) {
    const transaction = data;
    const found = this.accessKeys.some((r) => Object.keys(data.filter).indexOf(r) >= 0);
    const query = this.searchQuery(transaction);
    if (data.pagination.startKeys !== '') {
      query.startAt(JSON.parse(data.pagination.startKeys));
    }
    if (found) {
      query.limit(data.pagination.limit);
    }
    return query.exec();
  }

  async revenueWirelessMonthlyList(data) {
    const filter = data;
    const query = this.monthlyQuery(filter);

    if (data.pagination.startKeys !== '') {
      query.startAt(JSON.parse(data.pagination.startKeys));
    }
    return query.exec();
  }

  async searchFilterV2(filters) {
    /**
     * Filter by primary key
     */
    //const copy = filters;

    if (filters?.paymentId) {
      const query = this.model.query('paymentId').eq(filters.paymentId);
      const results = await query.exec();
      let filteredResults = results;

      delete filters.paymentId;

      const filterKeys = Object.keys(filters);
      if (filterKeys.length > 0) {
        // Apply remaining filters
        filteredResults = results.filter((item) => {
          let isMatch = true;
          for (const key in filters) {
            // this.logger.info({
            //   key,
            //   f: filters[key],
            //   i: item[key]
            // });
            if (item[key] !== filters[key]) {
              isMatch = false;
              break;
            }
          }
          return isMatch;
        });
      }
      // this.logger.info({
      //   message: '[Translog] Search by primary query',
      //   filters: copy,
      //   coount: filteredResults.length,
      //   data: JSON.stringify(filteredResults),
      // });
      return filteredResults;
    } else {
      /**
       * Filter by secondary key
       */
      const query = this.model.scan();

      if (filters?.createDateTime?.start && filters?.createDateTime?.end) {
        const { start, end } = filters.createDateTime || {};
        query.filter('createDateTime').between(start, end);
      }
      if (filters?.paymentMethod) {
        query.filter('paymentMethod').eq(filters.paymentMethod);
      }
      if (filters?.gatewayProcessor) {
        query.filter('gatewayProcessor').eq(filters.gatewayProcessor);
      }
      if (filters?.channelId) {
        query.filter('channelId').eq(filters.channelId);
      }
      if (filters?.customerName) {
        query.filter('customerName').eq(filters.customerName);
      }
      if (filters?.totalAmount) {
        query.filter('totalAmount').eq(parseFloat(filters?.totalAmount));
      }
      if (filters?.paymentCode) {
        query.filter('paymentCode').eq(filters.paymentCode);
      }
      if (filters?.userAssignedChannels) {
        query.filter('channelId').in(filters.userAssignedChannels);
      }

      if (typeof query.sort === 'function') {
        query.sort('descending');
      }

      const results = await query.exec();
      // this.logger.info({
      //   message: '[Translog] Search by secondary query',
      //   filters: copy,
      //   coount: results.count,
      //   data: results,
      // });
      return results;
    }
  }

  async failedListAll(data) {
    const query = this.model.query('status').eq('POSTING_FAILED').sort('descending');
    if (data.pagination.startKeys !== '') {
      query.startAt(JSON.parse(data.pagination.startKeys));
    }
    return query.limit(data.pagination.limit).exec();
  }

  async failedSearchFilter(data) {
    const transaction = data;
    const found = this.accessKeys.some((r) => Object.keys(data.filter).indexOf(r) >= 0);
    transaction.filter.status = 'POSTING_FAILED';
    const query = this.searchQuery(transaction);
    if (data.pagination.startKeys !== '') {
      query.startAt(JSON.parse(data.pagination.startKeys));
    }
    if (found) {
      query.limit(data.pagination.limit);
    }
    return query.exec();
  }

  async postedAndFailedList(data) {
    const query = this.model
      .query('entityName')
      .eq('payment')
      .sort('descending')
      .filter('status')
      .in(['POSTED', 'POSTING_FAILED', 'POSTED_LUKE', 'XENDIT_AUTHORISED_NON_POSTING']);
    if (data.pagination.startKeys !== '') {
      query.startAt(JSON.parse(data.pagination.startKeys));
    }
    return query.limit(data.pagination.limit).exec();
  }

  async postedAndFailedFilter(data) {
    const transaction = data;
    const found = this.accessKeys.some((r) => Object.keys(data.filter).indexOf(r) >= 0);
    if (!Object.prototype.hasOwnProperty.call(data.filter, 'status')) {
      transaction.filter.status = ['POSTED', 'POSTING_FAILED', 'POSTED_LUKE', 'XENDIT_AUTHORISED_NON_POSTING'];
    }
    const query = this.searchQuery(transaction);
    if (data.pagination.startKeys !== '') {
      query.startAt(JSON.parse(data.pagination.startKeys));
    }
    if (found) {
      query.limit(data.pagination.limit);
    }
    return query.exec();
  }

  // /**
  //  * Creates Snapshot compatible payload for dynamodb snapshot table
  //  * @param {*} timestamp Unix Timestamp
  //  * Note:
  //  *  no-await-in-loop is disabled due to the reason that lastKey from previous payload
  //  *  will be used as ExclusiveStartKey to the next queries
  //  *
  //  */

  async transactionSnapshot(datetime, startKeys) {
    const { currentDate, startDate } = datetime;
    const query = this.model
      .query('entityName')
      .eq('payment')
      .ascending()
      .where('timestamp')
      .between(startDate.toISOString(), currentDate.toISOString());
    if (typeof startKeys !== 'undefined') {
      query.startAt(startKeys);
    }
    return query.exec();
  }

  async transactionSnapshotv2(now, startKeys) {
    const query = this.model
      .query('entityName')
      .eq('payment')
      .startAt(startKeys)
      .ascending()
      .where('timestamp')
      .le(now.toISOString());
    return query.exec();
  }

  async getFilterTransactions(datetime) {
    return this.model.query('entityName').eq('payment').sort('descending').where('timestamp').ge(datetime).exec();
  }

  async getTransactionCount(id, date) {
    return this.model.query('channelId').eq(id).where('timestamp').between(date.start, date.end).all().exec();
  }

  async gatewayCCMontlyReport(month, lastKey) {
    const query = this.model
      .query('monthlyReportDate')
      .eq(month)
      .filter('status')
      .in(['POSTED', 'POSTED_LUKE', 'POSTING_FAILED'])
      .filter('gatewayProcessor')
      .not()
      .eq('gcash');
    if (lastKey !== '') {
      query.startAt(lastKey);
    }
    return query.exec();
  }

  async billingDailyReport(day, lastKey) {
    const query = this.model
      .query('dailyReportDate')
      .eq(day)
      .filter('status')
      .in(['POSTED', 'POSTED_LUKE', 'POSTING_FAILED']);
    if (lastKey !== '') {
      query.startAt(lastKey);
    }
    return query.exec();
  }

  async raWirelessMonthlyReport(month, lastKey) {
    const query = this.model.query('monthlyReportDate').eq(month);

    if (lastKey !== '') {
      query.startAt(lastKey);
    }

    return query.exec();
  }

  async queryByPosted(datetime) {
    const { currentDate, startDate } = datetime;
    return this.model
      .query('status')
      .eq('POSTED')
      .sort('descending')
      .where('timestamp')
      .between(startDate.toISOString(), currentDate.toISOString())
      .all()
      .exec();
  }

  async queryByPostedLuke(datetime) {
    const { currentDate, startDate } = datetime;
    return this.model
      .query('status')
      .eq('POSTED_LUKE')
      .sort('descending')
      .where('timestamp')
      .between(startDate.toISOString(), currentDate.toISOString())
      .all()
      .exec();
  }

  async queryByFailed(datetime) {
    const { currentDate, startDate } = datetime;
    return this.model
      .query('status')
      .eq('POSTING_FAILED')
      .sort('descending')
      .where('timestamp')
      .between(startDate.toISOString(), currentDate.toISOString())
      .all()
      .exec();
  }

  async queryByChannelAdyenRefused(datetime) {
    const { currentDate, startDate } = datetime;
    return this.model
      .query('status')
      .eq('ADYEN_REFUSED')
      .where('timestamp')
      .between(startDate.toISOString(), currentDate.toISOString())
      .all()
      .exec();
  }

  async queryByChannelGCashRefused(datetime) {
    const { currentDate, startDate } = datetime;
    return this.model
      .query('status')
      .eq('GCASH_REFUSED')
      .where('timestamp')
      .between(startDate.toISOString(), currentDate.toISOString())
      .all()
      .exec();
  }

  async queryByGatewayProcessor(datetime, gateway) {
    const { currentDate, startDate } = datetime;
    return this.model
      .query('gatewayProcessor')
      .eq(gateway)
      .sort('descending')
      .where('timestamp')
      .between(startDate, currentDate)
      .filter('status')
      .in(['POSTED', 'POSTED_LUKE'])
      .all()
      .exec();
  }

  async globeOneDailyReport(day, startKey, channelId) {
    const query = this.model.query('dailyReportDate').eq(day).filter('channelId').eq(channelId);
    if (startKey !== '') {
      query.startAt(startKey);
    }
    return query.exec();
  }

  async ecpayDailyReport(day, startKey) {
    const query = this.model.query('dailyReportDate').eq(day).filter('state').contains('ecpay');
    if (startKey !== '') {
      query.startAt(startKey);
    }
    return query.exec();
  }

  async queryTransactionsByStatus(datetime, status) {
    const { currentDate, startDate } = datetime;
    return this.model
      .query('status')
      .eq(status)
      .sort('descending')
      .where('timestamp')
      .between(startDate, currentDate)
      .exec();
  }

  async getByPaymentId(id) {
    return this.model.query('paymentId').eq(id).exec();
  }

  async createPayByLinkTransaction(data) {
    const {
      paymentId,
      customerId,
      sessionId,
      createDateTime,
      status,
      channelId,
      gatewayProcessor,
      paymentMethod,
      totalAmount,
    } = data;

    return this.model.create({
      paymentId,
      createDateTime,
      status,
      channelId,
      gatewayProcessor,
      paymentMethod,
      totalAmount,
      settlementBreakdown: [],
      customerId,
      sessionId,
    });
  }
}

module.exports = TransactionRepository;
