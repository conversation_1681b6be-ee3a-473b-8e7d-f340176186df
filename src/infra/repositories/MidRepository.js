const BaseRepository = require('./BaseRepository');

class MidRepository extends BaseRepository {
  constructor({ MidModel }) {
    super(MidModel);
  }

  async getById(id) {
    return this.model.query('id').eq(id).exec();
  }

  async getMIdUniquessGlobal(data) {
    return this.model
      .scan()
      .filter('billType')
      .eq(data.billType)
      .filter('paymentType')
      .eq(data.paymentType)
      .filter('company')
      .eq(data.company)
      .filter('channelId')
      .eq(data.channelId)
      .all()
      .exec();
  }

  async getMIdUniquessBillWithOutChannel(data) {
    return (
      this.model
        .scan()
        .filter('billType')
        .eq(data.billType)
        .filter('paymentType')
        .eq(data.paymentType)
        .filter('company')
        .eq(data.company)
        .filter('channelId')
        // .null()
        .not()
        .exists() // company must not exists
        .all()
        .exec()
    );
  }

  async getMIdUniquessNonBillWithOutCompany(data) {
    return (
      this.model
        .scan()
        .filter('billType')
        .eq(data.billType)
        .filter('paymentType')
        .eq(data.paymentType)
        .filter('channelId')
        .eq(data.channelId)
        .filter('company')
        // .null()
        .not()
        .exists() // company must not exists
        .all()
        .exec()
    );
  }

  async getMIdUniquessGlobalOtherId(data, id) {
    return this.model
      .scan()
      .filter('id')
      .not()
      .eq(id)
      .filter('billType')
      .eq(data.billType)
      .filter('paymentType')
      .eq(data.paymentType)
      .filter('company')
      .eq(data.company)
      .filter('channelId')
      .eq(data.channelId)
      .all()
      .exec();
  }

  // Get Merchant Id Uniqueness Bill Without Channel Other Id
  async getMIdUniqBillWOChannelId(data, id) {
    return (
      this.model
        .scan()
        .filter('id')
        .not()
        .eq(id)
        .filter('billType')
        .eq(data.billType)
        .filter('paymentType')
        .eq(data.paymentType)
        .filter('company')
        .eq(data.company)
        .filter('channelId')
        // .null()
        .not()
        .exists() // company must not exists
        .all()
        .exec()
    );
  }

  async getMIdUniquessNonBillWithOutCompanyOtherId(data, id) {
    return (
      this.model
        .scan()
        .filter('id')
        .not()
        .eq(id)
        .filter('billType')
        .eq(data.billType)
        .filter('paymentType')
        .eq(data.paymentType)
        .filter('channelId')
        .eq(data.channelId)
        .filter('company')
        // .null()
        .not()
        .exists() // company must not exists
        .all()
        .exec()
    );
  }

  async getByChannel(data) {
    return this.model.scan().filter('paymentType').eq(data.channelId).exec();
  }

  async getByIdCompanyPaymentType(data, id) {
    return this.model
      .scan()
      .filter('id')
      .not()
      .eq(id)
      .filter('billType')
      .eq(data.billType)
      .filter('paymentType')
      .eq(data.paymentType)
      .filter('company')
      .eq(data.company)
      .exec();
  }

  async getChannelNotEqualToId(data, id) {
    return this.model.scan().filter('id').not().eq(id).filter('channelId').eq(data.channelId).exec();
  }

  async getRegisteredNonBillByChannels() {
    return (
      this.model
        .scan()
        .filter('billType')
        .eq('NonBill')
        .filter('channelId')
        // .not()
        // .null()
        .exists() // channelId must exists
        .attributes(['channelId'])
        .exec()
    );
  }

  midQuery(data) {
    const scan = this.model.scan();
    Object.keys(data.filter).forEach((key) => {
      scan.filter(key).eq(data.filter[key]);
    });
    return scan;
  }

  async searchFilter(data) {
    const scan = this.midQuery(data);
    if (typeof data.pagination.start !== 'undefined' && data.pagination.start !== '') {
      return scan.startAt({ id: { S: data.pagination.start } }).exec();
    }
    return scan.exec();
  }

  async searchFilterNoPaginate(data) {
    const scan = this.midQuery(data);
    return scan.attributes(['id']).exec();
  }

  async getInstallmentBankName(bank, bankTerm) {
    return this.model
      .scan()
      .filter('paymentType')
      .eq('Installment')
      .filter('bank')
      .eq(bank)
      .filter('bankTerm')
      .eq(bankTerm)
      .all()
      .exec();
  }

  async getMIdUpdateUniquessGlobal(data) {
    return this.model
      .scan()
      .filter('id')
      .not()
      .eq(data.id)
      .filter('billType')
      .eq(data.billType)
      .filter('paymentType')
      .eq(data.paymentType)
      .filter('company')
      .eq(data.company)
      .filter('channelId')
      .eq(data.channelId)
      .all()
      .exec();
  }

  async getMIdUpdateUniquessNonBillWithOutCompanyOtherId(data, id) {
    return (
      this.model
        .scan()
        .filter('id')
        .not()
        .eq(id)
        .filter('billType')
        .eq(data.billType)
        .filter('paymentType')
        .eq(data.paymentType)
        .filter('channelId')
        .eq(data.channelId)
        .filter('company')
        // .not()
        // .null()
        .exists() // channelId must exists
        .all()
        .exec()
    );
  }
}

module.exports = MidRepository;
