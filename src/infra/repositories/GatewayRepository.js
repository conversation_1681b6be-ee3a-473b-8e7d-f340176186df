const BaseRepository = require('./BaseRepository');

class GatewayRepository extends BaseRepository {
  constructor({ GatewayModel }) {
    super(GatewayModel);
  }

  async getByIdAndMethod(id, gateway) {
    return this.model.scan('channelId').eq(id).where('paymentMethod').eq(gateway).exec();
  }

  async getByChannelId(id) {
    return this.model.scan('channelId').eq(id).exec();
  }

  async checkGateway(ids, date) {
    return this.model.scan('channelId').in(ids).where('timestamp').between(date.startDate, date.endDate).exec();
  }

  gatewayQuery(data) {
    const scan = this.model.scan();
    Object.keys(data.filter).forEach((key) => {
      if (key === 'status') {
        scan.where('status').eq(data.filter[key]);
      } else if (key === 'updatedAt') {
        if (data.filter[key][0].start === null) {
          scan.where('updatedAt').le(data.filter[key][0].end);
        } else if (data.filter[key][0].end === null) {
          scan.where('updatedAt').ge(data.filter[key][0].start);
        } else {
          scan.where('updatedAt').between(data.filter[key][0].start, data.filter[key][0].end);
        }
      } else if (key === 'channelId') {
        scan.where('channelId').in(data.filter[key]);
      } else {
        scan.filter(key).contains(data.filter[key]);
      }
    });
    return scan;
  }

  async listAll(data) {
    const scan = this.model.scan();
    if (
      typeof data?.pagination?.start?.channelId !== 'undefined' &&
      data?.pagination?.start?.channelId !== '' &&
      typeof data?.pagination?.start?.paymentMethod !== 'undefined' &&
      data?.pagination?.start?.paymentMethod !== ''
    ) {
      return scan
        .startAt({
          channelId: { S: data.pagination.start.channelId },
          paymentMethod: { S: data.pagination.start.paymentMethod },
        })
        .limit(data.pagination.limit)
        .exec();
    }
    return scan.limit(data.pagination.limit).exec();
  }

  async searchFilter(data) {
    const scan = this.gatewayQuery(data);
    if (
      typeof data.pagination.start.channelId !== 'undefined' &&
      data.pagination.start.channelId !== '' &&
      typeof data.pagination.start.paymentMethod !== 'undefined' &&
      data.pagination.start.paymentMethod !== ''
    ) {
      return scan
        .startAt({
          channelId: { S: data.pagination.start.channelId },
          paymentMethod: { S: data.pagination.start.paymentMethod },
        })
        .exec();
    }
    return scan.exec();
  }

  async searchFilterNoPaginate(data) {
    const scan = this.gatewayQuery(data);
    return scan.exec();
  }
}

module.exports = GatewayRepository;
