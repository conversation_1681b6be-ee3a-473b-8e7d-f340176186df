const BaseRepository = require('./BaseRepository');

class BankRepository extends BaseRepository {
  constructor({ BankModel }) {
    super(BankModel);
  }

  async getByName(name) {
    return this.model.query('name').eq(name).exec();
  }

  async getByCode(code) {
    return this.model.scan('code').eq(code).exec();
  }

  async getUniqueBankCode(data) {
    return this.model.scan('name').not().eq(data.name).filter('code').eq(data.code).exec();
  }

  async getAllName() {
    return this.model.scan().all().attributes(['name', 'code']).exec();
  }

  banksQuery(data) {
    const scan = this.model.scan();
    Object.keys(data.filter).forEach((key) => {
      scan.filter(key).contains(data.filter[key]);
    });
    return scan;
  }

  async listAll(data) {
    const scan = this.banksQuery(data);
    if (typeof data.pagination.start !== 'undefined' && data.pagination.start !== '') {
      return scan
        .startAt({
          name: { S: data.pagination.start },
        })
        .limit(data.pagination.limit)
        .exec();
    }
    return scan.limit(data.pagination.limit).exec();
  }

  async searchFilter(data) {
    const scan = this.banksQuery(data);
    if (typeof data.pagination.start !== 'undefined' && data.pagination.start !== '') {
      return scan.startAt({ name: { S: data.pagination.start } }).exec();
    }
    return scan.exec();
  }

  async searchFilterNoPaginate(data) {
    const scan = this.banksQuery(data);
    return scan.attributes(['name']).exec();
  }

  async getAllId() {
    return this.model.scan().attributes(['name']).exec();
  }
}

module.exports = BankRepository;
