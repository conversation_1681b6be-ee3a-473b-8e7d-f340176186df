const BaseRepository = require('./BaseRepository');

class ConvenienceFeeBrandRepository extends BaseRepository {
  constructor({ ConvenienceFeeBrandModel }) {
    super(ConvenienceFeeBrandModel);
  }

  list(data) {
    const scan = this.model.scan();
    Object.keys(data.filter).forEach((key) => {
      scan.filter(key).contains(data.filter[key]);
    });
    return scan;
  }

  async listAll(data) {
    const scan = this.list(data);
    if (typeof data.pagination.startKey !== 'undefined' && data.pagination.startKey !== '') {
      return scan
        .startAt({
          id: data.pagination.startKey,
        })
        .limit(data.pagination.limit)
        .exec();
    }
    return scan.limit(data.pagination.limit).exec();
  }

  async searchFilter(data) {
    const scan = this.list(data);
    if (typeof data.pagination.startKey !== 'undefined' && data.pagination.startKey !== '') {
      return scan.startAt({ name: data.pagination.startKey }).exec();
    }
    return scan.exec();
  }

  async searchFilterNoPaginate(data) {
    const scan = this.list(data);
    return scan.attributes(['id']).exec();
  }

  async getAllId() {
    return this.model.scan().attributes(['id']).exec();
  }

  async getById(id) {
    return this.model.query('id').eq(id).exec();
  }

  async createConvenienceFee(data) {
    return this.model.create(data);
  }
}

module.exports = ConvenienceFeeBrandRepository;
