const BaseRepository = require('./BaseRepository');
const collectionReportMapper = require('../utils/collectionReportMapper');

class SnapshotRepository extends BaseRepository {
  constructor({ SnapshotModel }) {
    super(SnapshotModel);
  }

  async getSnapshot(type, date) {
    return this.model
      .query('type')
      .eq(type)
      .filter('month')
      .eq(date.month)
      .filter('year')
      .eq(date.year)
      .filter('day')
      .eq(date.day)
      .all()
      .exec();
  }

  async getLastUpdatedAt() {
    return this.model.query('type').eq('overall').all().exec();
  }

  async getSnapshotByDateRange(type, startDate, endDate) {
    return this.model
      .query('type')
      .eq(type)
      .where('createdAt')
      .between(startDate, endDate)
      .sort('descending')
      .all()
      .exec();
  }

  async getSnapshotByYear(type, year) {
    return this.model.query('type').eq(type).filter('year').eq(year).sort('descending').all().exec();
  }

  async getSnapshotByMonth(type, months, year) {
    return this.model
      .query('type')
      .eq(type)
      .filter('year')
      .eq(`${year}`)
      .filter('month')
      .in(months)
      .sort('descending')
      .all()
      .exec();
  }

  async transactionsByMonth(months, type) {
    const resultPromises = months.map(
      (date) =>
        new Promise((resolve) => {
          const query = this.model
            .query('type')
            .eq('transactions')
            .filter('month')
            .eq(date.month)
            .filter('year')
            .eq(date.year);
          resolve(query.exec());
        })
    );
    const result = await Promise.all(resultPromises);
    const flattenedResult = result.reduce((accumulator, currentValue) => accumulator.concat(currentValue), []);
    if (type === 'transaction') {
      return flattenedResult.map((transaction) => {
        const data = transaction;
        const payload = JSON.parse(transaction.payload);
        data.payload = Object.values(payload.payloadTransaction);
        return data;
      });
    }
    if (type === 'mid') {
      let midCCDetails = [];

      flattenedResult.forEach((transaction) => {
        const data = transaction;
        const payload = JSON.parse(transaction.payload);
        data.payload = Object.values(payload.payloadCMid);

        midCCDetails = collectionReportMapper(midCCDetails, data, type);
      });

      return midCCDetails;
    }
    if (type === 'company') {
      let companyCCDetails = [];

      flattenedResult.forEach((transaction) => {
        const data = transaction;
        const payload = JSON.parse(transaction.payload);
        data.payload = Object.values(payload.payloadCCompany);
        companyCCDetails = collectionReportMapper(companyCCDetails, data, type);
      });

      return companyCCDetails;
    }
    return [];
  }
}
module.exports = SnapshotRepository;
