const { REFUND_ACTION_STATUSES } = require('../utils/constants');
const BaseRepository = require('./BaseRepository');

class RefundRepository extends BaseRepository {
  constructor({ RefundModel }) {
    super(RefundModel);
    this.accessKeys = ['reference', 'channelId'];
  }

  async getByReference(id) {
    return this.model.query('id').eq(id).exec();
  }

  async getByPaymentIdForApproval(id, transactionId) {
    let query = this.model.query('paymentId').eq(id);
    if (transactionId) {
      query = query.filter('transactionId').eq(transactionId);
    }
    return query.filter('refundApprovalStatus').eq(REFUND_ACTION_STATUSES.FOR_APPROVAL).exec();
  }

  searchQuery(data) {
    let query;
    const filters = data.filter;
    delete filters.gatewayProcessor;
    if (Object.prototype.hasOwnProperty.call(filters, 'paymentId')) {
      query = this.model.query('paymentId').eq(filters.paymentId);
      delete filters.paymentId;
    } else if (Object.prototype.hasOwnProperty.call(filters, 'channelId')) {
      query = this.model.query('channelId').eq(filters.channelId);
      delete filters.channelId;
    } else if (Object.prototype.hasOwnProperty.call(filters, 'paymentGateway')) {
      query =
        typeof filters.paymentGateway === 'string'
          ? this.model.query('paymentGateway').eq(filters.paymentGateway)
          : this.model.query('paymentGateway').in(filters.paymentGateway);

      delete filters.paymentGateway;
    } else if (Object.prototype.hasOwnProperty.call(filters, 'refundId')) {
      query = this.model.query('refundId').eq(filters.refundId);
      delete filters.refundId;
    } else if (Object.prototype.hasOwnProperty.call(filters, 'status') && typeof filters.status === 'string') {
      query = this.model.query('status').eq(filters.status);
      delete filters.status;
    } else if (
      Object.prototype.hasOwnProperty.call(filters, 'refundApprovalStatus') &&
      typeof filters.refundApprovalStatus === 'string'
    ) {
      query = this.model.query('refundApprovalStatus').eq(filters.refundApprovalStatus);
      delete filters.refundApprovalStatus;
    } else if (
      Object.prototype.hasOwnProperty.call(filters, 'paymentMethod') &&
      typeof filters.paymentMethod === 'string'
    ) {
      query = this.model.query('paymentMethod').eq(filters.paymentMethod);
      delete filters.paymentMethod;
    } else {
      query = this.model.query('entity').eq('refund');
    }
    query.sort('descending');
    Object.entries(filters).forEach((filter) => {
      const [filterKey, filterValue] = filter;
      if (filterKey === 'createDateTime') {
        const { start, end } = data.filter.createDateTime;
        query.where(filterKey).between(start, end);
      } else if (filterKey === 'status' && typeof filters.status === 'object') {
        query.filter('status').in(filterValue);
      } else if (filterKey === 'refundApprovalStatus') {
        if (typeof filters.refundApprovalStatus === 'object') {
          query.filter('refundApprovalStatus').in(filterValue);
        } else {
          query.filter('refundApprovalStatus').eq(filterValue);
        }
      } else if (filterKey === 'userAssignedChannels') {
        query.filter('channelId').in(filterValue);
      } else if (filterKey === 'paymentGateway') {
        query =
          typeof filters.paymentGateway === 'string'
            ? query.filter('paymentGateway').eq(filters.paymentGateway)
            : query.filter('paymentGateway').in(filters.paymentGateway);

        delete filters.paymentGateway;
      } else {
        query.filter(filterKey).eq(filterValue);
      }
    });
    return query;
  }

  async searchFilter(data) {
    const found = this.accessKeys.some((r) => Object.keys(data.filter).indexOf(r) >= 0);
    const query = this.searchQuery(data);
    if (data.pagination.startKey !== '') {
      query.startAt(JSON.parse(data.pagination.startKey));
    }
    if (found) {
      query.limit(data.pagination.limit);
    }
    return query.exec();
  }

  async getFARefundTransaction(filters, startKeys, paymentGateway) {
    const { refundRange, channelId } = filters;
    let query = this.model.query('refundApprovalStatus').eq('For Approval');
    if (typeof refundRange !== 'undefined' && typeof channelId !== 'undefined') {
      query = this.model
        .query('refundApprovalStatus')
        .eq('For Approval')
        .where('timestamp')
        .between(refundRange.start, refundRange.end)
        .filter('channelId')
        .eq(channelId);
    } else if (typeof channelId !== 'undefined') {
      query = this.model.query('refundApprovalStatus').eq('For Approval').filter('channelId').eq(channelId);
    } else if (typeof refundRange !== 'undefined') {
      query = this.model
        .query('refundApprovalStatus')
        .eq('For Approval')
        .where('timestamp')
        .between(refundRange.start, refundRange.end);
    }
    query.filter('paymentGateway').eq(paymentGateway);
    if (startKeys !== '') {
      query.startAt(JSON.parse(startKeys));
    }
    return query.exec();
  }

  async getRefundByRefundId(refundId) {
    if (!refundId) return null;

    return this.model
      .query('refundId')
      .using('RefundIdIndex')
      .eq(refundId)
      .attributes([
        'refundId',
        'refundApprovalStatus',
        'refundStatus',
        'createDateTime',
        'refundAmount',
        'refundReason',
      ])
      .exec();
  }

  async getByPaymentAndTransactionId({ paymentId, transactionId }) {
    let query = this.model.query('paymentId').eq(paymentId);
    if (transactionId) {
      query = query.filter('transactionId').eq(transactionId);
    }
    query = query.filter('refundApprovalStatus').not().eq(REFUND_ACTION_STATUSES.REJECTED);
    return await query.exec();
  }

  async getPaymentIdTotalRefundAmount(paymentId, transactionId = null) {
    const query = await this.model
      .query('paymentId')
      .eq(paymentId)
      .filter('refundApprovalStatus')
      .eq('Approved')
      .exec();
    return [
      query.reduce((amount, value) => amount + parseFloat(value.refundAmount), 0),
      query.reduce(
        (amount, value) => amount + (transactionId === value.transactionId ? parseFloat(value.refundAmount) : 0),
        0
      ),
    ];
  }
}

module.exports = RefundRepository;
