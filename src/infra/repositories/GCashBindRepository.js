const BaseRepository = require('./BaseRepository');

class GCashBindRepository extends BaseRepository {
  constructor({ BindingDetailModel }) {
    super(BindingDetailModel);
  }

  async getByStatus(data) {
    const query = this.model.query('status').eq(data.filter.status);

    if (data.pagination.startKeys) {
      query.startAt(JSON.parse(data.pagination.startKeys));
    }

    const filteredQuery = this.applyBindingFilter(query, data.filter, ['status']);

    if (data?.pagination?.limit) {
      filteredQuery.limit(data.pagination.limit);
    }

    return filteredQuery.exec();
  }

  async getByUuid(uuid, filter = {}) {
    const query = this.model.query('uuid').eq(uuid?.trim());

    return this.applyBindingFilter(query, filter, ['uuid']).exec();
  }

  async getByBindingRequestID(bindingRequestID, filter = {}) {
    const query = this.model.query('bindingRequestID').eq(bindingRequestID?.trim());

    return this.applyBindingFilter(query, filter, ['bindingRequestID']).exec();
  }

  async getAll(data) {
    const query = this.model.query('sortKey').using('sortKeyIndex').eq('binding');

    if (data.pagination.startKeys) {
      query.startAt(JSON.parse(data.pagination.startKeys));
    }

    return this.applyBindingFilter(query, data.filter)
      .sort('descending')
      .limit(data?.pagination?.limit ?? 10)
      .exec();
  }

  applyBindingFilter(query, filters = {}, excludedKeys = []) {
    Object.entries(filters).forEach(([filterKey, filterValue]) => {
      if (!excludedKeys.includes(filterKey)) {
        if (filterKey === 'validUntil') {
          const startDate = new Date(filterValue.start).getTime();
          const endDate = new Date(filterValue.end).getTime();

          query.where(filterKey).between(startDate, endDate);
        } else {
          query.filter(filterKey).eq(filterValue?.trim());
        }
      }
    });

    return query;
  }
}

module.exports = GCashBindRepository;
