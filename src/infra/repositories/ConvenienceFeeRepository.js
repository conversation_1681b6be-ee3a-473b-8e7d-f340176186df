const BaseRepository = require('./BaseRepository');

class ConvenienceFeeRepository extends BaseRepository {
  constructor({ ConvenienceFeeModel, formatter }) {
    super(ConvenienceFeeModel);
    this.formatter = formatter;
  }

  list(data) {
    const scan = this.model.scan();
    Object.keys(data.filter).forEach((key) => {
      scan.filter(key).contains(data.filter[key]);
    });
    return scan;
  }

  async listAll(data) {
    const scan = this.list(data);
    if (typeof data.pagination.startKey !== 'undefined' && data.pagination.startKey !== '') {
      return scan
        .startAt({
          id: data.pagination.startKey,
        })
        .limit(data.pagination.limit)
        .exec();
    }
    return scan.limit(data.pagination.limit).exec();
  }

  async searchFilter(data) {
    const scan = this.list(data);
    if (typeof data.pagination.startKey !== 'undefined' && data.pagination.startKey !== '') {
      return scan.startAt({ name: data.pagination.startKey }).exec();
    }
    return scan.exec();
  }

  async searchFilterNoPaginate(data) {
    const scan = this.list(data);
    return scan.attributes(['id']).exec();
  }

  async getAllId() {
    return this.model.scan().attributes(['id']).exec();
  }

  async getById(id) {
    return this.model.query('id').eq(id).exec();
  }

  async getCompositeKey(params) {
    const { channelId, brand, gatewayProcessor, paymentMethod, transactionType } = params;
    const payload = {
      channelId,
      brand,
      gatewayProcessor,
      paymentMethod,
      transactionType,
    };
    const compositeKey = this.formatter.createCompositeKey(payload, ['channelId']);
    return this.model.query('compositeKey').eq(compositeKey).exec();
  }

  async createConvenienceFee(data) {
    return this.model.create(data);
  }
}

module.exports = ConvenienceFeeRepository;
