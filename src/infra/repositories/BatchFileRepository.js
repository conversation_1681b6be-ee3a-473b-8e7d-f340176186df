const BaseRepository = require('./BaseRepository');

class BatchFileRepository extends BaseRepository {
  constructor({ BatchFileModel }) {
    super(BatchFileModel);
  }

  async searchFilter(data) {
    const { filter, pagination } = data;
    const scan = this.model.scan();
    if (Object.keys(filter).length >= 1) {
      Object.entries(filter).forEach((item) => {
        const [filterKey, filterValue] = item;
        if (filterKey === 'createdAt') {
          const { start, end } = filterValue;
          scan.filter('createdAt').between(start, end);
        } else {
          scan.filter(filterKey).contains(filterValue);
        }
      });
    }
    if (pagination.startKey !== '') {
      scan.startAt(JSON.parse(pagination.startKey));
    }
    return scan.limit(pagination.limit).exec();
  }

  async getData(filename) {
    return this.model.query('filename').eq(filename).exec();
  }
}

module.exports = BatchFileRepository;
