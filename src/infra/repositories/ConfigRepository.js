const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, UpdateCommand } = require('@aws-sdk/lib-dynamodb');
const BaseRepository = require('./BaseRepository');

class ConfigRepository extends BaseRepository {
  constructor({ ConfigurationModel }) {
    super(ConfigurationModel);
    let dbConfig = {
      region: process.env.AWS_REGION,
    };

    if (process.env.STAGE === 'local') {
      dbConfig = {
        ...dbConfig,
        endpoint: process.env.AWS_ENDPOINT,
      };
    }
    this.dynamoClient = new DynamoDBClient(dbConfig);
    this.dynamoDocClient = DynamoDBDocumentClient.from(this.dynamoClient);
  }

  async getSubMerchants(merchant) {
    return this.model.scan('merchant').eq(merchant).filter('type').in(['ecpaySubMerchant', 'subMerchant']).exec();
  }

  async getAllGlobeSubMerchant() {
    return this.model.query('type').eq('subMerchant').attributes(['name']).exec();
  }

  async getAllECPaySubMerchant() {
    return this.model.query('type').eq('ecpaySubMerchant').attributes(['name']).exec();
  }

  async getByName(name) {
    return this.model.query('name').eq(name).exec();
  }

  async updateSwipeORType(data) {
    const updatedAt = new Date().toISOString();
    const input = {
      TableName: `pgw${process.env.STAGE}-configurations`,
      Key: {
        name: 'swipeORPaymentType',
      },
      UpdateExpression: 'set #val = :vle, updatedAt = :upt',
      ExpressionAttributeNames: {
        '#val': 'value',
      },
      ExpressionAttributeValues: {
        ':vle': data,
        ':upt': updatedAt,
      },
    };
    const command = new UpdateCommand(input);
    return this.dynamoDocClient.send(command).then((result) => result);
  }

  async getByType(name) {
    return this.model.query('type').eq(name).exec();
  }
}

module.exports = ConfigRepository;
