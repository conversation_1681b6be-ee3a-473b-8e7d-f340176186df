const BaseRepository = require('./BaseRepository');

class PostPaymentConfigRepository extends BaseRepository {
  constructor({ PostPaymentConfigModel }) {
    super(PostPaymentConfigModel);
  }

  async getByChannelId(id, pagination) {
    const { startKey, limit } = pagination;

    return this.model
      .query('channelId')
      .eq(id)
      .startAt(startKey)
      .limit(limit)
      .exec();
  }

  async getByKeys(keys) {
    return this.model.get(keys);
  }

  async updateByChannelId(id, data) {
    return this.model.update({ channelId: id }, data);
  }

  async getByFilters(channelIdArray, pagination) {
    const { startKey, limit } = pagination;

    if (channelIdArray.length === 0) {
      channelIdArray = [''];
    }

    return this.model
      .scan('channelId')
      .in(channelIdArray)
      .startAt(startKey)
      .limit(limit)
      .exec();
  }
}

module.exports = PostPaymentConfigRepository;
