const BaseRepository = require('./BaseRepository');

class AuditRepository extends BaseRepository {
  constructor({ AuditModel }) {
    super(AuditModel);
  }

  auditQuery(data) {
    const query = this.model.query('sortKey').eq('audit');
    Object.keys(data.filter).forEach((key) => {
      if (key === 'createdAt') {
        if (data.filter[key][0].start === null) {
          query.filter('updatedAt').le(data.filter[key][0].end);
        } else if (data.filter[key][0].end === null) {
          query.filter('updatedAt').ge(data.filter[key][0].start);
        } else {
          query.filter('updatedAt').between(data.filter[key][0].start, data.filter[key][0].end);
        }
      } else if (key === 'roleId') {
        query.filter('roleId').in(data.filter[key]);
      } else {
        query.filter(key).contains(data.filter[key]);
      }
    });
    return query;
  }

  async searchFilter(data) {
    const query = this.auditQuery(data);
    const start = data.pagination.start[0];
    if (start.id !== '' && start.sortKey !== '' && data.createdAt !== '') {
      return query
        .startAt({
          id: { S: start.id },
          sortKey: { S: start.sortKey },
          createdAt: { S: start.createdAt },
        })
        .sort('descending')
        .exec();
    }
    return query.sort('descending').limit(data.pagination.limit).exec();
  }

  async listAll(data) {
    const start = data.pagination.start[0];
    const query = this.model.query('sortKey').eq('audit');
    if (start.id !== '' && start.sortKey !== '' && data.createdAt !== '') {
      return query
        .startAt({
          id: { S: start.id },
          sortKey: { S: start.sortKey },
          createdAt: { S: start.createdAt },
        })
        .sort('descending')
        .limit(data.pagination.limit)
        .exec();
    }
    return query.sort('descending').limit(data.pagination.limit).exec();
  }

  async notifications(args) {
    return this.model.query('sortKey').eq('audit').limit(args.limit).sort('descending').exec();
  }

  async notifNotViewCount() {
    return this.model.query('sortKey').eq('audit').filter('isViewed').eq(false).count().exec();
  }
}

module.exports = AuditRepository;
