const BaseRepository = require('./BaseRepository');

class InstallmentMidRepository extends BaseRepository {
  constructor({ InstallmentMidModel }) {
    super(InstallmentMidModel);
  }

  async getById(id) {
    return this.model.query('id').eq(id).exec();
  }

  async getUniqueBankTerm(bank, term) {
    return this.model.query('bank').eq(bank).where('term').eq(term).exec();
  }

  searchQuery(data) {
    const scan = this.model.scan();
    Object.keys(data.filter).forEach((key) => {
      scan.filter(key).eq(data.filter[key]);
    });
    return scan;
  }

  async searchFilter(data) {
    const scan = this.searchQuery(data);
    if (typeof data.pagination.start !== 'undefined' && data.pagination.start !== '') {
      return scan.startAt({ id: { S: data.pagination.start } }).exec();
    }
    return scan.exec();
  }

  async getAllBankTerm() {
    return this.model.scan().attributes(['bank', 'term']).all().exec();
  }

  async listAll(data) {
    const scan = this.model.scan();
    if (typeof data.pagination.start !== 'undefined' && data.pagination.start !== '') {
      return scan.startAt(JSON.parse(data.pagination.start)).limit(data.pagination.limit).exec();
    }
    return scan.limit(data.pagination.limit).exec();
  }
}

module.exports = InstallmentMidRepository;
