const BaseRepository = require('./BaseRepository');

class GeneratedReportRepository extends BaseRepository {
  constructor({ GeneratedReportModel }) {
    super(GeneratedReportModel);
  }

  async searchFilter(data) {
    const { filter, pagination } = data;
    const query = this.model.query('type').eq(filter.type);
    if (Object.keys(filter).length >= 1) {
      Object.entries(filter).forEach((item) => {
        const [filterKey, filterValue] = item;
        if (filterKey === 'createdAt') {
          const { start, end } = filterValue;
          query.filter('createdAt').between(start, end);
        } else if (filterKey !== 'type') {
          query.filter(filterKey).contains(filterValue);
        } else if (filterKey === 'month') {
          query('type').eq('').filter('month').eq(filterValue);
        }
      });
    }
    if (pagination.startKeys !== '') {
      query.startAt(JSON.parse(pagination.startKeys));
    }
    return query.limit(pagination.limit).exec();
  }

  async getData(id) {
    return this.model.query('id').eq(id).exec();
  }
}

module.exports = GeneratedReportRepository;
