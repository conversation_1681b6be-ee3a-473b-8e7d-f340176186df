const BaseRepository = require('./BaseRepository');

class ChannelRepository extends BaseRepository {
  constructor({ ChannelModel }) {
    super(ChannelModel);
  }

  async getByEmail(email) {
    return this.model.scan().filter('email').eq(email).exec();
  }

  async getAllName() {
    return this.model.scan().all().attributes(['id', 'name']).exec();
  }

  async getByChannelId(channelId) {
    return this.model.scan().filter('channelId').eq(channelId).exec();
  }

  async getByChannelCode(channelCode) {
    return this.model.scan().filter('channelCode').eq(channelCode).exec();
  }

  async getByClientId(clientId) {
    return this.model.scan().filter('clientId').eq(clientId).exec();
  }

  async getVerified() {
    return this.model
      .scan('isVerified')
      .eq(true)
      .attributes(['id', 'name', 'channelId', 'clientId', 'clientSecret'])
      .exec();
  }

  async getVerifiedPayByLinkChannels() {
    return this.model
      .scan('isVerified')
      .eq(true)
      .filter('isForPayByLink')
      .eq(true)
      .attributes(['id', 'name', 'channelId', 'clientId', 'clientSecret'])
      .exec();
  }

  async getVerifiedPayByLinkChannelById(channelId) {
    const result = await this.model
      .query('id')
      .eq(channelId)
      .filter('isVerified')
      .eq(true)
      .filter('isForPayByLink')
      .eq(true)
      .attributes(['id', 'channelCode'])
      .exec();

    return result[0] || null;
  }

  async getById(id) {
    return this.model.get(id);
  }

  async getChannelName(id) {
    return this.model.query('id').eq(id).attributes(['id', 'name']).exec();
  }

  async getChannelForTransaction() {
    return this.model.scan().attributes(['id', 'name', 'channelId']).exec();
  }

  async searchFilter(data) {
    const scan = this.model.scan();
    Object.keys(data.filter).forEach((key) => {
      scan.filter(key).contains(data.filter[key]);
    });
    return scan.exec();
  }

  async getBankDiscount() {
    return this.model.scan().attributes(['id', 'bankDiscount']).exec();
  }

  async getNonBillChannels() {
    return this.model.scan('billType').eq('NonBill').filter('isVerified').eq(true).attributes(['id', 'name']).exec();
  }

  async getExistingSubMerchants() {
    // return this.model.scan('serviceType').not().null()
    return this.model
      .scan('serviceType')
      .exists() // serviceType must exists
      .attributes(['id', 'name', 'serviceType', 'merchantType'])
      .exec();
  }

  // Check Globe SubMerchant
  async getByGlobeSubMerchant(serviceType) {
    return this.model.scan().filter('serviceType').eq(serviceType).exec();
  }

  // Check ECPay SubMerchants
  async getByECPaySubMerchants(serviceType) {
    return this.model.scan().filter('ecpayServiceType').eq(serviceType).exec();
  }

  // Globe SubMerchant
  async getGlobeSubMerchant() {
    // return this.model.scan('serviceType').not().null()
    return this.model
      .scan('serviceType')
      .exists() // serviceType must exists
      .attributes(['id', 'name', 'serviceType'])
      .exec();
  }

  // ECPay SubMerchant
  async getECPaySubMerchant() {
    // return this.model.scan('ecpayServiceType').not().null()
    return this.model
      .scan('ecpayServiceType')
      .exists() // ecpayServiceType must exists
      .attributes(['id', 'name', 'ecpayServiceType'])
      .exec();
  }

  // Channel By Bill Type
  async getChannelByBillType(type) {
    return this.model.scan('billType').eq(type).filter('isVerified').eq(true).exec();
  }

  async getByGcreditSubMerchantId(gcreditSubMerchantId, id) {
    return this.model.scan().filter('gcreditSubMerchantId').eq(gcreditSubMerchantId).exec();
  }

  async getBatch(channelIds) {
    return this.model.batchGet(channelIds);
  }
}

module.exports = ChannelRepository;
