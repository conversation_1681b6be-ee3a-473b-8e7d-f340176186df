const BaseAthenaGateway = require('./BaseAthenaGateway');

class PaybillTransactionLogsGateway extends BaseAthenaGateway {
  constructor(container) {
    const { config } = container;
    const database = {
      name: `rudy_db_migration_${process.env.STAGE}`,
      comment: 'Creates db if not exists',
      bucket: config.athena.bucket,
    };
    const table = {
      name: 'transaction_tracking_logs',
      serdeFormat: config.athena.serdeFormat,
      columns: [
        'order_ref_code',
        'seller_name',
        'payment_method',
        'description',
        'status',
        'subs_lname',
        'subs_fname',
        'subs_mname',
        'subs_accnt_no',
        'subs_globe_no',
        'subs_email_address',
        'datetime_added',
        'datetime_on_hold',
        'datetime_completed',
        'sent_amount',
        'approved_amount',
        'remarks',
        'payment_id',
        'trans_id1',
        'trans_id2',
        'account_type',
        'outstanding_balance',
      ],
      filters: {
        orderRefCode: 'order_ref_code',
      },
    };
    super(database, table, container);
  }
}

module.exports = PaybillTransactionLogsGateway;
