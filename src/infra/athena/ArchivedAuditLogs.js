const BaseAthenaGateway = require('./BaseAthenaGateway');

class ArchivedAuditLogs extends BaseAthenaGateway {
  constructor(container) {
    const { config } = container;
    const database = {
      name: `payment_service_${process.env.STAGE}`,
      comment: 'Creates db if not exists',
      bucket: config.athena.bucket,
    };
    const table = {
      name: 'payment_service_auditlogs',
      serdeFormat: config.athena.serdeFormat,
      columns: [
        'id',
        'userId',
        'userEmail',
        'userName',
        'roleId',
        'roleName',
        'ipAddress',
        'userAgent',
        'category',
        'isViewed',
        'reasonToDelete',
        'reasonToUpdate',
        'oldValue',
        'newValue',
        'createdAt',
      ],
      filters: {
        id: 'id',
        category: 'category',
        userEmail: 'userEmail',
        userAgent: 'userAgent',
        createdAt: 'createdAt',
      },
    };
    super(database, table, container);
  }
}

module.exports = ArchivedAuditLogs;
