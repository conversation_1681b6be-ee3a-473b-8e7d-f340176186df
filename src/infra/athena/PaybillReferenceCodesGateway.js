const BaseAthenaGateway = require('./BaseAthenaGateway');

class PaybillReferenceCodesGateway extends BaseAthenaGateway {
  constructor(container) {
    const { config } = container;
    const database = {
      name: `rudy_db_migration_${process.env.STAGE}`,
      comment: 'Creates db if not exists',
      bucket: config.athena.bucket,
    };

    const table = {
      name: 'reference_codes',
      serdeFormat: config.athena.serdeFormat,
      columns: ['order_ref_code', 'payment_id', 'trans_id1', 'trans_id2', 'datetime_added'],
      filters: {
        orderRefCode: 'order_ref_code',
      },
    };
    super(database, table, container);
  }
}

module.exports = PaybillReferenceCodesGateway;
