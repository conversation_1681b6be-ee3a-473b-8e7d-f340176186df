const fs = require('fs');
const { asClass } = require('awilix');

const gateway = {};
fs.readdirSync(`${process.env.PWD}/src/infra/athena`)
  .filter(
    (file) =>
      file.indexOf('.') !== 0 && file !== 'index.js' && file !== 'BaseAthenaGateway.js' && file.slice(-3) === '.js'
  )
  .forEach((file) => {
    const repositoryName = file.split('.')[0];
    const repository = require(`./${file}`);
    gateway[repositoryName.replace(/^./, (f) => f.toLowerCase())] = asClass(repository).singleton();
  });

module.exports = gateway;
