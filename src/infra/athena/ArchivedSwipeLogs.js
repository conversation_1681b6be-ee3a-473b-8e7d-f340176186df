const BaseAthenaGateway = require('./BaseAthenaGateway');

class ArchivedSwipeLogs extends BaseAthenaGateway {
  constructor(container) {
    const { config } = container;
    const database = {
      name: `payment_service_${process.env.STAGE}`,
      comment: 'Creates db if not exists',
      bucket: config.athena.bucket,
    };
    const table = {
      name: 'payment_service_swipe_transactions',
      serdeFormat: config.athena.serdeFormat,
      columns: [
        'id',
        'transactionId',
        'accountNo',
        'amount',
        'brand',
        'channelId',
        'channelName',
        'content',
        'contentPartnerShortName',
        'currency',
        'customerSegment',
        'customerSubType',
        'dailyReportDate',
        'date',
        'emailAddress',
        'entity',
        'gcashReferenceNo',
        'modeOfPayment',
        'monthlyReportDate',
        'msisdn',
        'notificationFormattedDate',
        'paymentMethod',
        'paymentStatus',
        'productDescription',
        'refundAmount',
        'refundStatus',
        'sku',
        'srn',
        'subscriberType',
        'subServiceType',
        'voucherDispenseStatus',
        'paymentGateway',
        'ttl',
      ],
      filters: {
        id: 'id',
        gcashReferenceNo: 'gcashReferenceNo',
        accountNo: 'accountNo',
        msisdn: 'msisdn',
        brand: 'brand',
        paymentStatus: 'paymentStatus',
        date: 'date',
      },
    };
    super(database, table, container);
  }
}

module.exports = ArchivedSwipeLogs;
