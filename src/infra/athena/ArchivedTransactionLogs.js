const BaseAthenaGateway = require('./BaseAthenaGateway');

class ArchivedTransactionLogs extends BaseAthenaGateway {
  constructor(container) {
    const { config } = container;
    const database = {
      name: `payment_service_${process.env.STAGE}`,
      comment: 'Creates db if not exists',
      bucket: config.athena.bucket,
    };
    const table = {
      name: 'payment_service_transactions',
      serdeFormat: config.athena.serdeFormat,
      columns: [
        'id',
        'transactionId',
        'channelId',
        'entityName',
        'status',
        'monthlyReportDate',
        'dailyReportDate',
        'gatewayProcessor',
        'accountNumber',
        'state',
        'timestamp',
        'amountCurrency',
        'mobileNumber',
        'amountValue',
        'acquirerCode',
        'paymentMethod',
        'fundingSource',
        'channelReference',
        'billerName',
        'subMerchantId',
        'postPaymentReason',
        'postPaymentEsbMessageId',
        'isOrTransaction',
        'orStatus',
        'lukeOrTimestamp',
        'refundApprovalStatus',
        'refundAmount',
        'refundReason',
        'refundRejectedTimestamp',
        'refundStatus',
        'isInstallment',
        'installmentTerm',
        'installmentPaymentId',
        'refundId',
        'isAutoRefund',
        'refundApprovedTimestamp',
      ],
      filters: {
        id: 'id',
        gatewayProcessor: 'gatewayProcessor',
        accountNumber: 'accountNumber',
        status: 'status',
        mobileNumber: 'mobileNumber',
        timestamp: 'timestamp',
      },
    };
    super(database, table, container);
  }
}

module.exports = ArchivedTransactionLogs;
