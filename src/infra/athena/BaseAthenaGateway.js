const {
  AthenaClient,
  GetQueryExecutionCommand,
  StartQueryExecutionCommand,
  GetQueryResultsCommand,
} = require('@aws-sdk/client-athena');

class BaseAthenaGateway {
  constructor(database, table, container) {
    const { logger } = container;
    this.logger = logger;
    this.database = database;
    this.table = table;
    this.athena = new AthenaClient({
      region: process.env.AWS_ATHENA_REGION,
    });
    this.initialize();
  }

  initialize() {
    try {
      if (process.env.STAGE !== 'local') {
        this.createDatabase();
        this.createTable();
      }
    } catch (error) {
      this.logger.error(error);
      this.logger.error('Database and tables could not be initialized!');
    }
  }

  async search(data, orderby) {
    const query = `select * from ${this.database.name}.${this.table.name} ${this.whereClause(data.filter)}${orderby}limit 1000;`;
    const outputLocation = `s3://${this.database.bucket}/results`;
    const queryExecution = await this.startQueryExecution({
      query,
      outputLocation,
    });
    const status = await this.getQuerySuccessfulExecution(queryExecution);
    let result = [];
    if (status === 'SUCCEEDED') {
      const results = await this.getQueryResults(queryExecution);
      const {
        ResultSet: { Rows },
      } = results;
      Rows.shift();
      result = Rows;
    }
    return this.tableMapper(result);
  }

  whereClause(filterParams) {
    const whereClause = [];

    Object.entries(filterParams).forEach((filters) => {
      const [filterKey, filterValue] = filters;
      Object.entries(this.table.filters).forEach((tableFilter) => {
        const [tableKey, tableName] = tableFilter;
        if (tableKey === filterKey) {
          whereClause.push(` ${tableName} = '${filterValue}'`);
        }
      });
    });
    return whereClause.length !== 0 ? ` where ${whereClause.join(' AND ')}` : '';
  }

  static pagination(pagination) {
    let paginationClause = '';

    const { limit, offset } = pagination;

    paginationClause += limit !== undefined ? `limit ${limit}` : '';
    paginationClause += offset !== undefined ? `offset ${offset}` : '';
    return paginationClause;
  }

  async tableMapper(rows) {
    const result = [];
    rows.forEach((row) => {
      const newRow = {};
      for (let index = 0; index < row.Data.length; index += 1) {
        const { VarCharValue } = row.Data[index];
        if (VarCharValue) {
          newRow[this.table.columns[index]] = VarCharValue.trim();
        }
      }

      const newData = {
        ...newRow,
        isViewed: Boolean(newRow?.isViewed),
      };

      result.push(newData);
    });

    return result;
  }

  async getQuerySuccessfulExecution(queryExecution) {
    const sleep = (ms) =>
      new Promise((resolve) => {
        setTimeout(resolve, ms);
      });
    await sleep(2000);
    const input = {
      QueryExecutionId: queryExecution,
    };
    const command = new GetQueryExecutionCommand(input);
    const queryExecutionResult = await this.athena.send(command);
    const {
      QueryExecution: { Status },
    } = queryExecutionResult;
    if (Status.State === 'SUCCEEDED') {
      return Status.State;
    }
    if (Status.State === 'FAILED' || Status.State === 'CANCELLED') {
      this.logger.error(`Query Execution Status is ${Status.State}`);
      return 'FAILED';
    }
    return this.getQuerySuccessfulExecution(queryExecution);
  }

  async getQueryResults(queryExecution) {
    const input = {
      QueryExecutionId: queryExecution,
    };
    const command = new GetQueryResultsCommand(input);
    return this.athena.send(command);
  }

  async startQueryExecution(params) {
    const { query, outputLocation } = params;

    const input = {
      QueryString: query,
      ResultConfiguration: {
        OutputLocation: outputLocation,
      },
    };
    const command = new StartQueryExecutionCommand(input);
    try {
      const result = await this.athena.send(command);
      return result.QueryExecutionId;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async createTable() {
    const { columns } = this.table;
    const { bucket } = this.database;
    const outputLocation = `s3://${bucket}/results`;
    const location = `s3://${bucket}/${this.table.name}`;
    const query = `CREATE EXTERNAL TABLE IF NOT EXISTS ${this.database.name}.${this.table.name} (
          ${columns.map((column) => `${column} string`)})
          ROW FORMAT SERDE '${this.table.serdeFormat}'
          WITH SERDEPROPERTIES (
            'serialization.format' = ',',
            'field.delim' = ','
          ) LOCATION '${location}'
          TBLPROPERTIES ('has_encrypted_data'='true');
          `;

    try {
      await this.startQueryExecution({
        query,
        outputLocation,
      });
    } catch (error) {
      this.logger.error(`Table "${this.table.name}" could not be initialized!`);
    }
  }

  async createDatabase() {
    const outputLocation = `s3://${this.database.bucket}/results`;
    let results;
    try {
      results = await this.startQueryExecution({
        query: `CREATE DATABASE IF NOT EXISTS ${this.database.name}
                  COMMENT '${this.database.comment}'
                  LOCATION '${this.database.bucket}'`,
        outputLocation,
      });
    } catch (error) {
      this.logger.error(`Database "${this.database.name}" could not be initialized!`);
    }
    return results;
  }
}

module.exports = BaseAthenaGateway;
