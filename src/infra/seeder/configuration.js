const config = [
  {
    name: 'ipay88SoapAction',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-12-17T04:51:21.831Z',
    value: 'http://tempuri.org/IGatewayService/EntryPageFunctionality',
  },
  {
    name: 'adaExpirationPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-11-23T04:51:21.831Z',
    value: 37761,
  },
  {
    name: 'maximumDelayMinutes',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-06-05T16:45:09.127Z',
    value: 3,
  },
  {
    name: 'xenditRefundUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2022-07-25T04:51:21.831Z',
    value: 'https://api.xendit.co/refunds',
  },
  {
    name: 'bpiApiOauthUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-12T02:09:20.826Z',
    value: 'https://testoauth.bpi.com.ph',
  },
  {
    name: 'channelReportEmailPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 52014,
  },
  {
    name: 'innoveEmailNotification',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: true,
  },
  {
    name: 'overAllSnapshot',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-05-03T02:30:35.034Z',
    value:
      '{""id"":{""S"":""GTB1714702511596304""},""entityName"":{""S"":""payment""},""transactionId"":{""S"":""910b8395-731e-8750-d087-9f677fb850f2""},""timestamp"":{""S"":""2024-05-03T02:21:12.795Z""}}',
  },
  {
    name: 'monthlyContentGCashReportPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 32341,
  },
  {
    name: 'maxReservationDayLimit',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-10-29T04:00:54.079Z',
    value: 1,
  },
  {
    name: 'xenditCreditCardChargesUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2022-07-25T04:51:21.831Z',
    value: 'https://api.xendit.co/credit_card_charges',
  },
  {
    name: 'ipay88WebServiceMerchantKey',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-12-17T04:51:21.831Z',
    value: 'MjJPb8spl6',
  },
  {
    name: 'BL-TapGo',
    adyenMerchantAccount: 'Globe-TapGo',
    content: 'TapGo',
    createdAt: '2021-11-18T07:09:36.961Z',
    type: 'billLinerMid',
    updatedAt: '2021-11-18T07:09:36.961Z',
    value: '',
  },
  {
    name: 'cardRefundReason',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value:
      '[{""reason"":""Double Posting""},{""reason"":""Erreoneous Amount""},{""reason"":""Unposted Payment""},{""reason"":""Test""},{""reason"":""zxzxzxzxzXZXZZXXZZXXZxz""}]',
  },
  {
    name: 'updateTransactionTTL',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-04-29T15:00:16.964Z',
    value: '',
  },
  {
    name: 'BL-Lazada',
    adyenMerchantAccount: 'Globe-Lazada',
    content: 'Lazada',
    createdAt: '2021-11-18T07:09:36.961Z',
    type: 'billLinerMid',
    updatedAt: '2021-11-18T07:09:36.961Z',
    value: '',
  },
  {
    name: 'payByLinkUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-10-15T04:51:21.831Z',
    value: 'https://checkout-test.adyen.com/v68/paymentLinks',
  },
  {
    name: 'adaMerchantCode',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-10-04T02:09:20.826Z',
    value: 'PH00136',
  },
  {
    name: 'xenditCreateCustomerUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2022-07-25T04:51:21.831Z',
    value: 'https://api.xendit.co/customers',
  },
  {
    name: 'successfulPaymentPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-07-20T06:25:33.954Z',
    value: 32345,
  },
  {
    name: 'channelReportRecipient',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: '<EMAIL>,<EMAIL>',
  },
  {
    name: 'dropInPaymentsDetailsAPI',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-04-12T04:51:21.831Z',
    value: 'https://checkout-test.adyen.com/v67/payments/details',
  },
  {
    name: 'xenditDDPaymentRequestUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2022-07-25T04:51:21.831Z',
    value: 'https://api.xendit.co/payment_requests',
  },
  {
    name: 'BL-Grab',
    adyenMerchantAccount: 'Globe-Grab',
    content: 'Grab',
    createdAt: '2021-11-18T07:09:36.961Z',
    type: 'billLinerMid',
    updatedAt: '2021-11-18T07:09:36.961Z',
    value: '',
  },
  {
    name: 'gcashRefundRetries',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 3,
  },
  {
    name: 'gcashRejectedPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-03-24T07:31:23.701Z',
    value: 44921,
  },
  {
    name: 'refundDaysValidity',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-06-05T16:45:09.127Z',
    value: 180,
  },
  {
    name: 'iccbsEmailNotification',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-05-20T07:32:13.975Z',
    value: true,
  },
  {
    name: 'Numbers',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'env',
    updatedAt: '2020-10-29T04:00:54.079Z',
    value: 1,
  },
  {
    name: 'gcashPaymentMethod',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '',
    value: 'gcash',
  },
  {
    name: 'adaDeclinedMaxRetry',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-01-29T02:09:20.826Z',
    value: 1,
  },
  {
    name: 'bayanEmailNotificationPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 1526,
  },
  {
    name: 'refundReason',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value:
      '[{""reason"":""Overpayment""},{""reason"":""Double debit""},{""reason"":""Incorrect Account posting""},{""reason"":""Unposted payment""},{""reason"":""Partially Posted payment""},{""reason"":""Erroneous Amount""},{""reason"":""zxzxzxzxzXZXZZXXZZXXZxz""}]',
  },
  {
    name: 'retryIndicator',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-02-03T04:51:21.831Z',
    value: true,
  },
  {
    name: 'ipay88WebServiceMerchantCode',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-12-17T04:51:21.831Z',
    value: 'PH00427',
  },
  {
    name: 'paymentMethodGcash',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 'mynt',
  },
  {
    name: 'enableDelayMonitoring',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-06-05T16:45:09.127Z',
    value: true,
  },
  {
    name: 'swipeBrandsListPostpaid',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-10',
    value: [
      {
        S: 'PT',
      },
      {
        S: 'POSTPAID',
      },
    ],
  },
  {
    name: 'adyenTokenUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'env',
    updatedAt: '2021-09-01T02:09:20.826Z',
    value: 'https://checkout-test.adyen.com/checkout/v67/paymentMethods',
  },
  {
    name: 'BL-WeTV',
    adyenMerchantAccount: 'Globe-WeTV',
    content: 'WeTV',
    createdAt: '2021-11-18T07:09:36.961Z',
    type: 'billLinerMid',
    updatedAt: '2021-11-18T07:09:36.961Z',
    value: '',
  },
  {
    name: 'PAYBILL_CORP_CHANNEL_ID',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'env',
    updatedAt: 'Fri Aug 27 2021 03:49',
    value: '3a0299dc-eea7-4300-86e9-054f2ed07d91',
  },
  {
    name: 'creditCardReportRecipient',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: '<EMAIL>',
  },
  {
    name: 'ipay88BackendUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-09-17T00:00:23.701Z',
    value:
      'https://25w2ybpp28.execute-api.ap-southeast-1.amazonaws.com/dev/api/ipay88/callback',
  },
  {
    name: 'creditCardReportEmailPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 25363,
  },
  {
    name: 'swipeBrandsList',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-10',
    value: [
      {
        S: 'PT',
      },
      {
        S: 'GHP',
      },
      {
        S: 'TM',
      },
      {
        S: 'HPW',
      },
      {
        S: 'POSTPAID',
      },
      {
        S: 'PREPAID',
      },
    ],
  },
  {
    name: 'bpiClientSecret',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-12T02:09:20.826Z',
    value: 'eY2wP0tG2xH5wC4rT2tT3yR5nE5rX6wE1rI6kS8vT3bO4sX2nV',
  },
  {
    name: 'emailNotificationSchedule',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-06-05T16:45:09.127Z',
    value: 3,
  },
  {
    name: 'refreshTime',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 15,
  },
  {
    name: 'globeEmailNotificationPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 1524,
  },
  {
    name: 'innoveEmailNotificationPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 1525,
  },
  {
    name: 'dropInPaymentMethodsAPI',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-04-12T04:51:21.831Z',
    value: 'https://checkout-test.adyen.com/v67/paymentMethods',
  },
  {
    name: 'iccbsEmailNotificationPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-05-20T07:32:13.975Z',
    value: 1527,
  },
  {
    name: 'xenditSecretKey',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2022-07-22T04:51:21.831Z',
    value:
      'xnd_development_nOT95faHDIZSzAhpos43W9DovW04ykHmwa9dEpDFoFtvx1oKRrCEikkBR3IpVA',
  },
  {
    name: 'paymentMethodCard',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 'adyen',
  },
  {
    name: 'delayPaymentNotification',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-06-05T16:45:09.127Z',
    value: '<EMAIL>',
  },
  {
    name: 'adyenTokenUrlDisable',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'env',
    updatedAt: '2021-09-01T02:09:20.826Z',
    value: 'https://pal-test.adyen.com/pal/servlet/Recurring/v49/disable',
  },
  {
    name: 'ecpayReportRecipient',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: '<EMAIL>',
  },
  {
    name: 'PayO-GatewayMethods',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: '',
    updatedAt: '',
    value:
      '{""gcash"":[{""paymentMethod"":""dragonpay_gcash"",""name"":""GCash""}],""adyen"":[{""paymentMethod"":""mastercard"",""name"":""MasterCard""},{""paymentMethod"":""visa"",""name"":""Visa""},{""paymentMethod"":""amex"",""name"":""American Express""}],""xendit"":[{""paymentMethod"":""cc_dc"",""name"":""Xendit CC/DC""},{""paymentMethod"":""grabpay"",""name"":""Xendit Grabpay""},{""paymentMethod"":""paymaya"",""name"":""Xendit Paymaya""},{""paymentMethod"":""shopeepay"",""name"":""Xendit Shopeepay""},{""paymentMethod"":""bpi"",""name"":""Xendit DD BPI""},{""paymentMethod"":""rcbc"",""name"":""Xendit DD RCBC""},{""paymentMethod"":""ubp"",""name"":""Xendit DD UBP""}]}',
  },
  {
    name: 'adaDeclinedPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-03-25T04:51:21.831Z',
    value: 44901,
  },
  {
    name: 'failedVoucherRefundNotificationEmailPatternID',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-05T00:00:23.701Z',
    value: 32346,
  },
  {
    name: 'dropInPaymentsAPI',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-04-12T04:51:21.831Z',
    value: 'https://checkout-test.adyen.com/v67/payments',
  },
  {
    name: 'BL-SAMPLE',
    adyenMerchantAccount: 'SAMPLE',
    content: 'SAMPLE',
    createdAt: '2021-11-15T07:26:40.178Z',
    type: 'billLinerMid',
    updatedAt: '2021-11-15T07:26:40.178Z',
    value: '',
  },
  {
    name: 'billingReportPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 72364,
  },
  {
    name: 'secureHash',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-09-22T02:09:20.826Z',
    value: 'sha1',
  },
  {
    name: 'normalizedPaymentNotificationPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-06-05T16:45:09.127Z',
    value: 30001,
  },
  {
    name: 'ipay88KeyEncryption',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-09-16T04:51:21.831Z',
    value: "'+zp4W5xgEN4yVeEVdmeaaQ==",
  },
  {
    name: 'paymentMethodBank',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-05-28T04:17:40.147Z',
    value: 'bpi',
  },
  {
    name: 'monthlyContentGCashReportRecipient',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: '<EMAIL>',
  },
  {
    name: 'ADYEN_TOKEN_URL',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'env',
    updatedAt: 'Wed Aug 04 2021 05:39:58 GMT+0800 (Philippine Standard Time)',
    value: 'https://pal-test.adyen.com/pal/servlet/Recurring/v49',
  },
  {
    name: 'BL-NBA',
    adyenMerchantAccount: 'Globe-NBA',
    content: 'NBA',
    createdAt: '2021-11-18T07:09:36.961Z',
    type: 'billLinerMid',
    updatedAt: '2021-11-18T07:09:36.961Z',
    value: '',
  },
  {
    name: 'lastMaintenanceModeTimestamp',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: '',
    updatedAt: '',
    value: '2021-10-13T02:52:56.999Z',
  },
  {
    name: 'gCreditEcpaySubMerchantId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-12-13T04:51:21.831Z',
  },
  {
    name: 'normalizedPaymentNotification',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-06-05T16:45:09.127Z',
    value: '<EMAIL>',
  },
  {
    name: 'xenditPaymentRequestUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2022-07-22T04:51:21.831Z',
    value: 'https://api.xendit.co/payment_requests',
  },
  {
    name: 'successVoucherPaymentNotificationEmailPatternID',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-10',
    value: 32345,
  },
  {
    name: 'manageCardMerchantCode',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-12-10T04:51:21.831Z',
    value: 'PH00098',
  },
  {
    name: 'gcashRefundFailedPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-09-18',
    value: 34841,
  },
  {
    name: 'transactionSnapshot',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-05-03T02:30:22.199Z',
    value:
      '{""id"":{""S"":""GTB1714702511596304""},""entityName"":{""S"":""payment""},""transactionId"":{""S"":""910b8395-731e-8750-d087-9f677fb850f2""},""timestamp"":{""S"":""2024-05-03T02:21:12.795Z""}}',
  },
  {
    name: 'amaxLoadRetailer',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 1047,
  },
  {
    name: 'esbPostPayment',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-09-23T04:51:21.831Z',
    value: 'PostPayment',
  },
  {
    name: 'ipay88CheckoutUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-09-17T04:51:21.831Z',
    value: 'https://sandbox.ipay88.com.ph/epayment/entry.asp',
  },
  {
    name: 'globeEmailNotification',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: true,
  },
  {
    name: 'successVoucherRefundNotificationSMSPatternID',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-05T00:00:23.701Z',
    value: 32363,
  },
  {
    name: 'successVoucherPaymentNotificationSMSPatternID',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-10',
    value: 32342,
  },
  {
    name: 'lastNormalizeNotificationSentTimestamp',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-06-05T16:45:09.127Z',
    value: '2020-06-05T16:45:09.127Z',
  },
  {
    name: 'failedVoucherRefundNotificationSMSPatternID',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-06T00:00:23.701Z',
    value: 32364,
  },
  {
    name: 'adyenPreAuthPaymentUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-10-29T04:00:54.079Z',
    value: 'https://checkout-test.adyen.com/checkout/V65/payments',
  },
  {
    name: 'gCreditSubMerchantId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-12-13T04:51:21.831Z',
  },
  {
    name: 'dailyContentGCashReportRecipient',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: '<EMAIL>',
  },
  {
    name: 'orGenerationConfig',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-06-25T02:52:37.954Z',
    value: 'uni',
  },
  {
    name: 'ipay88AdaCallbackUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-09-17T04:51:21.831Z',
    value:
      'https://25w2ybpp28.execute-api.ap-southeast-1.amazonaws.com/dev/api/ada/callback',
  },
  {
    name: 'amaxLoadConsumer',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 1047,
  },
  {
    name: 'failedVoucherPaymentNotificationEmailPatternID',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-10',
    value: 32365,
  },
  {
    name: 'adyenCaptureUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-10-29T04:00:54.079Z',
    value: 'https://pal-test.adyen.com/pal/servlet/Payment/V64/capture',
  },
  {
    name: 'preAuthPaymentId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-09-16T04:51:21.831Z',
    value: 25,
  },
  {
    name: 'BL-DocuBay',
    adyenMerchantAccount: 'Globe-DocuBay',
    content: 'DocuBay',
    createdAt: '2021-11-18T07:09:36.961Z',
    type: 'billLinerMid',
    updatedAt: '2021-11-18T07:09:36.961Z',
    value: '',
  },
  {
    name: 'SampleJJString',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: '',
    updatedAt: '',
    value: 500,
  },
  {
    name: 'dailyContentGCashReportPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 32321,
  },
  {
    name: 'paymentServiceApiMaintenance',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: false,
  },
  {
    name: 'globeOneReportRecipient',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: '<EMAIL>',
  },
  {
    name: 'BL-GenericContent',
    adyenMerchantAccount: 'Globe-Content',
    content: 'GenericContent',
    createdAt: '2021-11-18T07:09:36.961Z',
    type: 'billLinerMid',
    updatedAt: '2021-11-18T07:09:36.961Z',
    value: '',
  },
  {
    name: 'bpiClientId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-12T02:09:20.826Z',
    value: '8a250556-afe5-4eaa-b8a8-623a34d33d51',
  },
  {
    name: 'tokenizationPassPhrase',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-24T02:09:20.826Z',
    value: '3be1413ee79655f59b0439e1fcacebfb',
  },
  {
    name: 'collectionReportEmailPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 44144,
  },
  {
    name: 'bayanEmailNotification',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: true,
  },
  {
    name: 'gcashOrderQueryRetry',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-01-21T02:09:20.826Z',
    value: 1,
  },
  {
    name: 'ipay88SoapUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-12-17T04:51:21.831Z',
    value:
      'https://payment.ipay88.com.ph/ePayment/WebService/MHPHGatewayService/GatewayService.svc',
  },
  {
    name: 'recurringSnapshot',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-01-15T04:30:11.449Z',
    value:
      '{""id"":""REC1607511798780587"",""transactionId"":""e25582b9-3e03-a710-1ceb-c13b2bf81f1b"",""entityName"":""payment"",""timestamp"":""2021-01-12T04:51:21.831Z""}',
  },
  {
    name: 'successVoucherRefundNotificationEmailPatternID',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-06T00:00:23.701Z',
    value: 32366,
  },
  {
    name: 'BL-Shopee',
    adyenMerchantAccount: 'Globe-Shopee',
    content: 'Shopee',
    createdAt: '2021-11-18T07:09:36.961Z',
    type: 'billLinerMid',
    updatedAt: '2021-11-18T07:09:36.961Z',
    value: '',
  },
  {
    name: 'swipeORPaymentType',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-04-12T03:23:51.341Z',
    value: [
      {
        M: {
          name: {
            S: 'hey',
          },
          description: {
            S: 'sampledesc',
          },
          or: {
            S: '123',
          },
          orVat: {
            S: 'vat',
          },
        },
      },
    ],
  },
  {
    name: 'ecpayReportEmailPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 31521,
  },
  {
    name: 'globeOneReportEmailPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: 59473,
  },
  {
    name: 'updateEventTTL',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-04-22T09:36:27.218Z',
    value: '',
  },
  {
    name: 'lastDelayNotificationSentTimestamp',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: '',
    updatedAt: '',
    value: '2021-10-13T02:52:56.979Z',
  },
  {
    name: 'billingReportRecipient',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: '<EMAIL>,<EMAIL>',
  },
  {
    name: 'adaMerchantKey',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-10-04T02:09:20.826Z',
    value: 'hu7RtT5LLz',
  },
  {
    name: 'bpiRedirectUri',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-09-24T02:09:20.826Z',
    value: 'https://test.payments-globe.com.ph/',
  },
  {
    name: 'collectionReportRecipient',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: '<EMAIL>,<EMAIL>',
  },
  {
    name: 'BL-YTS',
    adyenMerchantAccount: 'GlobePH',
    content: 'YTS',
    createdAt: '2021-11-15T07:26:40.178Z',
    type: 'billLinerMid',
    updatedAt: '2021-11-15T07:26:40.178Z',
    value: '',
  },
  {
    name: 'isTokenEncrypted',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-01-13T04:51:21.831Z',
    value: false,
  },
  {
    name: 'ipay88RequeryUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-10-06T04:51:21.831Z',
    value: 'https://sandbox.ipay88.com.ph/epayment/enquiry.asp',
  },
  {
    name: 'swipeBrandsListPrepaid',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-10',
    value: [
      {
        S: 'GHP',
      },
      {
        S: 'TM',
      },
      {
        S: 'HPW',
      },
      {
        S: 'PREPAID',
      },
    ],
  },
  {
    name: 'globeOneChannelId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-05-17T06:51:30.524Z',
    value: '03bf3f11-ffb7-437a-90ac-f9655bc5e390',
  },
  {
    name: 'delayPaymentNotificationPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-06-05T16:45:09.127Z',
    value: 29981,
  },
  {
    name: 'failedVoucherPaymentNotificationSMSPatternID',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-10',
    value: 32343,
  },
  {
    name: 'failedPaymentPatternId',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-07-20T06:25:33.954Z',
    value: 32365,
  },
  {
    name: 'BL-SPOTI2F-Y',
    adyenMerchantAccount: 'GlobePH',
    content: 'SPOTI2F-Y',
    createdAt: '2021-11-12T07:55:38.970Z',
    type: 'billLinerMid',
    updatedAt: '2021-11-12T07:55:38.970Z',
    value: '',
  },
  {
    name: 'BL-Lalamove',
    adyenMerchantAccount: 'Globe-Lalamove',
    content: 'Lalamove',
    createdAt: '2021-11-18T07:09:36.961Z',
    type: 'billLinerMid',
    updatedAt: '2021-11-18T07:09:36.961Z',
    value: '',
  },
  {
    name: 'bpiApiUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-08-12T02:09:20.826Z',
    value: 'https://apitest.bpi.com.ph',
  },
  {
    name: 'xenditCallbackToken',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2022-09-01T16:44:41.831Z',
    value: 'XNDMtc7s1vIBFUts7PGnAfieCWikbgbtuZxYd4xnarIw1lON',
  },
  {
    name: 'BL-iQiyi',
    adyenMerchantAccount: 'Globe-iQiyi',
    content: 'iQiyi',
    createdAt: '2021-11-18T07:09:36.961Z',
    type: 'billLinerMid',
    updatedAt: '2021-11-18T07:09:36.961Z',
    value: '',
  },
  {
    name: 'adyenRefundUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2021-01-26T04:51:21.831Z',
    value: 'https://pal-test.adyen.com/pal/servlet/Payment/v64/refund',
  },
  {
    name: 'PSORPaymentType',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value:
      '[{""description"":""Sample Payment Type"",""name"":""NBA-Sample"",""or"":""NBA-Promo2-G1"",""orVat"":""SampleORVAT""},{""description"":""Sample Payment Type2"",""name"":""NBA-Sample2"",""or"":""NBA-Promo2-G2"",""orVat"":""SampleORVAT2""},{""description"":""Sample Payment 1"",""name"":""SPOTIFY-300"",""or"":""SPOTIFY-Promo"",""orVat"":""SampleORVAT3""},{""description"":""Sample Payment 2"",""name"":""SPOTIFY-100"",""or"":""SPOTIFY-Promo2"",""orVat"":""SampleORVAT4""}]',
  },
  {
    name: 'configIpWhitelist',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2024-03-26T07:46:56.231Z',
    value: '{"xendit":{"callback":[]}}',
  },
  {
    name: 'ipay88TokenizationUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-09-17T04:51:21.831Z',
    value:
      'https://payment.ipay88.com.ph/ePayment/MerchantTokenization/manageCard.asp',
  },
  {
    name: 'adyenCancelUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '2020-10-29T04:00:54.079Z',
    value: 'https://pal-test.adyen.com/pal/servlet/Payment/V64/cancel',
  },
  {
    name: 'xenditGetTokenUrl',
    adyenMerchantAccount: '',
    content: '',
    createdAt: '',
    type: 'default',
    updatedAt: '',
    value: 'https://api.xendit.co/credit_card_tokens',
  },
  {
    name: 'xenditPayByLinkUrl',
    type: 'default',
    value: 'https://api.xendit.co/v2/invoices',
  },
  {
    name: 'payByLinkPaymentMethods',
    type: 'default',
    value:
      '{"xendit":["CREDIT_CARD","DD_BPI","DD_RCBC","DD_UBP","GRABPAY","PAYMAYA","SHOPEEPAY"]}',
  },
];

module.exports = config;
