const dynamoose = require('dynamoose');
const awsS3 = require('@aws-sdk/client-s3');
const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, PutCommand } = require('@aws-sdk/lib-dynamodb');
const uuidGenerator = require('../utils/Uuid');
const container = require('../../container');
const configurations = require('./configuration');
const readline = require('readline');

class Seeder {
  constructor() {
    this.userRepository = container.resolve('UserModel');
    this.roleRepository = container.resolve('RoleModel');
    this.configRepository = container.resolve('ConfigurationModel');
    this.uuid = uuidGenerator;
    this.timestamp = new Date().toISOString();
    let ddb;
    let dbconfig = {
      region: process.env.AWS_REGION,
    };
    let s3config = {
      region: process.env.AWS_REGION,
      forcePathStyle: true,
    };
    if (process.env.STAGE !== 'undefined' && process.env.STAGE === 'local') {
      // Dynamoose
      dbconfig = {
        region: process.env.DDB_AWS_REGION,
        endpoint: process.env.DDB_ENDPOINT,
        maxAttempts: process.env.DDB_MAX_ATTEMPTS || 10,
        requestHandler: {
          httpsAgent: { maxSockets: 50 },
          // requestTimeout: 60_000,
        },
        credentials: {
          accessKeyId: 'test',
          secretAccessKey: 'test',
        },
      };

      // AWS-SDK 3
      s3config = {
        ...s3config,
        endpoint: process.env.AWS_ENDPOINT,
      };
    } else {
      ddb = new dynamoose.aws.ddb.DynamoDB(dbconfig);
      dynamoose.aws.ddb.set(ddb);
    }
    this.s3 = new awsS3.S3Client(s3config);
    // Create a DynamoDB client
    const dbClient = new DynamoDBClient(dbconfig);
    // Creating a DynamoDB Document Client for easier data handling
    this.docClient = DynamoDBDocumentClient.from(dbClient);
  }

  async createSwipeORType(data) {
    const input = {
      TableName: process.env.DDB_TABLE_CONFIGURATION,
      Item: data,
    };
    const command = new PutCommand(input);
    return this.docClient.send(command).then((result) => result);
  }

  async RoleCreate(roleId) {
    const role = {
      code: '100-SuperAdmin',
      createdAt: this.timestamp,
      emailNotif: true,
      id: roleId,
      isActive: true,
      name: 'SuperAdmin',
      notes: 'This is Super Admin Can access everything',
      permissions: {
        ADADeclinedReport: ['view', 'export'],
        ADASummaryReport: ['view', 'export'],
        Archive: ['view', 'export'],
        Audit: ['view', 'export'],
        Bank: ['view', 'create', 'update', 'delete', 'import'],
        Billing: ['view', 'export'],
        CardRefundApproval: ['view', 'create', 'update', 'delete'],
        CardRefundDetailedReport: ['view', 'export'],
        CardRefundRequest: ['view', 'create', 'update', 'delete'],
        XenditRefundRequest: ['view', 'create', 'update', 'delete'],
        XenditRefundApproval: ['view', 'create', 'update', 'delete'],
        CardRefundSummaryReport: ['view', 'export'],
        Channel: ['view', 'create', 'update', 'delete'],
        ChannelReport: ['view', 'export'],
        Collection: ['view', 'export'],
        Config: ['view', 'update'],
        ContentFraudReport: ['view', 'export'],
        ContentGcashReport: ['view', 'export'],
        Dashboard: [
          'view',
          'transactions',
          'onlineCCGCash',
          'revenuePerChannel',
          'notifications',
          'transactionsPerChannel',
          'transactionsPercentage',
          'gatewayStatus',
          'performance',
          'adyen',
          'channelTransaction',
          'userMgmt',
        ],
        ECPay: ['view', 'export'],
        EndGameReport: ['view', 'export'],
        Failed: ['view', 'export'],
        Gateway: ['view', 'export'],
        GcashRefundApproval: ['view', 'create', 'update', 'delete'],
        GcashRefundDetailedReport: ['view', 'export'],
        GcashRefundRequest: ['view', 'create', 'update', 'delete'],
        GcashRefundSummaryReport: ['view', 'export'],
        GlobeOne: ['view', 'export'],
        GotsReport: ['view', 'export'],
        InstallmentMid: ['view', 'create', 'update', 'delete', 'export'],
        InstallmentReport: ['view', 'export'],
        LoadORReport: ['view', 'export'],
        LukeBatchFile: ['view', 'export'],
        Mid: ['view', 'create', 'update', 'delete', 'export'],
        MonthlyGenerated: ['view', 'export'],
        PayByLink: ['view', 'export'],
        Provider: ['view', 'create', 'update', 'delete'],
        Role: ['view', 'create', 'update', 'delete', 'import'],
        Transaction: ['view', 'export'],
        Treasury: ['view', 'export'],
        User: ['view', 'create', 'update', 'delete', 'export', 'import', 'deactivate'],
        Wireline: ['view', 'export'],
        DropinSimulator: ['view'],
        BillLinerConfig: ['view', 'create', 'delete'],
      },
      smsNotif: true,
      updatedAt: this.timestamp,
    };

    try {
      const checkRole = await this.roleRepository.scan('name').eq(role.name).exec();
      if (checkRole.count < 1) {
        const result = await this.roleRepository.create(role);
        process.stdout.write('Successful Creating Role\n');
        return result.id;
      }
      process.stdout.write('Role Has Been Created Already\n');
      return checkRole[0].id;
    } catch (error) {
      process.stdout.write('Error While Creating Role\n');
      throw error;
    }
  }

  async UserCreate(email, roleId) {
    const user = {
      createdAt: this.timestamp,
      department: 'SuperAdmin',
      division: 'SuperAdmin',
      email,
      emailNotif: true,
      group: 'SuperAdmin',
      id: this.uuid.create(),
      isActive: true,
      isActiveAt: this.timestamp,
      loginTime: this.timestamp,
      mobileNumber: '9999999999',
      name: 'SuperAdmin',
      roleId,
      smsNotif: true,
      updatedAt: this.timestamp,
      billType: 'Both',
      assignedChannels: '[]',
      cardAssignedChannels: '[]',
      ewalletAssignedChannels: '[]',
      postPaymentConfigChannels: '[]',
    };
    try {
      const checkUser = await this.userRepository.scan('email').eq(email).exec();
      if (checkUser.count < 1) {
        await this.userRepository.create(user);
        process.stdout.write('Successful Creating User\n');
      } else {
        process.stdout.write('User Has Been Created Already\n');
      }
    } catch (error) {
      process.stdout.write('Error While Creating User\n');
      throw error;
    }
  }

  async ConfigCreate() {
    const config = [
      {
        name: 'refreshTime',
        type: 'default',
        updatedAt: this.timestamp,
        value: '5',
      },
      {
        name: 'paymentServiceApiMaintenance',
        type: 'default',
        updatedAt: this.timestamp,
        value: 'false',
      },
      {
        name: 'amaxLoadConsumer',
        type: 'default',
        updatedAt: this.timestamp,
        value: '',
      },
      {
        name: 'amaxLoadRetailer',
        type: 'default',
        updatedAt: this.timestamp,
        value: '',
      },
      {
        name: 'bayanEmailNotification',
        type: 'default',
        updatedAt: this.timestamp,
        value: 'false',
      },
      {
        name: 'bayanEmailNotificationPatternId',
        type: 'default',
        updatedAt: this.timestamp,
        value: '',
      },
      {
        name: 'globeEmailNotification',
        type: 'default',
        updatedAt: this.timestamp,
        value: 'false',
      },
      {
        name: 'globeEmailNotificationPatternId',
        type: 'default',
        updatedAt: this.timestamp,
        value: '',
      },
      {
        name: 'innoveEmailNotification',
        type: 'default',
        updatedAt: this.timestamp,
        value: 'false',
      },
      {
        name: 'innoveEmailNotificationPatternId',
        type: 'default',
        updatedAt: this.timestamp,
        value: '',
      },
      {
        name: 'paymentMethodCard',
        type: 'default',
        updatedAt: this.timestamp,
        value: 'adyen',
      },
      {
        name: 'paymentMethodGcash',
        type: 'default',
        updatedAt: this.timestamp,
        value: 'mynt',
      },
      {
        name: 'paymentMethodBank',
        type: 'default',
        updatedAt: this.timestamp,
        value: 'bpi',
      },
      {
        name: 'gcashRefundRetries',
        type: 'default',
        updatedAt: this.timestamp,
        value: '0',
      },
      {
        name: 'refundReason',
        type: 'default',
        updatedAt: this.timestamp,
        value:
          '[{"reason":"Overpayment"},{"reason":"Double debit"},{"reason":"Incorrect Account posting"},{"reason":"Unposted payment"},{"reason":"Partially Posted payment"},{"reason":"Erroneous Amount"}]',
      },
      {
        name: 'cardRefundReason',
        type: 'default',
        updatedAt: this.timestamp,
        value: '[{"reason":"Double Posting"},{"reason":"Erreoneous Amount"},{"reason":"Unposted Payment"}]',
      },
      {
        name: 'PSORPaymentType',
        type: 'default',
        updatedAt: this.timestamp,
        value: '[]',
      },
      {
        name: 'orGenerationConfig',
        type: 'default',
        updatedAt: this.timestamp,
        value: 'luke',
      },
      {
        name: 'seederExist',
        type: 'default',
        updatedAt: this.timestamp,
        value: 'true',
      },
      ...configurations,
    ];
    try {
      const max = 25;
      const groupedData = [];
      for (let i = 0; i < config.length; i += max) {
        const x = config.slice(i, i + max);
        const fill = x.map((item) => ({
          ...item,
          type: item.type || 'default',
          updatedAt: new Date().toISOString(),
          createdAt: new Date().toISOString(),
        }));
        const data = this.configRepository.batchPut(fill);
        groupedData.push(data);
      }

      await Promise.allSettled(groupedData);
      await this.createSwipeORType({
        name: 'swipeORPaymentTypeSample',
        type: 'default',
        updatedAt: this.timestamp,
        value: [],
      });
      process.stdout.write('Successful Creating Config\n');
    } catch (error) {
      process.stdout.write('Failed to Create Config\n');
      throw error;
    }
  }

  async S3BucketCreate() {
    // Create the parameters for calling createBucket
    const bucketParams = {
      Bucket: process.env.S3_BUCKET_NAME,
      CreateBucketConfiguration: {
        LocationConstraint: process.env.AWS_REGION,
      },
    };

    // call S3 to create the bucket
    const createBucketCommand = new awsS3.CreateBucketCommand(bucketParams);
    try {
      const response = await this.s3.send(createBucketCommand);
      process.stdout.write('Bucket created successfully\n', response);
    } catch (error) {
      if (error.name === 'BucketAlreadyOwnedByYou') {
        process.stdout.write('The bucket already exists and is owned by you.\n');
      } else {
        process.stdout.write('Error creating the bucket:', error.message);
      }
    }
  }
}

const readLineInterface = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

readLineInterface.question('Do you want to run the seeder? y/n: ', (answer) => {
  if (answer === 'y' || answer === 'Y') {
    readLineInterface.question('Enter Email for Super Admin: ', async (email) => {
      const seeder = new Seeder();
      const roleId = uuidGenerator.create();
      process.stdout.write('Creating Role\n');
      const role = await seeder.RoleCreate(roleId);
      process.stdout.write('Creating User\n');
      await seeder.UserCreate(email, role);
      process.stdout.write('Creating Configs\n');
      await seeder.ConfigCreate();
      // process.stdout.write('Creating S3 Bucket\n');
      // await seeder.S3BucketCreate();
      readLineInterface.close();
    });
  } else {
    process.stdout.write('Skipping Creation of Super Admin\n');
    readLineInterface.close();
  }
});
