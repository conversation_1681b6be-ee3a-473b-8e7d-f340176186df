const { <PERSON>ron<PERSON>ob } = require('cron');

class CronJobs {
  constructor(container) {
    const { handlers, logger } = container;
    this.logger = logger;
    this.handlers = handlers;
  }

  start() {
    this.logger.info('starting cron...');
    this.billingReportGeneration().start();
    this.wirelessPayTypeGeneration().start();
    this.wirelessPayModeGeneration().start();
  }

  billingReportGeneration() {
    return new CronJob('0 4 * * *', async () => {
      this.logger.info('Generate Billing Report');
      await this.handlers.Report.generateBillingReportFile();
      this.logger.info('End Billing Report');
    });
  }

  creditCardReportGeneration() {
    return new CronJob('0 9 3 * *', async () => {
      this.logger.info('Generate Gateway Credit Card Report');
      await this.handlers.Report.generateGCreditCardReportFile();
      this.logger.info('Done Generating Gateway Credit Card Report');
    });
  }

  collectionReportGeneration() {
    return new <PERSON>ron<PERSON>ob('30 9 3 * *', async () => {
      this.logger.info('Generate Gateway Collection Report');
      await this.handlers.Report.generateGCollectionReportFile();
      this.logger.info('Done Generating Gateway Collection Report');
    });
  }

  wirelessPayTypeGeneration() {
    return new CronJob('0 3 1 * *', async () => {
      this.logger.info('Generate Revenue Wireless PayType Report');
      await this.handlers.Report.generateMonthlyWirelessPayTypeReport();
      this.logger.info('Done Generating Revenue Wireless PayType Report');
    });
  }

  wirelessPayModeGeneration() {
    return new CronJob('30 3 1 * *', async () => {
      this.logger.info('Generate Revenue Wireless PayMode Report');
      await this.handlers.Report.generateMonthlyWirelessPayModeReport();
      this.logger.info('Done Generating Revenue Wireless PayMode Report');
    });
  }

  globeOneReportGeneration() {
    return new CronJob('0 8 * * *', async () => {
      this.logger.info('Generate GlobeOne Report');
      await this.handlers.Report.generateGlobeOneReportFile();
      this.logger.info('End GlobeOne Report');
    });
  }

  ecpayReportGeneration() {
    return new CronJob('0 8 * * *', async () => {
      this.logger.info('Generate ECPay Report');
      await this.handlers.Report.generateECPayReportFile();
      this.logger.info('End ECPay Report');
    });
  }
}

module.exports = CronJobs;
