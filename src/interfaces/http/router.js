const { Router } = require('express');
const { graphqlUploadExpress } = require('graphql-upload-minimal');
const express = require('express');
const cors = require('cors');
const compression = require('compression');
const methodOverride = require('method-override');
const path = require('path');
const { createYoga } = require('graphql-yoga');
const { useDisableIntrospection } = require('@graphql-yoga/plugin-disable-introspection');
const graphqlHandler = require('../graphql');
const Schema = require('../graphql/schema');

module.exports = ({ config, containerMiddleware, loggerMiddleware, errorHandler }) => {
  const router = Router();
  let graphiql = false;
  let isDisableIntrospection = true;
  let whitelist = [config.appUrl, config.webPortalUrl];

  if (config.env === 'development' || config.env === 'local') {
    graphiql = true;
    isDisableIntrospection = false;
    whitelist.push('chrome-extension://flnheeellpciglgpaodhkhmapeljopja');
  }

  /* istanbul ignore if */
  if (config.env !== 'test') {
    router.use(loggerMiddleware);
  }

  const corsOptions = {
    origin(origin, callback) {
      if (whitelist.indexOf(origin) !== -1 || !origin) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
  };

  router
    .use(methodOverride('X-HTTP-Method-Override'))
    .use(cors(corsOptions))
    .use(express.json())
    .use(
      express.urlencoded({
        extended: true,
      })
    )
    .use(compression())
    .use(containerMiddleware)
    .get('/healthz', (_, res) => res.status(200).json({ status: 'ok' }))
    .use('/', express.static(path.join(__dirname, './public')))
    .post('/graphql', graphqlUploadExpress({ maxFileSize: 5000000, maxFiles: 1 }), graphqlHandler)
    .get(
      '/graphql',
      graphqlUploadExpress({ maxFileSize: 5000000, maxFiles: 1 }),
      createYoga({
        schema: Schema,
        graphiql,
        plugins: [
          useDisableIntrospection({
            isDisabled: isDisableIntrospection,
          }),
        ],
      })
    )
    .use(errorHandler);

  return router;
};
