const express = require('express');

class Server {
  constructor({ config, router, logger }) {
    this.router = router;
    this.express = express();
    this.config = config;
    this.logger = logger;
    this.express = express();

    this.express.disable('x-powered-by');
    this.express.use(router);
  }

  startSLS() {
    return this.express;
  }

  startExpress() {
    return new Promise((resolve) => {
      const http = this.express.listen(this.config.web.port, () => {
        const { port } = http.address();
        this.logger.info(`[p ${process.pid}] Listening at port ${port}`);
        resolve();
      });
    });
  }
}

module.exports = Server;
