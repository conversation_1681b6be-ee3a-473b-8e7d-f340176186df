const Status = require('http-status');

/* istanbul ignore next */
module.exports = (err, req, res, next) => {
  if (err.message === 'ValidationError') {
    res.status(Status.BAD_REQUEST).json({
      type: 'ValidationError',
      details: err.details,
    });
  } else if (err.message === 'NotFoundError') {
    res.status(Status.NOT_FOUND).json({
      type: 'NotFoundError',
      details: err.details,
    });
  } else {
    res.status(Status.INTERNAL_SERVER_ERROR).json({
      type: 'InternalServerError',
      message: 'The server failed to handle this request',
    });
  }
};
