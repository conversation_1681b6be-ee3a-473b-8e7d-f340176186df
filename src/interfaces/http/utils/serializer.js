class Serializer {
  constructor(properties) {
    this.properties = properties;
  }

  serialize(input) {
    const data = {
      deserialized: input,
      serialized: {},
    };

    this.properties.reduce(this.reducer, data);

    return data.serialized;
  }

  static reducer(data, property) {
    const item = data;
    if (Object.prototype.isPrototypeOf.call(data.deserialized, property)) {
      item.serialized[property] = data.deserialized[property];
    } else {
      item.serialized[property] = null;
    }
    return item;
  }
}

module.exports = Serializer;
