module.exports = `
type Channel {
  id: String
  name: String 
  channelId: String
  channelCode: String
  callbackUrl: String
  isVerified: Boolean
  email: String
  ipAddress: String
  clientId: String
  clientSecret: String
  emailNotif: Boolean
  smsNotif: Boolean
  enablePaymentSession: Boolean
  createdAt: DateTime
  updatedAt: DateTime
  merchantCode: String
  merchantKey: String
  xApiKey: String
  paymentGateway: String
  globeEmailNotification: Boolean
  globeEmailNotificationPatternId: String
  innoveEmailNotification: Boolean
  innoveEmailNotificationPatternId: String
  bayanEmailNotification: Boolean
  bayanEmailNotificationPatternId: String
  failedEmailNotification: Boolean
  billType: String
  enableCardHolderName: Boolean
  isCallbackEncrypted: Boolean
  callbackPassPhrase: String
  cardPaymentMethod: String
  cardHolderNameType: String
  gcashPaymentMethod: String
  bankPaymentMethod: String
  subMerchants: [ChannelSubMerchants]
  isOrEnabled: Boolean
  gcreditSubMerchantId: String
  ewalletPaymentMethod: String
  isForPayByLink: Boolean
  gcashOneClickEnabled: Boolean
  gcashOneClickMerchantId: String
  gcashOneClickClientId: String
  gcashOneClickClientSecret: String
  gcashOneClickProductCode: String
  gcashOneClickRedirectUrl: String
  gcashOneClickValidity: Int
  gcashOneClickBindingIdPrefix: String
  bindCallbackUrl: String
  isSecureConnection: Boolean
}

type ChannelSubMerchants{
  merchant: Merchants
  serviceType: String
}

type ChannelsLoose {
  id: String
  name: String 
  channelId: String 
}

type ChannelsLoose {
  id: String
  name: String
  channelId: String
}

type PayByLinkChannel {
  id: String
  name: String
  channelId: String
  clientId: String
  clientSecret: String
}

type SearchFilterChannel {
  count: Int
  filteredData: [Channel]
}

type ChannelVerified {
  verified: Boolean
}

type SubMerchantExist {
  exist: Boolean
}
 
type Query {
  payByLinkChannels: [PayByLinkChannel]
  channels(filter: SearchChannelInput): SearchFilterChannel!
  channel(where: ChannelPrimary!): Channel
  channelsLoose: [ChannelsLoose]
  subMerchants(where: MerchantPrimary): [SubMerchant]
  chanelSubMerchants(id: String!): SubMerchantExist
  channelsBillType(billType: ChannelBillType!): [Channel]
  channelDropInSimulator(where: ChannelPrimary!): Channel
}

type Mutation {
  createChannel(data: CreateChannelInput!): Channel!
  updateChannel(data: UpdateChannelInput!, where: ChannelPrimary!): Channel
  deleteChannel(data: DeleteChannelInput!, where: ChannelPrimary!): Channel
  verifyChannel(data: ChannelVerificationInput ): ChannelVerified
  deleteChannels(data: DeleteChannelInput, where: Ids!): BatchDeleteResponse
}

# Input for create mutation
input CreateChannelInput{
  name: String!
  channelId: String!
  channelCode: String!
  email: String!
  ipAddress: String!
  merchantCode: String
  merchantKey: String
  xApiKey: String
  billType: billTypeTransaction!
  isForPayByLink: Boolean
}

# Input for update mutation
input UpdateChannelInput {
  name: String
  channelId: String
  channelCode: String
  ipAddress: String
  callbackUrl: String
  emailNotif: Boolean
  smsNotif: Boolean
  enablePaymentSession: Boolean
  merchantCode: String
  merchantKey: String
  xApiKey: String
  globeEmailNotification: Boolean
  globeEmailNotificationPatternId: String
  innoveEmailNotification: Boolean
  innoveEmailNotificationPatternId: String
  bayanEmailNotification: Boolean
  bayanEmailNotificationPatternId: String
  failedEmailNotification: Boolean
  billType: billTypeTransaction
  enableCardHolderName: Boolean
  isCallbackEncrypted: Boolean
  subMerchants: [ChannelSubMerchantInput]
  cardPaymentMethod: cardPaymentMethods
  cardHolderNameType: cardHolderNameTypes
  gcashPaymentMethod: gcashPaymentMethods
  bankPaymentMethod: bankPaymentMethods
  ewalletPaymentMethod: ewalletPaymentMethods
  isOrEnabled: Boolean
  gcreditSubMerchantId: String
  isForPayByLink: Boolean
  gcashOneClickEnabled: Boolean
  gcashOneClickMerchantId: String
  gcashOneClickClientId: String
  gcashOneClickClientSecret: String
  gcashOneClickProductCode: String
  gcashOneClickRedirectUrl: String
  gcashOneClickValidity: Int
  gcashOneClickBindingIdPrefix: String
  bindCallbackUrl: String
  isSecureConnection: Boolean
}

# Delete Channel Input
input ChannelSubMerchantInput {
  merchant: Merchants
  serviceType: String
}

# Delete Channel Input
input DeleteChannelInput {
  reasonToDelete: String!
}

input ChannelPrimary {
  id: String!
}

# Search Input
input SearchChannelInput {
  name: String
  channelId: String
}

# ChannelVerificationInput
input ChannelVerificationInput {
  email: String
  confirmationCode: String
}

enum cardHolderNameTypes {
  OPTIONAL,
  REQUIRED,
}

enum cardPaymentMethods {
  off
  adyen
  ipay88
  xendit
}

enum gcashPaymentMethods {
  mynt
}

enum bankPaymentMethods {
  off
  bpi
}

enum ewalletPaymentMethods {
  xendit
}

# Input MerchantPrimary
input MerchantPrimary {
  merchant: Merchants 
}

enum ChannelBillType {
  Bill
  NonBill
  Both
}

`;
