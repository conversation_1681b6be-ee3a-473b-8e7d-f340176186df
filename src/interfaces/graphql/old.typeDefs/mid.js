module.exports = `
type Mid {
  id: String
  merchantId: String
  name: String
  previousMerchantId: String
  depositoryBankName: String
  depositoryBankAccount: String
  paymentType: String
  company: String
  bankDiscount: String
  billType: String
  withholdingTax: String
  createdAt: DateTime
  updatedAt: DateTime
  costCenter: String
  channelId: String
  installments: [MIdInstallment]
  ada: [MIdADA]
  bankTerm: String
  bank: String
  businessUnit: String
}

type MIdADA {
  enrollmentType: String
  adaMid: String
}

type MIdInstallment {
  bankTerm: String
  bankMid: String
  bank: String
}

type SearchFilterMids {
  cursors: [String]
  count: Int
  filteredData: [Mid]
}

type Query {
  mid(where: String!): Mid
  mids(filter: SearchMidsFilterInput, pagination: PaginationInput!): SearchFilterMids!
}

type Mutation {
  createMid(data: CreateMidInput!): [Mid]
  updateMid(data: UpdateMidInput!, where: String!): [Mid] 
  deleteMid(data: DeleteMidInput!, where: String!): Mid 
}

enum midCompany {
  Globe
  Innove
  Bayan
}

enum midPaymentType {
  AutoDebit
  Straight
  Installment
}

enum billTypeTransaction {
  Bill
  NonBill
}

# Input for create mutation
input CreateMidInput{
  billType: billTypeTransaction!
  paymentType: midPaymentType!
  name: String!
  depositoryBankName: String!
  depositoryBankAccount: String!
  bankDiscount: String!
  withholdingTax: String!
  costCenter: String!
  merchantId: String
  previousMerchantId: String
  company: midCompany
  channelId: String
  installments: [InstallmentInputDetail]
  ada: [ADAInputDetail]
  businessUnit: String!
}

# Input for update mutation
input UpdateMidInput{
  billType: billTypeTransaction!
  paymentType: midPaymentType!
  name: String!
  merchantId: String
  depositoryBankName: String!
  depositoryBankAccount: String!
  company: midCompany
  bankDiscount: String!
  withholdingTax: String!
  previousMerchantId: String
  costCenter: String!
  channelId: String
  installments: [InstallmentInputDetail]
  ada: [ADAInputDetail]
  businessUnit: String
}

# Input for delete mutation
input DeleteMidInput {
  reasonToDelete: String!
}

input SearchMidsFilterInput {
  name: String
  merchantId: String
  previousMerchantId: String
  depositoryBankName: String
  depositoryBankAccount: String
  billType: billTypeTransaction
  paymentType: midPaymentType
  company: String
  bankDiscount: String
  withholdingTax: String
  costCenter: String,
}

input InstallmentInputDetail {
  bankTerm: String!
  bankMid: String!
  bank: String!
}

input ADAInputDetail {
  adaMid: String!
  enrollmentType: ADAEnrollmentType!
}

enum ADAEnrollmentType {
  Straight
  PreAuth
}
`;
