module.exports = `
enum AccessType {
  view
  create
  update
  delete
  import
  export
  deactivate
}

enum DashboardTiles {
  view
  transactions
  onlineCCGCash
  revenuePerChannel
  notifications
  transactionsPerChannel
  transactionsPercentage
  gatewayStatus
  performance
  adyen
  channelTransaction
  userMgmt
}

type Permissions {
  Dashboard: [DashboardTiles],
  User: [String],
  Role: [String],
  Channel: [String],
  Mid: [String],
  Provider: [String],
  Bank: [String],
  Transaction: [String],
  Failed: [String],
  Billing: [String],
  Gateway: [String],
  Collection: [String],
  Wireline: [String],
  MonthlyGenerated: [String],
  Treasury: [String],
  Audit: [String],
  Archive: [String],
  Config: [String],
  LukeBatchFile: [String],
  ChannelReport: [String],
  GotsReport: [String]
  ECPay: [String],
  GlobeOne: [String],
  PayByLink: [String],
  LoadORReport: [String],
  ContentGcashReport: [String],
  ContentFraudReport: [String],
  GcashRefundRequest: [String],
  GcashRefundApproval: [String],
  GcashRefundDetailedReport: [String],
  GcashRefundSummaryReport: [String],
  InstallmentReport: [String],
  InstallmentMid: [String],
  ADADeclinedReport: [String],
  ADASummaryReport: [String],
  CardRefundRequest: [String],
  CardRefundApproval: [String],
  EndGameReport: [String],
  CardRefundDetailedReport: [String],
  CardRefundSummaryReport: [String],
  DropinSimulator: [String],
  BillLinerConfig: [String],
  XenditRefundRequest: [String],
  XenditRefundApproval: [String],
  XenditRefundDetailedReport: [String],
  XenditRefundSummaryReport: [String],
  PostPaymentConfig: [String],
  PayByLinkModule: [String],
  PayByLinkReport: [String],
  GCashBindingReport: [String],
  ConvenienceFee: [String],
  ConvenienceFeeBrand: [String]
}

type Role {
  id: String
  isActive: Boolean 
  name: String
  code: String
  numberOfUsers: Int
  notes: String 
  emailNotif: Boolean
  smsNotif: Boolean
  permissions: Permissions
  createdAt: DateTime
  updatedAt: DateTime
}

type RolesLoose {
  id: String
  name: String
}

type SearchFilterRoles {
  cursors: [String]
  count: Int
  filteredData: [Role]
}

type Query {
  role(where: RolePrimary!): Role
  roles(filter: SearchRolesFilterInput, pagination: PaginationInput!): SearchFilterRoles!
  rolesLoose: [RolesLoose]
}

type Mutation {
  createRole(data: CreateRoleInput!): Role!
  updateRole(data: UpdateRoleInput!, where: RolePrimary!): Role 
  deleteRole(data: DeleteRoleInput!, where: RolePrimary!): Role 
  deleteRoles(data: DeleteRoleInput!, where: Ids!): BatchDeleteResponse 
  uploadRoles(file: Upload!): File!
}

input InputPermission {
  Dashboard: [DashboardTiles],
  User: [AccessType],
  Role: [AccessType],
  Channel: [AccessType],
  Mid: [AccessType],
  Provider: [AccessType],
  Bank: [AccessType],
  Transaction: [AccessType],
  Failed: [AccessType],
  Billing: [AccessType],
  Gateway: [AccessType],
  Collection: [AccessType],
  Wireline: [AccessType],
  MonthlyGenerated: [AccessType],
  Treasury: [AccessType],
  Audit: [AccessType],
  Archive: [AccessType],
  Config: [AccessType],
  LukeBatchFile: [AccessType],
  ChannelReport: [AccessType],
  GotsReport: [AccessType]
  ECPay: [AccessType],
  GlobeOne: [AccessType],
  PayByLink: [AccessType],
  LoadORReport: [AccessType],
  ContentGcashReport: [AccessType],
  ContentFraudReport: [AccessType],
  GcashRefundRequest: [AccessType],
  GcashRefundApproval: [AccessType],
  GcashRefundDetailedReport: [AccessType],
  GcashRefundSummaryReport: [AccessType],
  InstallmentReport: [AccessType],
  InstallmentMid: [AccessType],
  ADADeclinedReport: [AccessType],
  ADASummaryReport: [AccessType],
  CardRefundRequest: [AccessType],
  CardRefundApproval: [AccessType],
  EndGameReport: [AccessType],
  CardRefundDetailedReport: [AccessType],
  CardRefundSummaryReport: [AccessType],
  DropinSimulator: [AccessType],
  BillLinerConfig: [AccessType],
  XenditRefundRequest: [AccessType],
  XenditRefundApproval: [AccessType],
  XenditRefundDetailedReport: [AccessType],
  XenditRefundSummaryReport: [AccessType],
  PostPaymentConfig: [AccessType],
  PayByLinkModule: [AccessType],
  PayByLinkReport: [AccessType],
  GCashBindingReport: [AccessType],
  ConvenienceFee: [AccessType],
  ConvenienceFeeBrand: [AccessType]
}

# Input for create mutation
input CreateRoleInput{
  name: String!
  code: String!
  notes: String
  permissions: InputPermission
}

# Input for update mutation
input UpdateRoleInput{
  name: String 
  code: String
  notes: String
  permissions: InputPermission
  isActive: Boolean
  emailNotif: Boolean
  smsNotif: Boolean
}

# Delete Role Input
input DeleteRoleInput {
  reasonToDelete: String!
}

input RolePrimary {
  id: String!
}

input Operator {
  number: Int
  operator: String
}

input SearchRolesFilterInput {
  code: String
  name: String
  numberOfUsers: [Operator]
  notes: String
  isActive: Boolean
  createdAt: [DateRange]
}
`;
