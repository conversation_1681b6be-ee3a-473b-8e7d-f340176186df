module.exports = `

type Query {
  overallTransactions(where: monthInput): OverAllPayload
  transactions(where: monthInput, isPolling: Boolean):  [Transaction]
  performance(isPolling: Boolean): Performance
  pgUptimeMonitor(isPolling: Boolean): PaymentGatewayUptime
  channelAndGatewayStatus(filter: SearchPaymentGatewayInput,  pagination: PaginationGatewayInput!, isPolling: Boolean): SearchPaymentGateway
  overallLastUpdatedAt: LastUpdatedAt                                                                          
}

type PaymentGateways {
  channel: Channel
  paymentMethod: String
  status: Boolean
  updatedAt: DateTime
}

type SearchPaymentGateway {
  cursors: [PaginationGatewayCursor]
  count: Int
  filteredData: [PaymentGateways]
}

input PaginationGatewayInput {
  start: PaginationGatewayStart
  limit: Int
}

input PaginationGatewayStart {
  channelId: String
  paymentMethod: String
}

type PaginationGatewayCursor {
  channelId: String
  paymentMethod: String
}

input monthInput {
  startMonth: String
  endMonth: String
}

type PaymentGatewayUptime {
  online: Boolean
}

# Input for search query
input SearchPaymentGatewayInput  {
  channelId: [String]
  paymentMethod: String
  status: Boolean
  updatedAt: [DateRange]
}

type Transaction{ 
  month: String
  year: String
  type: String
  payload: [TransactionPerChannels]
}

type TransactionPerChannels {
  channelId: String
  channelName: String
  transactions: Float
  revenue: Float
}

type Performance { 
  cpu: String
  memory: String
}

type LastUpdatedAt { 
  lastUpdatedAt: String
}

type OverAllPayload {
  overall: Int
  success: Int
  failed: Int
  refused: Int
  adyen: Int
  gcash: Int
}
`;
