module.exports = `

type BatchFile { 
  filename: String
  createdAt: DateTime
}

type SearchBatchFile {
  lastKey: String
  count: Int
  filteredData: [BatchFile]
}

type Query {
  batchfiles(filter: SearchBatchFileFilterInput!, pagination: PaginationBatchFileInput!):  SearchBatchFile  
}

input PaginationBatchFileInput {
  startKeys: String
  limit: Int!
}

# Search Input
input SearchBatchFileFilterInput {
  filename: String
  createdAt: DateRange
}
`;
