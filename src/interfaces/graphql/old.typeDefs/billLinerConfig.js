module.exports = `

type Query {
  listBillLinerConfig: [<PERSON><PERSON><PERSON>]
}  

type Mutation {
  deleteBillLinerConfig(data: DeleteBillLinerInput, where: String!): <PERSON><PERSON><PERSON>
  createBillLinerConfig(data: CreateBillLinerConfig): Bill<PERSON><PERSON>
}

type Bill<PERSON>iner {
  content: String
  adyenMerchantAccount: String
}

input DeleteBillLinerInput{
  reasonToDelete: String!
}

input CreateBillLinerConfig{
  content: String
  adyenMerchantAccount: String
}
`;
