module.exports = `

type Bank { 
  name: String
  code: String 
  gateway: String
  createdAt: DateTime
  updatedAt: DateTime
}

type SearchBank {
  cursors: [String]
  count: Int
  filteredData: [Bank]
}

type Query {
  bank(where: BankPrimary!): Bank
  banks(filter: SearchBankFilterInput!, pagination: PaginationInput!):  SearchBank  
}

type Mutation {
  createBank(data: CreateBankInput!): Bank
  updateBank(data: UpdateBankInput!, where: BankPrimary!): Bank 
  deleteBank(data: DeleteBankInput!, where: BankPrimary!): Bank
  uploadBanks(file: Upload!): File!
}

input BankPrimary {
  name: String!
}

# Input for create mutation
input CreateBankInput{
  name: String
  code: String 
  gateway: String
}

# Input for update mutation
input UpdateBankInput{
  code: String 
  gateway: String
}

# Input for delete mutation
input DeleteBankInput {
  reasonToDelete: String!
}

# Search Input
input SearchBankFilterInput {
  name: String
  code: String 
  gateway: String
}
`;
