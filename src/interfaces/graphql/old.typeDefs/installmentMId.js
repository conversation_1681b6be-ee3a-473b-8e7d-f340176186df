module.exports = `
type Installment {
  bank: String,
  term: String,
  paymentId: String
  createdAt: String
  updatedAt: String
}

type SearchFilterInstallmentMIds {
  cursors: [String]
  count: Int
  filteredData: [Installment]
}

type Query {
  searchInstallmentMid(filter: InstallmentMerchantIdFilter, pagination: PaginationInput!): SearchFilterInstallmentMIds!
  listInstallmentBank: [String]  
}

type Mutation {
  createInstallmentMid(data: CreateInstallmentMIdInput!): Installment
  deleteInstallmentMid(data: DeleteInstallmentMIdInput!, where: InstallmentPrimary!): Installment
  downloadInstallmentMid(filter: InstallmentMerchantIdFilter, pagination: PaginationInput!): SearchFilterInstallmentMIds
}

# Input for create mutation
input CreateInstallmentMIdInput{
    bank: String!
    term: Float!
    paymentId: Float!
}

# Input for delete mutation
input DeleteInstallmentMIdInput {
  reasonToDelete: String!
}

input InstallmentMerchantIdFilter {
  bank: String
  term: Float
  paymentId: String
  createdAt: DateRange
}

input InstallmentPrimary {
  bank: String!
  term: String!
}
`;
