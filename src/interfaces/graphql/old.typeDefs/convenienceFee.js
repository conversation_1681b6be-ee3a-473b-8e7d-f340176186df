module.exports = `
type ConvenienceFee {
  id: String,
  channelId: String,
  name: String,
  brand: String,
  gatewayProcessor: String,
  paymentMethod:  String,
  transactionType: String,
  convenienceFeeType: String,
  convenienceFeeValue: String,
  convenienceFeeThreshold: String,
  convenienceFeeTieredScheme: String,
  createdAt: String,
  updatedAt: String,
}

input ConvenienceFeePrimary {
  id: String!
}

type SearchConvenienceFee {
  cursors: [String]
  count: Int
  filteredData: [ConvenienceFee]
}

type Query {
  convenienceFee(where: ConvenienceFeePrimary!): ConvenienceFee
  convenienceFees(filter: SearchConvenienceFeeInput, pagination: PaginationInput!):  SearchConvenienceFee
}

type Mutation {
  createConvenienceFee(data: CreateConvenienceFeeInput!): ConvenienceFee
  updateConvenienceFee(data: updateConvenienceFeeInput!, where: ConvenienceFeePrimary!): ConvenienceFee
  deleteConvenienceFee(data: DeleteConvenienceFeeInput!, where: ConvenienceFeePrimary!): ConvenienceFee

}

input CreateConvenienceFeeInput{
  channelId: String!,
  brand: String!,
  gatewayProcessor: String!,
  paymentMethod:  String!,
  transactionType: String!,
  convenienceFeeType: String!,
  convenienceFeeValue: String,
  convenienceFeeThreshold: String,
  convenienceFeeTieredScheme: String,
}

input updateConvenienceFeeInput{
  channelId: String!,
  brand: String!,
  gatewayProcessor: String!,
  paymentMethod:  String!,
  transactionType: String!,
  convenienceFeeType: String,
  convenienceFeeValue: String,
  convenienceFeeThreshold: String,
  convenienceFeeTieredScheme: String,
}

input DeleteConvenienceFeeInput {
  reasonToDelete: String!
}

input SearchConvenienceFeeInput{
  id: String,
  channelId: String,
  name: String,
  brand: String,
  gatewayProcessor: String,
  paymentMethod:  String,
  transactionType: String,
  convenienceFeeType: String,
}`;
