module.exports = `
type VoucherTransaction {
  psReferenceNo: String
  gcashReferenceNo: String
  accountNo: String
  srn: String
  channelName: String
  channelId: String 
  paymentMethod: String
  paymentStatus: String
  amount: Float
  currency: String
  date: DateTime
  msisdn: String
  productDescription: String
  emailAddress: String
  paymentGateway: String
  customerSegment: String
  customerSubType: String
  brand: String
  entity: String
  sku: String
  modeOfPayment: String
  subscriberType: String
  contentPartnerShortName: String
  voucherDispenseStatus: String
  refundStatus: String
  refundAmount: String
}

type SearchVoucherTransaction {
  lastKey: String
  filteredData: [VoucherTransaction]
}

type Query {
  contentGcashReport(filter: contentGcashReportFilterInput, pagination: PaginationReportInput!): SearchVoucherTransaction
  contentFraudReport(filter: contentFraudReportFilterInput, pagination: PaginationReportInput!): SearchFraudReport
}

input contentGcashReportFilterInput {
  psReferenceNo: String
  gcashReferenceNo: String
  accountNo: String
  # mobileNumber
  msisdn: String
  brand: String
  # Use only GCASH_AUTHORISED and GCASH_REFUSED
  paymentStatus: TransactionStatus
  date: DateRange
}

### Voucher Fraud Reports

type FraudReport {
  psReferenceNo: String
  gcashReferenceNo: String
  accountNo: String
  srn: String
  channelName: String
  channelId: String 
  paymentMethod: String
  paymentStatus: String
  amount: Float
  currency: String
  date: DateTime
  msisdn: String
  productDescription: String
  emailAddress: String
  paymentGateway: String
  customerSegment: String
  customerSubType: String
  brand: String
  entity: String
  sku: String
  modeOfPayment: String
  subscriberType: String
  contentPartnerShortName: String
  voucherDispenseStatus: String
  refundStatus: String
  refundAmount: String
}

type SearchFraudReport {
  lastKey: String
  filteredData: [FraudReport]
}

input contentFraudReportFilterInput {
  psReferenceNo: String
  gcashReferenceNo: String
  accountNo: String
  # mobileNumber
  msisdn: String
  brand: String
  # Use only GCASH_AUTHORISED and GCASH_REFUSED
  paymentStatus: TransactionStatus
  date: DateRange
}
`;
