module.exports = `

type Query {
    providers(filter: SearchProviderFilterInput!, pagination: PaginationInput!):  SearchProvider  
}
type Mutation {
    updateProvider(data: UpdateProviderInput!, where: ProviderPrimary!): Provider 
}
# Search Input
input SearchProviderFilterInput {
    id: String
    name: String 
    email: String
    ipAddress: String
    isEnabled: Boolean
    createdAt: DateTime
}

input ProviderPrimary {
    id: String!
}

input UpdateProviderInput {
    isEnabled: Boolean
    reasonToUpdate: String
    adyenEnabled: Boolean
    gcashEnabled: Boolean
    ipay88Enabled: Boolean
    bpiEnabled: Boolean
    xenditEnabled: Boolean
}

type SearchProvider {
    cursors: [String]
    count: Int
    filteredData: [Provider]
}

type Provider { 
    id: String
    name: String 
    email: String
    ipAddress: String
    isEnabled: Boolean
    createdAt: DateTime
    adyenEnabled: Boolean
    gcashEnabled: Boolean
    ipay88Enabled: Boolean
    bpiEnabled: Boolean
    xenditEnabled: Boolean
}

`;
