module.exports = `

type Query {
    paybillReferenceCodes(filter: SearchPaybillReferenceCodesFilterInput!): SearchFilterPaybillReferenceCodes!
    paybillTransactionLogs(filter: SearchTransactionLogsGatewayFilterInput!): SearchFilterTransactionLogsGateway!
    archiveAuditLogs(filter: SearchArchiveAuditLogFilterInput!, limit: Int): SearchFiltedAuditLogs
    archiveTransactionLogs(filter: SearchArchiveTransactionLogFilterInput!, limit: Int): SearchFiltedTransactionLogs
    archiveSwipeLogs(filter: SearchArchiveSwipeLogFilterInput!, limit: Int): SearchFiltedSwipeLogs
}

type Mutation {
    downloadArchives(data: DownloadArchiveInput!): DownloadAuthorized!
}
  
input DownloadArchiveInput {
    type: DownloadArchiveType
}

enum DownloadArchiveType {
    transaction
    paybill
}

type SearchFilterPaybillReferenceCodes {
    filteredData: [PaybillReferenceCodes]
}

type PaybillReferenceCodes {
    order_ref_code : String 
    payment_id : String 
    trans_id1 : String 
    trans_id2 : String 
    datetime_added : String 
}

input SearchPaybillReferenceCodesFilterInput {
    orderRefCode: String
}

type SearchFilterTransactionLogsGateway {
    filteredData: [PaybillTransactionLogs]
}

type SearchFiltedAuditLogs {
    filteredData: [Audits]
}

type SearchFiltedTransactionLogs {
    filteredData: [TransactionLogs]
}

type SearchFiltedSwipeLogs {
    filteredData: [VoucherTransaction]
}

input SearchArchiveAuditLogFilterInput {
    id: String
    ipAddress: String
    userEmail: String
    userAgent: String
    category: String
    createdAt: [DateRange]
}

input SearchArchiveTransactionLogFilterInput {
    id: String,
    gatewayProcessor: ReportPaymentGateway,
    accountNumber: String,
    status: TransactionStatus,
    mobileNumber: String,
    createdAt: [DateRange],
}

input SearchArchiveSwipeLogFilterInput {
    id: String,
}

input SearchTransactionLogsGatewayFilterInput {
    orderRefCode: String
}

type PaybillTransactionLogs {
    order_ref_code : String 
    seller_name : String 
    payment_method : String 
    description : String 
    status : String 
    subs_lname : String 
    subs_fname : String 
    subs_mname : String 
    subs_accnt_no : String 
    subs_globe_no : String 
    subs_email_address : String 
    datetime_added : String 
    datetime_on_hold : String 
    datetime_completed : String 
    sent_amount : String 
    approved_amount : String 
    remarks : String 
    payment_id : String 
    trans_id1 : String 
    trans_id2 : String 
    account_type : String 
    outstanding_balance : String 
}

`;
