module.exports = `

type User {
  id: String 
  name: String
  email: String
  role: Role
  department: String
  division: String
  group: String
  channel: String
  loginTime: DateTime
  isActive: Boolean
  reasonToDeactivate: String
  mobileNumber: String
  isActiveAt: DateTime
  emailNotif: Boolean
  smsNotif: Boolean
  createdAt: DateTime
  updatedAt: DateTime
  assignedChannels: [UserAssignedChannels]
  cardAssignedChannels: [UserAssignedChannels]
  ewalletAssignedChannels: [UserAssignedChannels]
  postPaymentConfigChannels: [UserAssignedChannels]
  billType: String
}

type downloadUser {
  id: String 
  name: String
  emailAddress: String
  roleName: String
  roleDescription: String
  group: String
  permission: String
  createdAt: DateTime
  loginTime: DateTime
  status: String
}

type SearchFilterUser {
  cursors: [String]
  count: Int
  filteredData: [User]
}

type UserSummaryTotal {
  activeUsers: [Summary]
  inactiveUsers: [Summary]
}

type Summary{
  month: String
  count: Int
}

type BatchDeleteResponse {
  unprocessedItems: Int
}

type BatchDeactivateResponse {
  unprocessedItems: Int
}

type File {
  filename: String!
  mimetype: String!
}

type UserAssignedChannels {
  channelId: String
  name: String
}

type validUser {
  email: String  
}

type invalidUser {
  email: String
  errorReason: String
}

type bulkUploadUserStatus {
  filename: String
  mimetype: String
  validUsers: [validUser]
  invalidUsers: [invalidUser]  
}

type Query {
  user(where: UserPrimary!): User
  users(filter: SearchUsersFilterInput!, pagination: PaginationInput!): SearchFilterUser!
  usersSummary(range: DateRange!, isPolling: Boolean): UserSummaryTotal
}

type Mutation {
  createUser(data: RegisterInput!): User!
  validateToken(data: ValidateTokenInput!): User!
  updateUser(data: UpdateUserInput!, where: UserPrimary!): User 
  deleteUser(data: DeleteUserInput!, where: UserPrimary!): User 
  deleteUsers(data: DeleteUserInput!, where: Ids!): BatchDeleteResponse
  downloadUsers: [downloadUser]
  uploadUsers(file: Upload!): File!
  deactivateUser(data: DeactivateUserInput!, where: Ids!): BatchDeactivateResponse
  uploadUsersStatusUpdate(file: Upload!): bulkUploadUserStatus!
}

enum UserBillType {
  Bill
  NonBill
  Both
}

# Input Validate Token
input ValidateTokenInput {
  token: String!
}

# Input Registration
input RegisterInput {
  name: String!
  email: String!
  roleId: String!
  group: String!
  mobileNumber: String!
  department: String!
  division: String!
}

# Input Update User
input UpdateUserInput{
  name: String
  email: String
  roleId: String
  group: String
  channel: String
  division: String
  loginTime: DateTime
  department: String
  isActive: Boolean
  reasonToDeactivate: String
  mobileNumber: String
  emailNotif: Boolean
  smsNotif: Boolean
  assignedChannels: [InputAssignedChannels]!
  cardAssignedChannels: [InputAssignedChannels]!
  ewalletAssignedChannels: [InputAssignedChannels]!
  postPaymentConfigChannels: [InputAssignedChannels]!
  billType: UserBillType!
}

input InputAssignedChannels {
  channelId: String
  name: String
}

# Delete User Input
input DeleteUserInput {
  reasonToDelete: String!
}

input DeactivateUserInput {
  reasonToDeactivate: String!
}

input PaginationInput {
  start: String!
  limit: Int!
}

input LastKey {
  S: String!
}

input UserPrimary {
  id: String!
}

input Ids {
  ids: [String]
}

input DateRange {
  start: String
  end: String
}

input SearchUsersFilterInput {
  email: String
  name: String
  isActive: Boolean
  roleId: [String]
  group: String
  createdAt: [DateRange]
}

input UserSummaryInput {
  date: [DateRange]
}

`;
