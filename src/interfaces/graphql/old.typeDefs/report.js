module.exports = `

scalar YearOrInt

input YearRange {
  start: YearOrInt
  end: YearOrInt
}

type Query {
  payByLinkReport(filter: PayByLinkTableReportInput, pagination: PaginationReportInput!):  SearchPayByLinkTableReport
  reports(filter: SearchTransactionLogsFilterInput!, pagination: PaginationReportInput!):  SearchTransactionLogs
  failedReports(filter: SearchFailedLogsFilterInput!, pagination: PaginationReportInput!):  SearchTransactionLogs
  billingReports(filter: SearchTransactionLogsFilterInput!, pagination: PaginationReportInput!):  SearchBillingReports
  revenueWireline(filter: SearchRevenueWirelineInput!, pagination: PaginationReportInput!):  SearchRevenueWireline
  gatewayTransactionReports(filter: SearchGatewayTransactionReportsFilterInput!, pagination: PaginationReportInput!):  SearchGatewayTransactionReports
  transactionCount(filter: TransactionCountInput!): TransactionCount
  gatewayCollectionSummary(where: monthInput!, type: CollectionType!): [GatewaySummary]
  monthlyReports(filter: MontlyReportInput!, pagination: PaginationMonthlyReportInput!):  MontlyGeneratedReport
  treasuryYTD(filter: SearchTreasuryInput, reportType: TreasuryReportType!): SearchTreasury
  monthlyTreasury(filter: SearchTreasuryMonthInput, reportType: TreasuryReportType!): MonthlySearchTreasury
  raWirelessMonthlyTypeCode(filter: SearchRevenueWirelessInput!, pagination: PaginationReportInput!): RAWirelessMonthlyTypeCode
  raWirelessMonthlyModeCode(filter: SearchRevenueWirelessInput!, pagination: PaginationReportInput!): RAWirelessMonthlyModeCode
  raWirelessGenerateFile(filter: SearchRevenueWirelessInput!, pagination: PaginationReportInput!): WirelessFile
  treasuryReports(filter: SearchTransactionLogsFilterInput!, pagination: PaginationReportInput!):  SearchTransactionLogs
  channelReports(filter: channelReportFilterInput!, isPolling: Boolean): SearchChannelReports
  gotsReports(filter: SearchTransactionLogsFilterInput!, pagination: PaginationReportInput!):  SearchTransactionLogs
  ecpayReports(filter: ecpayReportFilterInput!, pagination: PaginationReportInput!):  SearchECPayReports
  globeOneReport(filter: globeOneReportFilterInput!, pagination: PaginationReportInput!):  SearchGlobeOneReport
  payByLink(filter: payByLinkFilterInput!, pagination: PaginationReportInput!):  SearchPayByLinkReport
  loadORReport(filter: loadORReportFilterInput!, pagination: PaginationReportInput!):  SearchLoadORReport
  installmentReport(filter: installmentReportFilterInput!, pagination: PaginationReportInput!):  SearchInstallmentReport
  adaDeclinedDetailedReport(filter: adaDeclinedDetailedInput, pagination: PaginationReportInput!): SearchADADeclinedDetailed
  adaSummaryReport(filter: adaSummaryInput): SearchADASummary
  endGameTransactionReport(filter: endGameReportFilterInput!, pagination: PaginationReportInput!): SearchEndGameReport
  uploadTransactions(file: Upload!): SearchTransactionLogs
}

type Mutation {
  downloadReports(data: DownloadReportInput!): DownloadAuthorized!
  downloadS3Reports(data: DownloadS3ReportInput!): DownloadS3Report
  downloadS3LukeBatchFiles(data: downloadS3LukeBatchFilesInput!): DownloadS3Report
}

input channelReportFilterInput {
  channelIds: [String]
  fundingSource: String
  billType: billTypeTransaction
  month: DateRange!
  year: YearRange!
}

type SearchChannelReports {
  filteredData: [ChannelReports]
}

type ChannelReports {
  # ChannelId For Filtering Use
  channelId: String
  billType: billTypeTransaction
  channelName: String
  gateway: ReportPaymentGateway
  fundingSource: String
  transAmount: Float
  transCount: Int
  businessUnit: String
  paymentType: String
}

input DownloadS3ReportInput {
  id: String
  type: DownloadReportType
}

input downloadS3LukeBatchFilesInput {
  filename: String
}

input DownloadReportInput {
  type: DownloadReportType
}

input MontlyReportInput {
  #Required Key
  type: MontlyGeneratedReportType!
  #Optional
  year: String
  #Optional
  fileName: String
  #Optional
  createdAt: DateRange
  month: String
}

enum PaymentGateway {
  adyen
  ipay88
  gcash
  bpi
  xendit
}

enum MontlyGeneratedReportType {
  creditcard
  collectionmid
  collectioncompany
  revenuewirelesspaytype
  revenuewirelesspaymode
}

enum DownloadReportType {
  transaction
  failed
  billing
  creditcard
  collection
  rawireline
  rawireless
  gatewaycc
  collectionmid
  collectioncompany
  treasury
  revenuewirelesspaytype
  revenuewirelesspaymode
  lukebatch
  channelreport
  gots
  ecpay
  globeone
  paybylink
  loadorreport
  contentgcashreport
  contentfraudreport
  gcashrefunddetailedreport
  gcashrefundsummaryreport
  installmentreport  
  adadeclinedreport
  adasummaryreport
  endgamereport
  cardrefunddetailedreport
  cardrefundsummaryreport
  xenditrefunddetailedreport
  xenditrefundsummaryreport
  paybylinkreport
  gCashBindingReport
}

enum CollectionType {
  company
  mid
}

enum ProductDescription {
  #G - Globe-msisdn
  G 
  #I - Innove-wireline
  I
  #B - Bayan
  B
  #N - Innove-iccbs
  N
}

enum PaymentMethod {
  # GCASH
  GCT
  # Online Credit Card
  OCC
  # WeChat Pay
  wechatpaySdk
  # Dragon Pay
  dragonpay_otc_philippines
  # Alipay
  alipay
  # GrabPay
  grabpay
  # Online Banking
  bank
  # PayMaya
  paymaya
  # Shopee
  shopeepay

  # Direct Bank Payment Methods
  BPI
  Unionbank
  RCBC
}

enum TransactionStatus {
  #ALL PAYMENT AUTHORISED
  PAYMENT_AUTHORIZED
  #ALL PAYMENT AUTHORISED
  PAYMENT_REFUSED
  #Labeled as PAYMENT_POSTED
  POSTED
  #Labeled as PAYMENT_POST_FAILED
  POSTING_FAILED
  #Labeled as PAYMENT_POSTED_LUKE
  POSTED_LUKE
  #Labeled as GCASH_AUTHORISED
  GCASH_AUTHORISED
  #Labeled as GCASH_REFUSED
  GCASH_REFUSED
}

enum RevenueTransactionStatus{
  #Labeled as PAYMENT_POSTED
  POSTED
  #Labeled as POSTED_LUKE
  POSTED_LUKE
  #Labeled as PAYMENT_POST_FAILED
  POSTING_FAILED
}

enum GatewayTransactionStatus{
  #Labeled as PAYMENT_POSTED
  POSTED
  #Labeled as POSTED_LUKE
  POSTED_LUKE
  #Labeled as PAYMENT_POST_FAILED
  POSTING_FAILED
}

enum ORTransactionStatus{ 
  #Labeled as GCASH_AUTHORISED
  GCASH_AUTHORISED
  #Labeled as GCASH_REFUSED
  GCASH_REFUSED
  #Labeled as ADYEN_AUTHORISED
  ADYEN_AUTHORISED
  #Labeled as ADYEN_REFUSED
  ADYEN_REFUSED
}

enum ORPaymentType {
  adyen
  gcash
}

enum ORLoadType{
  RETAILER
  CONSUMER
}

enum PayModeCode {
  GCT
  OCC
}

enum AccountType {
  N
  G
  I
  B
}

enum ReportPaymentGateway {
  adyen
  ipay88
  gcash
  bpi
  xendit
  generic
}

enum ECPayStatus {
  #Labeled as GCASH_AUTHORISED
  GCASH_AUTHORISED
  #Labeled as GCASH_REFUSED
  GCASH_REFUSED
}

enum RecurringPaymentStatus {
  Declined
  Approved
}

enum Entity {
  #Globe-msisdn
  G
  #Bayan
  B
  #Innove-wireline
  I
  #Innove-iccbs
  N
}

# Search Input
input SearchTransactionLogsFilterInput {
    # Keys for Fast Searching
    reference: String
    # Keys for Fast Searching
    accountNumber: String
    # Keys for Fast Searching
    channelId: String
    # Keys for Fast Searching
    status: TransactionStatus
    prodDesc: ProductDescription
    paymentMethod: PaymentMethod
    amountCurrency: String
    amountValue: String
    createdAt: DateRange
    mobileNumber: String
    transId: String
    emailAddress: String
    paymentGateway: ReportPaymentGateway
    splitPayment: Boolean
    billType: billTypeTransaction
    paymentType: midPaymentType
    fundingSource: String
    statuses: [TransactionStatus]
    # Order Reference
    channelReference: String
    # Refund Identification
    isAutoRefund: Boolean
}

input SearchFailedLogsFilterInput {
  # Keys for Fast Searching
  reference: String
  # Keys for Fast Searching
  accountNumber: String
  # Keys for Fast Searching
  channelId: String
  paymentMethod: PaymentMethod
  amountCurrency: String
  amountValue: Float
  createdAt: DateRange
  mobileNumber: String
}

# Search Input
input SearchRevenueWirelineInput {
    reference: String
    accountNumber: String
    prodDesc: ProductDescription
    status: RevenueTransactionStatus
    paymentMethod: PaymentMethod
    createdAt: DateRange
}

input SearchRevenueWirelessInput {
  # Keys for Fast Searching
  reference: String
  # Keys for Fast Searching
  accountNumber: String
  #Payment Date
  createdAt: DateRange
  payModeCode: PayModeCode
  paymentTypeCode: String
  month: String
  year: String
  creditCardBank: String
}

input SearchWirelessMonthTypeCodeInput {
  createdAt: DateRange
  paymentTypeCode: String
}

input SearchWirelessMonthModeCodeInput {
  createdAt: DateRange
  payModeCode: PayModeCode
  creditCardBank: String

}

input PaginationPrimary {
  id: String
}

input PaginationMonthlyReportInput {
  startKeys: String
  limit: Int!
}

input PaginationReportInput {
  startKeys: String!
  limit: Int!
}

input PaginationStart {
  id: String!
  transactionId: String!
}

type SearchTransactionLogs {
  lastKey: String
  filteredData: [TransactionLogs]
}

type CursorData {
  id: String
  accountNumber: String
}

type TransactionLogs {
    mobileNumber: String
    reference: String
    accountNumber: String
    channelName: String
    paymentMethod: String
    status: String
    amountValue: Float
    amountCurrency: String
    createdAt: DateTime
    prodDesc: String
    emailAddress: String
    paymentGateway: String
    splitPayment: Boolean
    billType: String
    paymentType: String
    fundingSource: String
    fromBatchFile: Boolean
    costCenter: String
    creditCardHolderName: String
    creditCardNumber: String
    creditCardBank: String
    creditCardCountry: String
    creditCardType: String
    transId: String
    threeDFlag: String
    # Order Reference
    channelReference: String
    accountType: String
    refusalReasonRaw: String
    postPaymentReason: String
    postPaymentEsbMessageId: String
    refundAmount: String
    refundStatus: String
    refundId: String
    timestamp: DateTime
    budgetProtectValue: String
    isBinding: Boolean
    bindingRequestID: String
    finalAmount: Float
    convenienceFee: Float
}

type TransactionCount {
  count: Int
}

input TransactionCountInput {
  channelId: String!
  range: DateRange
}

type GatewaySummary {
  month: String
  year: String
  payload: [GatewayReport]
}

type GatewayReport {
  name: String
  transCount: Float
  transAmount: Float
  bankCharge: Float
  depositoryBankAccount: String
  merchantCompany: String
  costCenter: String
}
input SearchBillingReportsFilterInput {
  reference: String
  accountNumber: String
  channelId: String
  paymentMethod: PaymentMethod
  status: String
  amountValue: Float
  amountCurrency: String
  createdAt: DateRange
}

type SearchBillingReports {
  lastKey: String
  filteredData: [BillingReports]
}

type BillingReports {
  createdAt: DateTime
  accountNumber: String
  reference: String
  amountValue: Float
  amountCurrency: String
  status: String
  paymentMethod: String
  channelName: String
  creditCardNumber: String
  creditCardBank: String
  creditCardCountry: String
  creditCardType: String
  ipAddress: String
  mid: String
  merchantName: String
  transId: String
  authCode: String
  prodDesc: String
  threeDFlag: String
  mobileNumber: String
  postPaymentReferenceId: String
  refundAmount: String
  refundStatus: String
  refundId: String
}

input SearchGatewayTransactionReportsFilterInput {
  mobileNumber: String
  reference: String
  accountNumber: String
  channelId: String
  paymentMethod: PaymentMethod
  status: GatewayTransactionStatus
  mid: String
  amountValue: Float
  amountCurrency: String
  merchantName: String
  merchantCompany: String
  createdAt: DateRange
  paymentGateway: PaymentGateway
}

type SearchGatewayTransactionReports {
  lastKey: String
  filteredData: [GatewayTransactionReports]
}

type GatewayTransactionReports {
  creditCardHolderName: String
  creditCardNumber: String
  creditCardBank: String
  creditCardCountry: String
  creditCardType: String
  ipAddress: String
  createdAt: DateTime
  paymentMethod: String
  amountCurrency: String
  grossAmount: Float
  netAmount: Float
  bankDiscount: Float
  withholdingtax: Float
  status: String
  prodDesc: String
  depositoryBank: String
  depositoryBankAccountNo: String
  mid: String
  reference: String
  transId: String
  authCode: String
  accountNumber: String
  threeDFlag: String
  channelName: String
  mobileNumber: String
  merchantCompany: String
  merchantName: String
  paymentGateway: String
  costCenter: String
}

input SummaryInput {
  startMonth: String
  endMonth: String
  depositoryBankAccount: String
}


type GatewayTransactionSummary{
  id: String
  month: String
  year: String
  type: String
  payload: [GatewayTransaction]
  total: GatewayTransaction
}

type GatewayTransaction {
  channelId: String
  channelName: String
  mid: String
  bankCharges: Float
  company: String
  revenues: Float
  transactions: Int
}

type DownloadAuthorized {
  authorized: Boolean!
}

type MonthlyGenerated {
  id: String
  year: String
  fileName: String
  type: String
  createdAt: String
  month: String
}

type RevenueWireline {
  reference: String
  accountNumber: String
  prodDesc: String
  status: String
  amountValue: Float
  paymentMethod: String
  createdAt: DateTime
  postedTimestamp: DateTime
  refundAmount: String
  refundStatus: String
  refundId: String
}

type SearchRevenueWireline {
  lastKey: String
  filteredData: [RevenueWireline]
}

type MontlyGeneratedReport {
  lastKey: String
  count: Int
  filteredData: [MonthlyGenerated]
}

type DailyReport {
  # Payment Reference
  reference: String
  # Payment Date
  createdAt: DateTime
  # Account Number
  accountNumber: String
  # Payment Mode Code
  payModeCode: String
  # Payment Mode Description
  payModeDesc: String
  # Credit Card Company
  creditCardBank: String
  # Payment Type Code
  paymentTypeCode: String
  # Amount
  amountValue: String
}

type MonthlyTypeCode {
  #Payment Date
  createdAt: DateTime
  #Payment Type Code
  paymentTypeCode: String
  #Amount
  amountValue: String
}

type MonthlyModeCode {
  #Payment Date
  createdAt: DateTime
  #Payment Mode Code
  payModeCode: String
  #Payment Mode Desc
  payModeDesc: String
  #Credit Card Company
  creditCardBank: String
  #Amount
  amountValue: String
}

type SearchRevenueWireless {
  lastKey: String
  filteredData: [DailyReport]
}

enum TreasuryReportType {
  payloadCreditDebit
  payloadCardBrand
  payloadGcash
  payloadXendit
  payloadXenditEwallet
  payloadXenditDirectDebit
}

input SearchTreasuryInput {
  month: DateRange!
  year: YearOrInt!
  accountType: AccountType
}

input SearchTreasuryMonthInput {
  month: DateRange!
  year: YearRange!
  accountType: AccountType
}

type TreasuryReport {
  name: String
  paymentType: String
  paymentMethod: String
  gatewayProcessor: String
  accountType: String
  month: String
  year: String
  count: Int
  amount: Float
  cardType: String
  cardBrand: String
}

type PreviousYears {
  year: String
  count: Int
  amount: Float
  name: String
  accountType: String
  cardType: String
  cardBrand: String
  paymentMethod: String
}

type SearchTreasury {
  lastKey: String
  filteredData: [TreasuryReport]
  previousYears: [PreviousYears]
}

type MonthlyTreasuryReport {
  name: String
  accountType: String
  startMonth: String
  startYear: String
  endMonth: String
  endYear: String
  count: Int
  amount: Float
  cardType: String
  cardBrand: String
  paymentType: String
  paymentMethod: String
  paymentGateway: String
}

type MonthlySearchTreasury {
  lastKey: String
  filteredData: [MonthlyTreasuryReport]
}


type RAWirelessMonthlyTypeCode {
  lastKey: String
  filteredData: [MonthlyTypeCode]
}

type RAWirelessMonthlyModeCode {
  lastKey: String
  filteredData: [MonthlyModeCode]
}

type WirelessFile {
  lastKey: String
}

type DownloadS3Report {
  urlLink: String
}

input ecpayReportFilterInput {
  # Transaction Type
  billerName: String
  reference: String
  subMerchantId: String
  createdAt: DateRange
  status: ECPayStatus
}

type SearchECPayReports {
  lastKey: String
  filteredData: [ECPayReports]
}

type ECPayReports {
  # Keys for Fast Searching
  reference: String
  # Transaction Type
  billerName: String
  subMerchantId: String
  amountValue: Float
  status: String
}


input globeOneReportFilterInput {
  # Key for Fast Searching
  reference: String
  paymentMethod: PaymentMethod
  status: TransactionStatus
  createdAt: DateRange
}

type SearchGlobeOneReport {
  lastKey: String
  filteredData: [GlobeOneReport]
}

type GlobeOneReport {
  createdAt: DateTime
  mobileNumber: String
  amountValue: Float
  paymentMethod: String
  status: String
  reference: String
  refusalReasonRaw: String
}

input payByLinkFilterInput {
  # Key for Fast Searching
  reference: String
  paymentLinkId: String
  createdAt: DateRange
  status: TransactionStatus
}

type SearchPayByLinkReport {
  lastKey: String
  filteredData: [PayByLinkReport]
}

type PayByLinkReport {
  reference: String
  paymentLinkId: String
  createdAt: String
  amountValue: Float
  status: String
  description: String
}

input loadORReportFilterInput {
  # Key for Fast Searching
  reference: String
  channelId: String
  mobileNumber: String
  loadType: String
  paymentGateway: ReportPaymentGateway
  status: ORTransactionStatus
  createdAt: DateRange
}

type SearchLoadORReport {
  lastKey: String
  filteredData: [LoadORReport]
}

type LoadORReport {
  createdAt: String
  channelName: String
  reference: String
  amountValue: Float
  isOrTransaction: Boolean
  paymentGateway: String
  status: String
  orStatus: String
  lukeOrTimestamp: String
  mobileNumber: String
  loadType: String
} 

input installmentReportFilterInput {
  orderReference: String
  # Key for Fast Searching
  reference: String
  installmentTerm: Float
  status: TransactionStatus
  installmentPaymentId: String
  createdAt: DateRange
}

type SearchInstallmentReport {
  lastKey: String
  filteredData: [InstallmentReport]
}

type InstallmentReport {
  orderReference: String
  reference: String
  createdAt: String
  itemPurchased: String
  installmentTerm: String
  amountCurrency: String
  amountValue: Float
  status: String
  creditCardNumber: String
  creditCardBank: String
  creditCardCountry: String
  creditCardType: String
  creditCardHolderName: String
  customerContactNumber: String
  isThreeDFlag: Boolean
  emailAddress: String
  installmentPaymentId: String
} 

input adaDeclinedDetailedInput {
  accountNumber: String
  channelId: String
  entity: Entity
  status: RecurringPaymentStatus
  timestamp: DateRange
}

type SearchADADeclinedDetailed {
  lastKey: String
  filteredData: [ADADeclinedDetailedReport]
}

type ADADeclinedDetailedReport {
  accountNumber: String
  isActive: String
  channelName: String
  transId: String
  enrollmentType: String
  ccNumber: String
  ccType: String
  bankName: String
  tokenId: String
  amount: String
  status: String
  reason: String
  entity: String
  currency: String
  userContact: String
  userEmail: String
  timestamp: DateTime
} 

enum MonthRange {
  January
  February
  March
  April
  May
  June
  July
  August
  September
  October
  November
  December
}

input MonthDateRange {
  start: MonthRange!
  end: MonthRange!
}

input adaSummaryInput {
  bank: String
  month: MonthDateRange!
  year: YearRange!
}

type ADASummary {
  month: String
  year: String
  bank: String
  numberOfBilledAccount: String
  totalAmountAR: String
  approvedTransCount: Float 
  approvedTransAmount: Float 
  declinedTransCount: Float
  declinedTransAmount: Float
  declinedRate: Float
}

type SearchADASummary {
  filteredData: [ADASummary]
}

enum EndGameStatus {
  For_Processing
  Approved
  Declined
}

input endGameReportFilterInput {
  id: String
  accountNumber: String
  userContact: String
  status: EndGameStatus
  timestamp: DateRange
}

type SearchEndGameReport {
  lastKey: String
  filteredData: [EndGameReport]
}

type EndGameReport {
  pspReferenceNumber: String
  accountNumber: String
  # Account Status
  isActive: Boolean
  channelName: String
  transId: String
  # Transaction Date
  timestamp: String
  ccNumber: String
  ccType: String
  bankName: String
  tokenId: String
  # Amount Debited
  amount: String
  # Payment Status
  status: String
  # Payment Reason
  reason: String
  entity: String
  # Amount Currency
  currency: String
  productDescription: String
  price: String
  # MSISDN
  userContact: String
} 

type PayByLinkTableReport {
  id: String
  amount: Float
  description: String
  paymentLink: String
  expiredAt: String
  createdAt: String
  merchantAccount: String
  merchantReference: String
  channelId: String
  paymentGateway: String
  status: String
  customer: String
  updatedAt: String
  linkType: String
}

type SearchPayByLinkTableReport {
  lastKey: String
  filteredData: [PayByLinkTableReport]
}

enum PayByLinkStatus {
  PENDING
  PAID
  EXPIRED
  SETTLED
}

input PayByLinkTableReportInput {
  reference: String
  merchantReference: String
  status: PayByLinkStatus
}
`;
