module.exports = `
enum GCashBBindingStatus {
  ACTIVE
  EXPIRED
  UNBIND
  INACTIVE
  PROCESSING
}

type GCashBindingReport {
  uuid: String
  bindingRequestID: String
  bindingTokenId: String
  channel: String
  channelId: String
  status: String
  phoneNumber: String
  validUntil: String
  gatewayProcessor: String
  paymentMethod: String
  unbindDate: String
  createdAt: String
  updatedAt: String
}

type GCashUnBindingResponse {
  uuid: String
  status: String
  bindingRequestID: String
}

type SearchGCashBindingReport {
  lastKey: String
  filteredData: [GCashBindingReport]
}

input GCashBindingReportInput {
  channel: String
  uuid: String
  bindingRequestID: String
  status: GCashBBindingStatus
  validUntil: DateRange
}

input UpdateGCashBindingWhere {
  bindingRequestID: String!
}

input PaginationReportInput {
  startKeys: String!
  limit: Int!
}

type Query {
  gCashBindingReport(filter: GCashBindingReportInput, pagination: PaginationReportInput!):  SearchGCashBindingReport
}

type Mutation {
  updategCashBindingReport(where: UpdateGCashBindingWhere!): GCashUnBindingResponse
}
`;
