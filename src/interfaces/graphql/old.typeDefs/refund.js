module.exports = `
type RefundReport {
    reference: String
    accountNumber: String
    channelName: String
    timestamp: DateTime
    postedTimestamp: DateTime
    amountValue: String
    status: String
    refundAmount: String
    refundReason: String
    refundApprovalStatus: String
    refundId: String
    gcashTransId: String
    mobileNumber: String
    refundDate: DateTime
    requestTimeStamp: DateTime
    refundRejectedTimestamp: DateTime
    refundStatus: String
    refundType: String
    isRefundable: Boolean
    billType: String
    paymentMethod: String
    finalAmount: Float
    convenienceFee: String
    hasConvenienceFee: Boolean
}

type RefundRequest {
    lastKey: String
    filteredData: [RefundReport]
}

type RefundApproval {
    lastKey: String
    filteredData: [RefundReport]
}

type GCashDetailedReport {
    lastKey: String
    filteredData: [RefundReport]
}

type GCashSummaryReport {
    channelName: String
    totalApprovedRefundAmount: String
    totalApprovedRefundCount: String
    totalForApprovalAmount: String
    totalForApprovalCount: String
    totalAutoRefundAmount: String
    totalAutoRefundCount: String
}

type CardDetailedReport {
    lastKey: String
    filteredData: [RefundReport]
}

type CardSummary {
    channelName: String
    totalApprovedRefundAmount: String
    totalApprovedRefundCount: String
    totalForApprovalAmount: String
    totalForApprovalCount: String
    totalAutoRefundAmount: String
    totalAutoRefundCount: String
}

type CardSummaryReport {
    Bill: [CardSummary]
    NonBill: [CardSummary]
}

type XenditDetailedReport {
    lastKey: String
    filteredData: [RefundReport]
}

type XenditSummaryReport {
    Bill: [CardSummary]
    NonBill: [CardSummary]
}

type Query {
    refundRequestModule(filter: SearchGcashRefundInput, pagination: PaginationReportInput!): RefundRequest!
    refundApprovalModule(filter: SearchGcashRefundInput, pagination: PaginationReportInput!): RefundApproval!
    refundApprovalModuleHistory(filter: SearchGcashRefundInput, pagination: PaginationReportInput!): RefundApproval!
    refundReason: [RefundReason]
    getCardRefundReason: [RefundReason]
    gcashRefundDetailedReport(filter: SearchGcashRefundDetailedReport, pagination: PaginationReportInput!): GCashDetailedReport!
    gcashRefundSummaryReport(filter: SearchGcashRefundSummaryReport): [GCashSummaryReport]!
    cardRefundRequestModule(filter: SearchCardRefundInput, pagination: PaginationReportInput!): RefundRequest!
    cardRefundApprovalModule(filter: SearchCardRefundInput, pagination: PaginationReportInput!): RefundApproval!
    cardRefundApprovalModuleHistory(filter: SearchCardRefundInput, pagination: PaginationReportInput!): RefundApproval!
    cardRefundDetailedReport(filter: SearchCardRefundDetailedReport, pagination: PaginationReportInput!): CardDetailedReport!
    cardRefundSummaryReport(filter: SearchCardRefundSummaryReport): CardSummaryReport!
    xenditRefundRequest(filter: SearchXenditRefundInput, pagination: PaginationReportInput!): RefundRequest!
    xenditRefundApproval(filter: SearchXenditRefundInput, pagination: PaginationReportInput!): RefundApproval!
    xenditRefundApprovalModuleHistory(filter: SearchXenditRefundInput, pagination: PaginationReportInput!): RefundApproval!
    xenditRefundDetailedReport(filter: SearchXenditRefundDetailedReport, pagination: PaginationReportInput!): XenditDetailedReport!
    xenditRefundSummaryReport(filter: SearchXenditRefundSummaryReport): XenditSummaryReport!
}

enum RefundStatus {
    PartialRefund
    FullRefund
    ForApproval
    Processing
    Rejected
    Declined
    Requested
    ForRequest
}

input SearchGcashRefundSummaryReport {
    channelId: String
    refundRange: DateRange
}

input SearchGcashRefundDetailedReport {
    refundId: String
    reference: String
    channelId: String
    createdAt: DateRange
    billType: String
    hasConvenienceFee: Boolean
}

input SearchCardRefundSummaryReport {
    channelId: String
    refundRange: DateRange
}

input SearchCardRefundDetailedReport {
    refundId: String
    reference: String
    channelId: String
    billType: billTypeTransaction
    createdAt: DateRange
}


input SearchGcashRefundInput {
    reference: String
    channelId: String
    status: TransactionStatus
    createdAt: DateRange
}

input SearchCardRefundInput {
    reference: String
    channelId: String
    status: TransactionStatus
    createdAt: DateRange
    refundStatus: RefundStatus
}

input SearchXenditRefundInput {
    reference: String
    channelId: String
    status: TransactionStatus
    createdAt: DateRange
    refundStatus: RefundStatus
}

input SearchXenditRefundSummaryReport {
    channelId: String
    refundRange: DateRange
}

input SearchXenditRefundDetailedReport {
    refundId: String
    reference: String
    channelId: String
    createdAt: DateRange
    billType: String
    hasConvenienceFee: Boolean
}

type Mutation {
    createGcashRefundRequest(data: RefundForApprovalInput!): ResponseRefundApproval
    updateGcashRefundApproval(data: RefundApprovalInput!): ResponseUpdateGCashRefundApproval
    createCardRefundRequest(data: RefundForApprovalInput!): ResponseRefundApproval
    updateCardRefundApproval(data: RefundApprovalInput!): ResponseUpdateCardRefundApproval
    createXenditRefundRequest(data: RefundForApprovalInput!): ResponseRefundApproval
    updateXenditRefundApproval(data: RefundApprovalInput!): ResponseUpdateCardRefundApproval
}

input RefundApprovalInput {
    reference: String!
    action: refundApprovalAction!
    approverRemarks: String
}

enum refundApprovalAction {
    approve
    reject
}

type ResponseUpdateGCashRefundApproval {
    success: Boolean
    code: String
}

type ResponseUpdateCardRefundApproval {
    success: Boolean
    code: String
    errorType: String
    errorDetails: RefundErrorDetails
}

type RefundErrorDetails {
    paymentId: String
    status: String
    error_code: String
    message: String
}

type ResponseRefundApproval {
    approve: Boolean
    refundApprovalStatus: String
}

input RefundForApprovalInput {
    reference: String
    refundAmount: String
    refundReason: String
}
`;
