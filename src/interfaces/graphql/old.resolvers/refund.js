module.exports = {
  Query: {
    //     refundRequestModule: async (_, args, { handlers, user }) => handlers.GCashRefund.refundRequestModule(args, user),
    //     refundApprovalModule: async (_, args, { handlers, user }) => handlers.GCashRefund.refundApprovalModule(args, user),
    //     refundApprovalModuleHistory: async (_, args, { handlers }) =>
    //       handlers.GCashRefund.refundApprovalModuleHistory(args),
    //     refundReason: async (_, args, { handlers }) => handlers.Config.getRefundReasons(),
    //     getCardRefundReason: async (_, args, { handlers }) => handlers.Config.getCardRefundReason(),
    //     gcashRefundDetailedReport: async (_, args, { handlers }) => handlers.GCashRefund.gcashRefundDetailedReport(args),
    //     gcashRefundSummaryReport: async (_, args, { handlers }) => handlers.GCashRefund.gcashRefundSummaryReport(args),
    //     cardRefundRequestModule: async (_, args, { handlers, user }) => handlers.CardRefund.refundRequestModule(args, user),
    //     cardRefundApprovalModule: async (_, args, { handlers, user }) =>
    //       handlers.CardRefund.refundApprovalModule(args, user),
    //     cardRefundApprovalModuleHistory: async (_, args, { handlers }) =>
    //       handlers.CardRefund.refundApprovalModuleHistory(args),
    //     cardRefundDetailedReport: async (_, args, { handlers }) => handlers.CardRefund.cardRefundDetailedReport(args),
    //     cardRefundSummaryReport: async (_, args, { handlers }) => handlers.CardRefund.cardRefundSummaryReport(args),
    //     xenditRefundRequest: async (_, args, { handlers, user }) => handlers.XenditRefund.refundRequest(args, user),
    //     xenditRefundApproval: async (_, args, { handlers, user }) => handlers.XenditRefund.refundApproval(args, user),
    //     xenditRefundApprovalModuleHistory: async (_, args, { handlers }) =>
    //       handlers.XenditRefund.refundApprovalModuleHistory(args),
    //     xenditRefundDetailedReport: async (_, args, { handlers }) => handlers.XenditRefund.xenditRefundDetailedReport(args),
    //     xenditRefundSummaryReport: async (_, args, { handlers }) => handlers.XenditRefund.xenditRefundSummaryReport(args),
  },
  Mutation: {
    //     createGcashRefundRequest: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.GCashRefund.requestForRefund(data, httpInfo, user),
    //     updateGcashRefundApproval: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.GCashRefund.refundapproval(data, httpInfo, user),
    //     createCardRefundRequest: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.CardRefund.requestForRefund(data, httpInfo, user),
    //     updateCardRefundApproval: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.CardRefund.refundapproval(data, httpInfo, user),
    //     createXenditRefundRequest: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.XenditRefund.requestForRefund(data, httpInfo, user),
    //     updateXenditRefundApproval: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.XenditRefund.refundapproval(data, httpInfo, user),
  },
};
