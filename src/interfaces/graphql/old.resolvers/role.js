module.exports = {
  Query: {
    //     role: async (_, args, { handlers }) => handlers.Role.show(args),
    //     roles: async (_, args, { handlers }) => handlers.Role.search(args),
    //     rolesLoose: async (_, args, { handlers }) => handlers.Role.rolesLoose(args),
  },
  Mutation: {
    //     createRole: async (_, { data }, { handlers, httpInfo, user }) => handlers.Role.createRole(data, httpInfo, user),
    //     updateRole: async (_, { data, where }, { handlers, httpInfo, user }) =>
    //       handlers.Role.updateRole(data, where, httpInfo, user),
    //     deleteRole: async (_, { data, where }, { handlers, httpInfo, user }) =>
    //       handlers.Role.deleteRole(data, where, httpInfo, user),
    //     deleteRoles: async (_, { data, where }, { handlers, httpInfo, user }) =>
    //       handlers.Role.deleteRoles(data, where, httpInfo, user),
    //     uploadRoles: async (_, args, { handlers, httpInfo, user }) => handlers.Role.upload(args, httpInfo, user),
  },
};
