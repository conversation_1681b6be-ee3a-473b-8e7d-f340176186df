module.exports = {
  Query: {
    //     convenienceFee: async (_, args, { handlers }) => handlers.ConvenienceFee.show(args),
    //     convenienceFees: async (_, args, { handlers }) => handlers.ConvenienceFee.search(args),
  },
  Mutation: {
    //     createConvenienceFee: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.ConvenienceFee.createConvenienceFee(data, httpInfo, user),
    //     updateConvenienceFee: async (_, { data, where }, { handlers, httpInfo, user }) =>
    //       handlers.ConvenienceFee.updateConvenienceFee(data, where, httpInfo, user),
    //     deleteConvenienceFee: async (_, { data, where }, { handlers, httpInfo, user }) =>
    //       handlers.ConvenienceFee.deleteConvenienceFee(data, where, httpInfo, user),
  },
};
