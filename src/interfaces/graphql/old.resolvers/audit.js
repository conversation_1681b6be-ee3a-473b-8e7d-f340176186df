module.exports = {
  Query: {
    //     audit: async (_, args, { handlers }) => handlers.Audit.show(args),
    //     audits: async (_, args, { handlers }) => handlers.Audit.search(args),
    //     notifications: (_, args, { handlers }) => handlers.Audit.notifications(args),
    //     searchNotifications: async (_, args, { handlers }) => handlers.Audit.search(args),
    //     notifIsNotViewCount: async (_, args, { handlers }) => handlers.Audit.isNotViewCount(),
  },
  Mutation: {
    //     updateNotificationStatus: async (_, { data }, { handlers }) => handlers.Audit.updateStatus(data),
  },
};
