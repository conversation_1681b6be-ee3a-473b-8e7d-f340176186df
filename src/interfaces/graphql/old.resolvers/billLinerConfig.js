module.exports = {
  Query: {
    //     listBillLinerConfig: async (_, args, { handlers }) => handlers.BillLinerConfig.listBillLinerConfig(args),
  },
  Mutation: {
    //     deleteBillLinerConfig: async (_, { data, where }, { handlers, httpInfo, user }) =>
    //       handlers.BillLinerConfig.deleteBillLinerConfig(data, where, httpInfo, user),
    //     createBillLinerConfig: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.BillLinerConfig.createBillLinerConfig(data, httpInfo, user),
  },
};
