module.exports = {
  Query: {
    //     convenienceFeeBrand: async (_, args, { handlers }) => handlers.ConvenienceFeeBrand.show(args),
    //     convenienceFeeBrands: async (_, args, { handlers }) => handlers.ConvenienceFeeBrand.search(args),
  },
  Mutation: {
    //     createConvenienceFeeBrand: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.ConvenienceFeeBrand.createConvenienceFeeBrand(data, httpInfo, user),
    //     updateConvenienceFeeBrand: async (_, { data, where }, { handlers, httpInfo, user }) =>
    //       handlers.ConvenienceFeeBrand.updateConvenienceFeeBrand(data, where, httpInfo, user),
    //     deleteConvenienceFeeBrand: async (_, { where }, { handlers, httpInfo, user }) =>
    //       handlers.ConvenienceFeeBrand.deleteConvenienceFeeBrand(where, httpInfo, user),
  },
};
