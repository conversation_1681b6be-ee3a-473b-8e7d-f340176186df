module.exports = {
  Query: {
    //     mid: async (_, { where }, { handlers }) => handlers.Mid.show(where),
    //     mids: async (_, args, { handlers }) => handlers.Mid.search(args),
  },
  Mutation: {
    //     createMid: async (_, { data }, { handlers, httpInfo, user }) => handlers.Mid.createMid(data, httpInfo, user),
    //     updateMid: async (_, { data, where }, { handlers, httpInfo, user }) =>
    //       handlers.Mid.updateMid(data, where, httpInfo, user),
    //     deleteMid: async (_, { data, where }, { handlers, httpInfo, user }) =>
    //       handlers.Mid.deleteMid(data, where, httpInfo, user),
  },
};
