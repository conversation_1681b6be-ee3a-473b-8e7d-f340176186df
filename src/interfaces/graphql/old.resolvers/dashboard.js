module.exports = {
  Query: {
    //     transactions: async (_, args, { handlers }) => handlers.Dashboard.transactions(args),
    //     performance: async (_, args, { handlers }) => handlers.Dashboard.performances(),
    //     overallLastUpdatedAt: async (_, args, { handlers }) => handlers.Dashboard.overallupdatedat(),
    //     pgUptimeMonitor: async (_, args, { handlers }) => handlers.Dashboard.paymentGatewayUptime(),
    //     channelAndGatewayStatus: async (_, args, { handlers }) => handlers.Dashboard.search(args),
    //     overallTransactions: async (_, args, { handlers }) => handlers.Dashboard.overallTransactions(args),
  },
};
