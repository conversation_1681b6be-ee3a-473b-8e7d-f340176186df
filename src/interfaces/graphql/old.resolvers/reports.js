module.exports = {
  Query: {
    //     payByLinkReport: async (_, args, { handlers }) => handlers.Report.payByLinkReport(args),
    reports: async (_, args, { handlers }) => handlers.Report.search(args),
    //     failedReports: async (_, args, { handlers }) => handlers.Report.failedReports(args),
    //     billingReports: async (_, args, { handlers }) => handlers.Report.billing(args),
    //     gatewayTransactionReports: async (_, args, { handlers }) => handlers.Report.gateway(args),
    //     revenueWireline: async (_, args, { handlers }) => handlers.Report.revenueWireline(args),
    //     gatewayCollectionSummary: async (_, args, { handlers }) => handlers.Report.collection(args),
    //     transactionCount: async (_, args, { handlers }) => handlers.Report.transactionCount(args),
    //     monthlyReports: async (_, args, { handlers }) => handlers.Report.monthlyReports(args),
    //     treasuryYTD: async (_, args, { handlers }) => handlers.Report.treasuryYTD(args),
    //     monthlyTreasury: async (_, args, { handlers }) => handlers.Report.monthlyTreasury(args),
    //     treasuryReports: async (_, args, { handlers }) => handlers.Report.search(args),
    //     channelReports: async (_, args, { handlers }) => handlers.Report.channelReport(args),
    //     raWirelessMonthlyTypeCode: async (_, args, { handlers }) => handlers.Report.raWirelessMonthly(args),
    //     raWirelessMonthlyModeCode: async (_, args, { handlers }) => handlers.Report.raWirelessMonthly(args),
    //     gotsReports: async (_, args, { handlers }) => handlers.Report.gotsReport(args),
    //     ecpayReports: async (_, args, { handlers }) => handlers.Report.ecpayReport(args),
    //     globeOneReport: async (_, args, { handlers }) => handlers.Report.globeOneReport(args),
    //     payByLink: async (_, args, { handlers }) => handlers.Report.payByLink(args),
    //     loadORReport: async (_, args, { handlers }) => handlers.Report.loadORReport(args),
    //     installmentReport: async (_, args, { handlers }) => handlers.Report.installmentReport(args),
    //     adaDeclinedDetailedReport: async (_, args, { handlers }) => handlers.Report.adaDeclinedDetailedReport(args),
    //     adaSummaryReport: async (_, args, { handlers }) => handlers.Report.adaSummaryReport(args),
    //     endGameTransactionReport: async (_, args, { handlers }) => handlers.Report.endGameTransactionReport(args),
    //     uploadTransactions: async (_, args, { handlers, httpInfo, user }) =>
    //       handlers.Report.uploadTransactions(args, httpInfo, user),
  },
  Mutation: {
    //     downloadReports: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.Report.verifyDownload(data, httpInfo, user),
    //     downloadS3Reports: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.Report.downloadS3Reports(data, httpInfo, user),
    //     downloadS3LukeBatchFiles: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.Report.downloadS3LukeBatchFiles(data, httpInfo, user),
  },
};
