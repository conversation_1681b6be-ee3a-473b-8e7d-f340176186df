module.exports = {
  Query: {
    //     searchInstallmentMid: async (_, args, { handlers }) => handlers.InstallmentMId.searchInstallment(args),
    //     listInstallmentBank: async (_, args, { handlers }) => handlers.InstallmentMId.listInstallment(args),
  },
  Mutation: {
    //     createInstallmentMid: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.InstallmentMId.createInstallment(data, httpInfo, user),
    //     deleteInstallmentMid: async (_, { data, where }, { handlers, httpInfo, user }) =>
    //       handlers.InstallmentMId.deleteInstallment(data, where, httpInfo, user),
    //     downloadInstallmentMid: async (_, args, { handlers, httpInfo, user }) =>
    //       handlers.InstallmentMId.downloadInstallment(args, httpInfo, user),
  },
};
