module.exports = {
  Query: {
    //     configs: async (_, args, { handlers }) => handlers.Config.show(args),
    //     refundValidity: async (_, args, { handlers }) => handlers.Config.refundValidity(),
    //     listPSORPaymentType: async (_, args, { handlers }) => handlers.Config.getPSPaymentType(),
  },
  Mutation: {
    //     updateConfig: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.Config.updateConfig(data, httpInfo, user),
  },
};
