module.exports = {
  Query: {
    //     bank: async (_, args, { handlers }) => handlers.Bank.show(args),
    //     banks: async (_, args, { handlers }) => handlers.Bank.search(args),
  },
  Mutation: {
    //     createBank: async (_, { data }, { handlers, httpInfo, user }) => handlers.Bank.createBank(data, httpInfo, user),
    //     updateBank: async (_, { data, where }, { handlers, httpInfo, user }) =>
    //       handlers.Bank.updateBank(data, where, httpInfo, user),
    //     deleteBank: async (_, { data, where }, { handlers, httpInfo, user }) =>
    //       handlers.Bank.deleteBank(data, where, httpInfo, user),
    //     uploadBanks: async (_, args, { handlers, httpInfo, user }) => handlers.Bank.upload(args, httpInfo, user),
  },
};
