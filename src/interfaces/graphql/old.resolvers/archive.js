module.exports = {
  Query: {
    //     paybillReferenceCodes: async (_, args, { handlers }) => handlers.Archive.searchReferenceCodes(args),
    //     paybillTransactionLogs: async (_, args, { handlers }) => handlers.Archive.searchTransactionLogsGateway(args),
    //     archiveAuditLogs: async (_, args, { handlers }) => handlers.Archive.searchArchiveAuditLogs(args),
    //     archiveTransactionLogs: async (_, args, { handlers }) => handlers.Archive.searchArchiveTransactionLogs(args),
    //     archiveSwipeLogs: async (_, args, { handlers }) => handlers.Archive.searchArchiveSwipeLogs(args),
  },
  Mutation: {
    //     downloadArchives: async (_, { data }, { handlers, httpInfo, user }) =>
    //       handlers.Archive.verifyDownload(data, httpInfo, user),
  },
};
