const { applyMiddleware } = require('graphql-middleware');

const { mergeTypeDefs, mergeResolvers } = require('@graphql-tools/merge');

const { loadFilesSync } = require('@graphql-tools/load-files');

const { makeExecutableSchema } = require('@graphql-tools/schema');
const path = require('path');

const typeDefs = mergeTypeDefs(loadFilesSync(path.join(__dirname, './typeDefs')));
const resolvers = mergeResolvers(loadFilesSync(path.join(__dirname, './resolvers')));

const authorization = async (resolve, _root, args, context, info) => {
  const { user, middlewares, handlers, psaccesstoken } = context;
  if (info.path.prev === undefined) {
    const operationName = info.operation.name?.value;
    const isActionPolling = info.variableValues.isPolling;
    if (operationName !== 'login') {
      await middlewares.authorization.authorize({
        action: info.fieldName,
        user,
        handlers,
        args,
        psaccesstoken,
        isActionPolling,
      });
    }
  }
  const result = await resolve(_root, args, context, info);
  return result;
};

const Schema = makeExecutableSchema({
  typeDefs,
  resolvers,
});
// add middleware to ALL resolvers
// (also to nested resolver if they are defined in schema like Post.author)
const schemaWithMiddleware = applyMiddleware(Schema, authorization);

module.exports = schemaWithMiddleware;
