module.exports = {
  Query: {
    channel: async (_, args, { handlers }) => handlers.Channel.show(args),
    channels: async (_, args, { handlers }) => handlers.Channel.search(args),
    channelsLoose: async (_, args, { handlers }) => handlers.Channel.channelsLoose(args),
    //     subMerchants: async (_, { where }, { handlers }) => handlers.Channel.availableSubMerchant(where),
    //     chanelSubMerchants: async (_, args, { handlers }) => handlers.Channel.checkChannelSubMerchant(args),
    channelsBillType: async (_, args, { handlers }) => handlers.Channel.channelsBillType(args),
    //     channelDropInSimulator: async (_, args, { handlers }) => handlers.Channel.show(args),
    payByLinkChannels: async (_, args, { handlers }) => handlers.Channel.payByLinkChannels(args),
  },
  Mutation: {
    createChannel: async (_, { data }, { handlers, httpInfo, user }) =>
      handlers.Channel.createChannel(data, httpInfo, user),
    updateChannel: async (_, { data, where }, { handlers, httpInfo, user }) =>
      handlers.Channel.updateChannel(data, where, httpInfo, user),
    deleteChannel: async (_, { data, where }, { handlers, httpInfo, user }) =>
      handlers.Channel.deleteChannel(data, where, httpInfo, user),
    verifyChannel: async (_, { data }, { handlers, user }) => handlers.Channel.verify(data, user),
    deleteChannels: async (_, { data, where }, { handlers, httpInfo, user }) =>
      handlers.Channel.deleteChannels(data, where, httpInfo, user),
  },
};
