module.exports = {
  Query: {
    user: async (_, args, { handlers }) => handlers.User.show(args),
    users: async (_, args, { handlers }) => handlers.User.search(args),
    usersSummary: async (_, args, { handlers }) => handlers.User.summary(args),
  },
  Mutation: {
    createUser: async (_, { data }, { handlers, httpInfo, user }) => handlers.User.registration(data, httpInfo, user),
    validateToken: async (_, { data }, { handlers, httpInfo, user }) =>
      handlers.User.validateToken(data, httpInfo, user),
    updateUser: async (_, { data, where }, { handlers, httpInfo, user }) =>
      handlers.User.updateUser(data, where, httpInfo, user),
    deleteUser: async (_, { data, where }, { handlers, httpInfo, user }) =>
      handlers.User.deleteUser(data, where, httpInfo, user),
    deleteUsers: async (_, { data, where }, { handlers, httpInfo, user }) =>
      handlers.User.deleteUsers(data, where, httpInfo, user),
    downloadUsers: async (_, args, { handlers, httpInfo, user }) => handlers.User.download(httpInfo, user),
    uploadUsers: async (_, args, { handlers, httpInfo, user }) => handlers.User.upload(args, httpInfo, user),
    deactivateUser: async (_, { data, where }, { handlers, httpInfo, user }) =>
      handlers.User.deactivateUser(data, where, httpInfo, user),
    uploadUsersStatusUpdate: async (_, args, { handlers, httpInfo, user }) =>
      handlers.User.uploadUsersStatusUpdate(args, httpInfo, user),
  },
};
