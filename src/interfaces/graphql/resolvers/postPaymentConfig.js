module.exports = {
  Query: {
    postPaymentConfig: async (_, args, { handlers, user }) => handlers.PostPaymentConfig.search(args, user),
    postPaymentConfigGatewayMethods: async (_, args, { handlers }) =>
      handlers.PostPaymentConfig.getGatewayMethods(args),
  },
  Mutation: {
    createPostPaymentConfig: async (_, { data }, { handlers, httpInfo, user }) =>
      handlers.PostPaymentConfig.createPostPaymentConfig(data, httpInfo, user),
    updatePostPaymentConfig: async (_, { data, where }, { handlers, httpInfo, user }) =>
      handlers.PostPaymentConfig.updatePostPaymentConfig(data, where, httpInfo, user),
    deletePostPaymentConfig: async (_, { where }, { handlers, httpInfo, user }) =>
      handlers.PostPaymentConfig.deletePostPaymentConfig(where, httpInfo, user),
  },
};
