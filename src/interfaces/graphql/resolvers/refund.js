module.exports = {
  Query: {
    xenditRefundApproval: async (_, args, { handlers, user }) => handlers.XenditRefund.refundApproval(args, user),
    xenditRefundSummaryReport: async (_, args, { handlers }) => handlers.XenditRefund.xenditRefundSummaryReport(args),
    xenditRefundApprovalModuleHistory: async (_, args, { handlers }) =>
      handlers.XenditRefund.refundApprovalModuleHistory(args),
    xenditRefundRequest: async (_, args, { handlers, user }) => handlers.XenditRefund.refundRequest(args, user),
  },
  Mutation: {
    updateXenditRefundApproval: async (_, { data }, { handlers, httpInfo, user }) =>
      handlers.XenditRefund.updateRefundRequest(data, httpInfo, user),
    createXenditRefundRequest: async (_, { data }, { handlers, httpInfo, user }) =>
      handlers.XenditRefund.createRefundRequest(data, httpInfo, user),
  },
};
