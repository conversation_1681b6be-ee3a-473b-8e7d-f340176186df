module.exports = `
type Audits {
  id: String
  userId: String
  userEmail: String
  userName: String
  roleId: String
  roleName: String
  ipAddress: String
  userAgent: String
  category: String
  isViewed: Boolean
  reasonToDelete: String
  reasonToUpdate: String
  oldValue: String
  newValue: String
  createdAt: DateTime
  reasonToDeactivate: String
}

type Audit {
  id: String
  user: User
  userName: String
  roleId: String
  roleName: String
  ipAddress: String
  userAgent: String
  category: String
  oldValue: String
  newValue: String
  reasonToDelete: String
  reasonToUpdate: String
  createdAt: DateTime
  reasonToDeactivate: String
}

type AuditKeys {
  id: String
  sortKey: String
  createdAt: String
}

type SearchFilterAudit {
  lastKey: AuditKeys
  count: Int
  filteredData: [Audits]
}

type Notifications {
  id: String
  userId: String
  roleId: String
  roleName: String
  userName: String
  userEmail: String
  ipAddress: String
  userAgent: String
  category: String
  isViewed: Boolean
  createdAt: DateTime
}

type SearchFilterNotifications {
  lastKey: AuditKeys
  count: Int
  filteredData: [Notifications]
}

type NotificationAudit {
  count: Int
  filteredData: [Notifications]
}

type NotifIsNotViewCount {
  isNotViewed: Int
}

type UpdateStatus {
  status: String
}

type Query {
  audits(filter: SearchAuditInput,  pagination: AuditPaginationInput!): SearchFilterAudit!
  audit(where: AuditPrimary!): Audit
  notifications(data: NotificationInput!, isPolling: Boolean): NotificationAudit!
  searchNotifications(filter: SearchNotificationInput,  pagination: AuditPaginationInput!): SearchFilterNotifications!
  notifIsNotViewCount(isPolling: Boolean): NotifIsNotViewCount
}

type Mutation {
  updateNotificationStatus(data: UpdateNotificationInput!): UpdateStatus
}

input UpdateNotificationInput {
  id: [String]!
}

input SearchAuditInput  {
  id: String
  ipAddress: String
  userEmail: String
  userAgent: String
  category: String
  createdAt: [DateRange]
}

input SearchNotificationInput  {
  userName: String
  roleId: [String]
  userEmail: String
  category: String
  createdAt: [DateRange]
}

input AuditPrimary {
  id: String!
}

input NotificationInput {
  limit: Int!
}

input AuditStartKey {
  id: String!
  sortKey: String!
  createdAt: String!
}

input AuditPaginationInput {
  limit: Int
  start: [AuditStartKey]!
}
`;
