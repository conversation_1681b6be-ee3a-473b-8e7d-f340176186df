module.exports = `
input ChannelFilter {
  id: String
  name: String
}

# Input for create mutation
input CreateChannel {
  name: String!
  channelCode: String!
  email: String!
  billType: BillTypeOption!
  ipAddress: String
  merchantCode: String
  merchantKey: String
  xApiKey: String
  isForPayByLink: Boolean
}

# Input for update mutation
input UpdateChannel {
  name: String
  channelCode: String
  ipAddress: String
  callbackUrl: String
  emailNotif: Boolean
  smsNotif: Boolean
  enablePaymentSession: Boolean
  merchantCode: String
  merchantKey: String
  xApiKey: String
  globeEmailNotification: Boolean
  globeEmailNotificationPatternId: String
  innoveEmailNotification: Boolean
  innoveEmailNotificationPatternId: String
  bayanEmailNotification: Boolean
  bayanEmailNotificationPatternId: String
  failedEmailNotification: Boolean
  billType: BillTypeOption
  enableCardHolderName: Boolean
  isCallbackEncrypted: Boolean
  subMerchants: [ChannelSubMerchant]
  cardPaymentMethod: cardPaymentMethods
  cardHolderNameType: cardHolderNameTypes
  gcashPaymentMethod: gcashPaymentMethods
  bankPaymentMethod: bankPaymentMethods
  ewalletPaymentMethod: ewalletPaymentMethods
  isOrEnabled: Boolean
  gcreditSubMerchantId: String
  isForPayByLink: Boolean
  gcashOneClickEnabled: Boolean
  gcashOneClickMerchantId: String
  gcashOneClickClientId: String
  gcashOneClickClientSecret: String
  gcashOneClickProductCode: String
  gcashOneClickRedirectUrl: String
  gcashOneClickValidity: Int
  gcashOneClickBindingIdPrefix: String
  bindCallbackUrl: String
  isSecureConnection: Boolean
  gateways: GatewaysInput
}

input ChannelSubMerchant {
  merchant: Merchants
  serviceType: String
}

input DeleteChannel {
  reasonToDelete: String!
}

input ChannelPrimary {
  id: String!
}

input SearchChannel {
  name: String
  channelId: String
}

input ChannelVerification {
  channelId: String!
  email: String
  confirmationCode: String
}

# Input MerchantPrimary
input MerchantPrimary {
  merchant: Merchants
}

input GatewaysInput {
  xendit: XenditGatewayInput
}

input XenditGatewayInput {
  card_straight: [String]
  card_installment: [String]
  otc: [String]
}
`;
