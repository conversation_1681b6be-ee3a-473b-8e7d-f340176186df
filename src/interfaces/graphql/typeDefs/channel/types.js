module.exports = `
type Channel {
  id: String
  name: String
  channelCode: String
  callbackUrl: String
  isVerified: Boolean
  email: String
  ipAddress: String
  clientId: String
  clientSecret: String
  emailNotif: Boolean
  smsNotif: Boolean
  enablePaymentSession: Boolean
  createdAt: DateTime
  updatedAt: DateTime
  merchantCode: String
  merchantKey: String
  xApiKey: String
  paymentGateway: String
  globeEmailNotification: Boolean
  globeEmailNotificationPatternId: String
  innoveEmailNotification: Boolean
  innoveEmailNotificationPatternId: String
  bayanEmailNotification: Boolean
  bayanEmailNotificationPatternId: String
  failedEmailNotification: Boolean
  billType: String
  enableCardHolderName: Boolean
  isCallbackEncrypted: Boolean
  callbackPassPhrase: String
  cardPaymentMethod: String
  cardHolderNameType: String
  gcashPaymentMethod: String
  bankPaymentMethod: String
  subMerchants: [ChannelSubMerchants]
  isOrEnabled: Boolean
  gcreditSubMerchantId: String
  ewalletPaymentMethod: String
  isForPayByLink: Boolean
  gcashOneClickEnabled: Boolean
  gcashOneClickMerchantId: String
  gcashOneClickClientId: String
  gcashOneClickClientSecret: String
  gcashOneClickProductCode: String
  gcashOneClickRedirectUrl: String
  gcashOneClickValidity: Int
  gcashOneClickBindingIdPrefix: String
  bindCallbackUrl: String
  isSecureConnection: Boolean
  gateways: ChannelGateways
}

type ChannelSubMerchants{
  merchant: Merchants
  serviceType: String
}

type ChannelsLoose {
  id: String
  name: String 
  channelId: String 
}

type PayByLinkChannel {
  id: String
  name: String
  channelId: String
  clientId: String
  clientSecret: String
}

type ChannelSearchOutput implements BaseSearchOutput{
  lastKey: String
  count: Int
  cursors: [String]
  filteredData: [Channel]
}

type ChannelVerified {
  verified: Boolean
}

type SubMerchantExist {
  exist: Boolean
}

type ChannelGateways {
  xendit: XenditGateway
}

type XenditGateway {
  card_straight: [String]
  card_installment: [String]
  otc: [String]
}
`;
