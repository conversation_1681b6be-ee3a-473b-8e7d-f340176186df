module.exports = `
type Query {
  payByLinkChannels: [PayByLinkChannel]
  channels(filter: ChannelFilter): ChannelSearchOutput
  channel(filter: ChannelFilter!): Channel
  # TODO: Remove and replace with 'channels' query
  channelsLoose: [ChannelsLoose] 
  subMerchants(where: MerchantPrimary): [SubMerchant]
  chanelSubMerchants(id: String!): SubMerchantExist
  channelsBillType(billType: BillTypeOption!): [Channel]
  # TODO: Remove because adyen is no longer used
  channelDropInSimulator(where: ChannelPrimary!): Channel
}
`;
