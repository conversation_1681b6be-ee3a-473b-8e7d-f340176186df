module.exports = `
    type RefundReport {
        paymentId: String
        accountNumber: String
        channelName: String
        timestamp: DateTime
        postedTimestamp: DateTime
        amountValue: String
        status: String
        refundAmount: String
        refundReason: String
        refundApprovalStatus: String
        refundId: String
        gcashTransId: String
        mobileNumber: String
        refundDate: DateTime
        requestTimeStamp: DateTime
        refundRejectedTimestamp: DateTime
        refundStatus: String
        refundType: String
        isRefundable: Boolean
        billType: String
        paymentMethod: String
        finalAmount: Float
        convenienceFee: String
        hasConvenienceFee: Boolean
        createDateTime: DateTime
        transactionId: String
    }

    type RefundRequest {
        lastKey: String
        filteredData: [RefundReport]
    }

    type RefundApproval {
        lastKey: String
        filteredData: [RefundReport]
    }

    type GCashDetailedReport {
        lastKey: String
        filteredData: [RefundReport]
    }

    type XenditSummaryReport {
        Bill: [CardSummary]
        NonBill: [CardSummary]
    }
        
    type CardSummary {
        channelName: String
        totalApprovedRefundAmount: String
        totalApprovedRefundCount: String
        totalForApprovalAmount: String
        totalForApprovalCount: String
        totalAutoRefundAmount: String
        totalAutoRefundCount: String
    }

    type ResponseUpdateCardRefundApproval {
        success: Boolean
        code: String
        errorType: String
        errorDetails: RefundErrorDetails
    }

    type RefundErrorDetails {
        paymentId: String
        status: String
        error_code: String
        message: String
    }

    type ResponseRefundApproval {
        approve: Boolean
        refundApprovalStatus: String
    }

`;
