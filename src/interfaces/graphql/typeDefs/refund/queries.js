module.exports = `
    type Query {
        xenditRefundApproval(filter: SearchXenditRefund, pagination: PaginationInput!): RefundApproval!
        xenditRefundSummaryReport(filter: SearchXenditRefundSummaryReport): XenditSummaryReport!
        xenditRefundApprovalModuleHistory(filter: SearchXenditRefund, pagination: PaginationInput!): RefundApproval!
        xenditRefundRequest(filter: SearchXenditRefund, pagination: PaginationInput!): RefundRequest!
    }
`;
