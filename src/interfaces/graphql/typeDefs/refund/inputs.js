module.exports = `
  input SearchXenditRefund {
    paymentId: String
    accountNumber: String
    channelId: String
    status: TransactionStatus
    createDateTime: DateRange
    refundStatus: RefundStatus
  }

  input SearchXenditRefundSummaryReport {
    accountNumber: String
    refundRange: DateRange
  }
  

  input UpdateRefundApproval {
    paymentId: String!
    transactionId: String
    action: refundApprovalAction!
    approverRemarks: String
  }


  input CreateRefundRequest {
    paymentId: String
    transactionId: String
    refundAmount: String
    refundReason: String
  }
`;
