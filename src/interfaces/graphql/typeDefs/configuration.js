module.exports = `

type Query {
  configs: Configurations
  refundValidity: RefundConfiguration
  listPSORPaymentType: [PSORPaymentTypes]
}  

type RefundConfiguration {
  days: String
}

type Mutation {
  updateConfig(data: UpdateConfigurations!): Configurations
}

enum Merchants {
  ECPay
  Globe
}

type SubMerchant {
  serviceType: String
  subMerchantId: String
  merchant: String
}

type RefundReason {
  reason: String
}


type ConfigIpWhitelist {
  xendit: ConfigIpWhitelistXendit
}

type ConfigIpWhitelistXendit {
  callback: [String]
}

type EmailAddressRecipient {
  email: String
}

type PSORPaymentTypes {
  description: String
  name: String
  or: String
  orVat: String
}

type SwipeORPaymentTypes {
  description: String
  name: String
  or: String
  orVat: String
}

type Configurations {
  refreshTime: String
  paymentServiceApiMaintenance: Boolean
  globeEmailNotification: Boolean
  globeEmailNotificationPatternId: String
  innoveEmailNotification: Boolean
  innoveEmailNotificationPatternId: String
  bayanEmailNotification: Boolean
  bayanEmailNotificationPatternId: String
  subMerchants: [SubMerchant]
  paymentMethodGcash: String
  paymentMethodCard: String
  paymentMethodBank: String
  amaxLoadConsumer: String
  amaxLoadRetailer: String
  gcashRefundRetries: String
  refundReason: [RefundReason]
  cardRefundReason: [RefundReason]
  PSORPaymentType: [PSORPaymentTypes]
  swipeORPaymentType: [SwipeORPaymentTypes]
  dailyContentGCashReportPatternId: String!
  dailyContentGCashReportRecipient: [EmailAddressRecipient]
  monthlyContentGCashReportPatternId: String!
  monthlyContentGCashReportRecipient: [EmailAddressRecipient]
  ecpayReportEmailPatternId: String
  ecpayReportRecipient: [EmailAddressRecipient]
  globeOneReportEmailPatternId: String!
  globeOneReportRecipient: [EmailAddressRecipient]
  collectionReportEmailPatternId: String!
  collectionReportRecipient: [EmailAddressRecipient]
  creditCardReportEmailPatternId: String!
  creditCardReportRecipient: [EmailAddressRecipient]
  channelReportEmailPatternId: String!
  channelReportRecipient: [EmailAddressRecipient]
  billingReportPatternId: String!
  billingReportRecipient: [EmailAddressRecipient]
  orGenerationConfig: String
  configIpWhitelist: ConfigIpWhitelist
  otcCodeLimit: Float
}

input UpdateConfigurations{
  refreshTime: String
  paymentServiceApiMaintenance: Boolean
  globeEmailNotification: Boolean
  globeEmailNotificationPatternId: String!
  innoveEmailNotification: Boolean
  innoveEmailNotificationPatternId: String!
  bayanEmailNotification: Boolean
  bayanEmailNotificationPatternId: String!
  subMerchants: [InputSubMerchant]
  paymentMethodGcash: gcash
  paymentMethodCard: cards
  paymentMethodBank: banks
  amaxLoadConsumer: String
  amaxLoadRetailer: String
  gcashRefundRetries: String
  refundReason: [InputRefundReason]
  cardRefundReason: [InputRefundReason]
  PSORPaymentType: [InputPSORPaymentTypes]
  swipeORPaymentType: [InputSwipePaymentTypes]
  dailyContentGCashReportPatternId: String!
  dailyContentGCashReportRecipient: [InputEmailAddressRecipient]
  monthlyContentGCashReportPatternId: String
  monthlyContentGCashReportRecipient: [InputEmailAddressRecipient]
  ecpayReportEmailPatternId: String!
  ecpayReportRecipient: [InputEmailAddressRecipient]
  globeOneReportEmailPatternId: String
  globeOneReportRecipient: [InputEmailAddressRecipient]
  collectionReportEmailPatternId: String
  collectionReportRecipient: [InputEmailAddressRecipient]
  creditCardReportEmailPatternId: String
  creditCardReportRecipient: [InputEmailAddressRecipient]
  channelReportEmailPatternId: String
  channelReportRecipient: [InputEmailAddressRecipient]
  billingReportPatternId: String
  billingReportRecipient: [InputEmailAddressRecipient]
  orGenerationConfig: orGeneration
  configIpWhitelist: InputConfigIpWhitelist
  otcCodeLimit: Float
}

enum orGeneration {
  uni
  luke
}

enum gcash {
  mynt
}

enum cards {
  adyen
  ipay88
}

enum banks {
  bpi
}

input InputSubMerchant {
  serviceType: String
  subMerchantId: String
  merchant: Merchants
}

input InputRefundReason {
  reason: String
}

input InputConfigIpWhitelist {
  xendit: InputConfigIpWhitelistXendit
}

input InputConfigIpWhitelistXendit {
  callback: [String]
}

input InputEmailAddressRecipient {
  email: String
}

input InputPSORPaymentTypes {
  #Promo Description
  description: String
  #Payment Type
  name: String
  #Promo Code
  or: String
  #Promo Vat
  orVat: String
}

input InputSwipePaymentTypes {
  #Promo Description
  description: String
  #Payment Type
  name: String
  #Promo Code
  or: String
  #Promo Vat
  orVat: String
}

`;
