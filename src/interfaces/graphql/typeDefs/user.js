module.exports = `

type User {
  id: String 
  name: String
  email: String
  role: Role
  department: String
  division: String
  group: String
  channel: String
  loginTime: DateTime
  isActive: Boolean
  reasonToDeactivate: String
  mobileNumber: String
  isActiveAt: DateTime
  emailNotif: Boolean
  smsNotif: Boolean
  createdAt: DateTime
  updatedAt: DateTime
  assignedChannels: [UserAssignedChannels]
  cardAssignedChannels: [UserAssignedChannels]
  ewalletAssignedChannels: [UserAssignedChannels]
  postPaymentConfigChannels: [UserAssignedChannels]
  billType: String
}

type downloadUser {
  id: String 
  name: String
  emailAddress: String
  roleName: String
  roleDescription: String
  group: String
  permission: String
  createdAt: DateTime
  loginTime: DateTime
  status: String
}

type UsersSearchOutput implements BaseSearchOutput {
  lastKey: String
  cursors: [String]
  count: Int
  filteredData: [User]
}

type UserSummaryTotal {
  activeUsers: [Summary]
  inactiveUsers: [Summary]
}

type Summary{
  month: String
  count: Int
}

type BatchDeleteResponse {
  unprocessedItems: Int
}

type BatchDeactivateResponse {
  unprocessedItems: Int
}

type UserAssignedChannels {
  channelId: String
  name: String
}

type validUser {
  email: String  
}

type invalidUser {
  email: String
  errorReason: String
}

type bulkUploadUserStatus {
  filename: String
  mimetype: String
  validUsers: [validUser]
  invalidUsers: [invalidUser]  
}

type Query {
  user(where: UserPrimary!): User
  users(filter: UsersFilter!, pagination: PaginationInput!): UsersSearchOutput!
  usersSummary(range: DateRange!, isPolling: Boolean): UserSummaryTotal
}

type Mutation {
  createUser(data: RegisterUser!): User!
  validateToken(data: ValidateToken!): User!
  updateUser(data: UpdateUser!, where: UserPrimary!): User 
  deleteUser(data: DeleteUser!, where: UserPrimary!): User 
  deleteUsers(data: DeleteUser!, where: Ids!): BatchDeleteResponse
  downloadUsers: [downloadUser]
  uploadUsers(file: Upload!): File!
  deactivateUser(data: DeactivateUser!, where: Ids!): BatchDeactivateResponse
  uploadUsersStatusUpdate(file: Upload!): bulkUploadUserStatus!
}


# Input Validate Token
input ValidateToken {
  token: String!
}

# Input Registration
input RegisterUser {
  name: String!
  email: String!
  roleId: String!
  group: String!
  mobileNumber: String!
  department: String!
  division: String!
}

# Input Update User
input UpdateUser {
  name: String
  email: String
  roleId: String
  group: String
  channel: String
  division: String
  loginTime: DateTime
  department: String
  isActive: Boolean
  reasonToDeactivate: String
  mobileNumber: String
  emailNotif: Boolean
  smsNotif: Boolean
  assignedChannels: [AssignedChannels]!
  cardAssignedChannels: [AssignedChannels]!
  ewalletAssignedChannels: [AssignedChannels]!
  postPaymentConfigChannels: [AssignedChannels]!
  billType: BillTypeOption!
}

input AssignedChannels {
  channelId: String
  name: String
}

# Delete User Input
input DeleteUser {
  reasonToDelete: String!
}

input DeactivateUser {
  reasonToDeactivate: String!
}

input LastKey {
  S: String!
}

input UserPrimary {
  id: String!
}

input Ids {
  ids: [String]
}

input DateRange {
  start: String
  end: String
}

input UsersFilter {
  email: String
  name: String
  isActive: Boolean
  roleId: [String]
  group: String
  createdAt: [DateRange]
}

input UserSummary {
  date: [DateRange]
}

`;
