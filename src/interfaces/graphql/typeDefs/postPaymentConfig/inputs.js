module.exports = `
input UpdatePostPaymentConfigInput {
  billRealTime: String!
  billFallout: Int!
  nonBill: Int!
}

input UpdatePostPaymentConfigWhere {
  channelId: String!
  compositeKey: String!
}

input DeletePostPaymentConfigWhere {
  channelId: String!
  compositeKey: String!
}

input CreatePostPaymentConfigInput {
  channelId: String!
  gatewayProcessor: String!
  paymentMethod: String!
  billRealTime: String!
  billFallout: Int!
  nonBill: Int!
}
  
# Search Input
input PostPaymentConfigInput {
  channelId: String!
}

input PostPaymentConfigMethodsInput {
  channelId: String!
}

input PostPaymentConfigStartKey {
  channelId: String
  compositeKey: String
}

input PostPaymentPagination {
  startKey: PostPaymentConfigStartKey
  limit: Int
}`;
