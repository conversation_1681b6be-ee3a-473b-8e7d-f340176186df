module.exports = `
type PostPaymentConfig { 
  channelId: String
  compositeKey: String
  gatewayProcessor: String
  paymentMethod: String
  billRealTime: String 
  billFallout: Int
  nonBill: Int
}

type PostPaymentConfigMethods {
  paymentMethod: String,
  name: String
}

type PostPaymentConfigGatewayMethods {
  xendit: [PostPaymentConfigMethods]
  adyen: [PostPaymentConfigMethods]
  gcash: [PostPaymentConfigMethods]
}

type PostPaymentConfigLastKey {
  channelId: String
  compositeKey: String
}

type QueryResult {
  filteredResult: [PostPaymentConfig],
  count: Int!,
  lastKey: PostPaymentConfigLastKey,
}

type PostPaymentConfigGatewayMethodsResult {
  result: PostPaymentConfigGatewayMethods
}
`;
