module.exports = `
type ConvenienceFee<PERSON>rand {
  id: String,
  name: String
  createdAt: String
  updatedAt: String
}

input ConvenienceFeeBrandPrimary {
  id: String!
}

type SearchConvenienceFeeBrand {
  cursors: [String]
  count: Int
  filteredData: [ConvenienceFeeBrand]
}

type Query {
  convenienceFeeBrand(where: ConvenienceFeeBrandPrimary!): ConvenienceFeeBrand
  convenienceFeeBrands(filter: SearchConvenienceFeeBrandInput, pagination: PaginationInput!):  SearchConvenienceFee<PERSON>rand
}

type Mutation {
  createConvenienceFeeBrand(data: CreateConvenienceFeeBrandInput!): ConvenienceFeeBrand
  updateConvenienceFeeBrand(data: updateConvenienceFeeBrandInput!, where: ConvenienceFeeBrandPrimary!): Convenience<PERSON>ee<PERSON><PERSON>
  deleteConvenienceFeeBrand(where: ConvenienceFeeBrandPrimary!): ConvenienceFee<PERSON><PERSON>

}

input CreateConvenienceFeeBrandInput{
  name: String!,
}

input updateConvenienceFeeBrandInput{
  name: String!,
}

input SearchConvenienceFeeBrandInput{
  id: String,
  name: String
}`;
