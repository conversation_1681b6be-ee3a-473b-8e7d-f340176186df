module.exports = `

enum TransactionStatus {
  WEB_SESSION_CREATED
  XENDIT_CALLBACK_RECEIVED
  ECPAY_EXPIRED
  ECPAY_GENERATED
  ECPAY_AUTHORISED
  ECPAY_SESSION_CREATED
  INVOICE_URL_CREATED
  INVOICE_URL_GENERATED
  INVOICE_URL_AUTHORISED
  POSTING_FAILED
  POSTED
  POSTED_LUKE
  PAYMENT_AUTHORIZED
  PAYMENT_REFUSED
  CARD_AUTHORISED
  FALLOUT_POSTED
  PAYMENT_POSTING
}

enum TransactionType {
  G # Bill Type
  N # Non-Bill Type
}

enum TransactionPaymentMethod {
  gcash
  card_straight
  card_installment
  otc
}

enum TransactionGatewayProcessor {
  gcash
  xendit
}

# For categorization of transactions 
# A transaction cannot be both
enum TransactionBillType {
  Bill
  NonBill
}

enum BillTypeOption {
  Bill
  NonBill
  Both
}
`;
