module.exports = `
input ConvenienceFeePrimary {
  id: String!
}

input CreateConvenienceFeeInput{
  channelId: String!,
  brand: String,
  gatewayProcessor: String!,
  paymentMethod:  String,
  brands: [String!],
  paymentMethods: [String!],
  transactionType: String!,
  convenienceFeeType: String!,
  convenienceFeeValue: String,
  convenienceFeeThreshold: String,
  convenienceFeeTieredScheme: String,
}

input updateConvenienceFeeInput{
  channelId: String!,
  brand: String!,
  gatewayProcessor: String!,
  paymentMethod:  String!,
  transactionType: String!,
  convenienceFeeType: String,
  convenienceFeeValue: String,
  convenienceFeeThreshold: String,
  convenienceFeeTieredScheme: String,
}

input DeleteConvenienceFeeInput {
  reasonToDelete: String!
}

input SearchConvenienceFeeInput{
  id: String,
  channelId: String,
  name: String,
  brand: String,
  gatewayProcessor: String,
  paymentMethod:  String,
  transactionType: String,
  convenienceFeeType: String,
}`;
