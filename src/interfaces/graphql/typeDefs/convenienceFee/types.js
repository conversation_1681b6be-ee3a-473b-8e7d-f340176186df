module.exports = `
type ConvenienceFee {
  id: String,
  channelId: String,
  name: String,
  brand: String,
  gatewayProcessor: String,
  paymentMethod:  String,
  transactionType: String,
  convenienceFeeType: String,
  convenienceFeeValue: String,
  convenienceFeeThreshold: String,
  convenienceFeeTieredScheme: String,
  createdAt: String,
  updatedAt: String,
}

type BulkConvenienceFeeResponse {
  created: Int!
  entries: [ConvenienceFee!]!
  skipped: Int!
}

type SearchConvenienceFee {
  cursors: [String]
  count: Int
  filteredData: [ConvenienceFee]
}`;
