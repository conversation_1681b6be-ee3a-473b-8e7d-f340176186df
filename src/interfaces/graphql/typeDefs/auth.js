module.exports = `

type Mutation {
  login(data: LoginInput!): LoginCredentials!
  logout(data: LogoutInput!): LogoutCredentials!
}

# Input for create mutation
input LoginInput {
  email: String
  idToken: String
}

input LogoutInput {
  accessToken: String
}

type LoginCredentials {
  token: String
  accessToken: String,
  user: User
}

type LogoutCredentials {
  status: String
}

type Auth {
    email: String 
    id: String
    clientSecret: String
    clientId: String
    roleId: String
  }

type AccessToken {
  accessToken: String
  refreshToken: String
  expiry: String
} 

`;
