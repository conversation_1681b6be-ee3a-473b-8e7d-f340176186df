module.exports = `
  type SettlementBreakdown {
    accountId: String
    accountType: String
    transactionType: String
    amount: Int
    amountValue: Int
    brand: String
    mobileNumber: String
    msisdn: String
    email: String
    status: String
  }

  type TransactionLog {
    paymentId: String
    createDateTime: String
    channelId: String
    customerId: String
    customerName: String
    gatewayProcessor: String
    paymentMethod: String
    status: String
    totalAmount: String
    sessionId: String
    updateDateTime: String
    channelName: String
    transactionType: String
    brand: String
    convenienceFeeAmount: Float
    oona: String
    budgetProtect: String
    settlementBreakdown: [SettlementBreakdown]
    settlement: SettlementBreakdown
    refundId: String
    refundApprovalStatus: String
    refundReason: String
    refundAmount: String
  }

  type SettlementLog {
    paymentId: String
    accountId: String
    accountNumber: String
    amountValue: String
    amount: String
    createDateTime: String
    channelId: String
    customerId: String
    customerName: String
    gatewayProcessor: String
    paymentMethod: TransactionPaymentMethod
    status: TransactionStatus
    totalAmount: String
    settlementBreakdown: [SettlementBreakdown]
    sessionId: String
    transactionExpiry: String
    updateDateTime: String
    channelName: String
    createdAt: String
    mobileNumber: String
    reference: String
    amountCurrency: String
  }
    
    
  type TransactionCount {
    count: Int
  }

  type TransactionLogsSearchOutput implements BaseSearchOutput {
    filteredData: [TransactionLog]
    lastKey: String
    count: Int
  }

   type SettlementLogsSearchOutput implements BaseSearchOutput {
    filteredData: [SettlementLog]
    lastKey: String
    count: Int
  }

  type DownloadAuthorized {
    authorized: Boolean!
  }

  type InstallmentBreakdown {
    interval: String
    count: Int
  }


  type InstallmentReport {
    orderReference: String
    paymentId: String
    createDateTime: String
    itemPurchased: String
    installmentTerm: String
    amountCurrency: String
    totalAmount: Float
    status: String
    creditCardNumber: String
    creditCardBank: String
    creditCardCountry: String
    creditCardType: String
    creditCardHolderName: String
    customerContactNumber: String
    isThreeDFlag: Boolean
    emailAddress: String
    installmentPaymentId: String
    installment: InstallmentBreakdown
  }   

  type InstallmentReportSearchOutput implements BaseSearchOutput {
    filteredData: [InstallmentReport]
    lastKey: String
    count: Int
  }

  type BatchFile { 
    filename: String
    createdAt: DateTime
  }

  type BatchFileSearchOutput implements BaseSearchOutput {
    filteredData: [BatchFile]
    lastKey: String
    count: Int
  }

  type DownloadS3Report {
    urlLink: String
  }

  type XenditDetailedReportSearchOutput implements BaseSearchOutput {
    lastKey: String
    count: Int
    filteredData: [RefundReport]
  }

  type BillingReports {
    # previously reference
    paymentId: String
    # previously amountValue
    totalAmount: String!
    status: String
    # previously paymentMethod
    gatewayProcessor: String
    # previously channelName
    channelId: String
    channelName: String
    settlementBreakdown: [SettlementBreakdown]!
    paymentMethod: String
    createDateTime: String!
    updateDateTime: String!
  }

  type BillingReportsSearchOutput implements BaseSearchOutput {
    lastKey: String
    count: Int
    filteredData: [BillingReports]
  }

  type SearchPayByLinkReport implements BaseSearchOutput{
    lastKey: String
    count: Int
    filteredData: [PayByLinkReport]
  }

  type PayByLinkReport {
    reference: String
    paymentLinkId: String
    createdAt: String
    amountValue: Float
    status: String
    description: String
  }

  type PayByLinkTableReport {
    id: String
    amount: Float
    description: String
    paymentLink: String
    expiredAt: String
    createdAt: String
    merchantAccount: String
    merchantReference: String
    channelId: String
    paymentGateway: String
    status: String
    customer: String
    updatedAt: String
    linkType: String
  }

  type SearchPayByLinkTableReport implements BaseSearchOutput{
    lastKey: String
    count: Int
    filteredData: [PayByLinkTableReport]
  }
`;
