module.exports = `
type Query {
  payByLinkReport(filter: PayByLinkTableReportInput, pagination: PaginationInput!):  SearchPayByLinkTableReport
  reports(filter: SearchTransactionLogsFilter, pagination: PaginationInput!):  TransactionLogsSearchOutput
  transactionLogs(filter: SearchTransactionLogsFilter, pagination: PaginationInput!):  TransactionLogsSearchOutput
  channelTransactionCount(filter: ChannelTransactionCountFilter!): TransactionCount
  installmentReport(filter: SearchInstallmentReportFilter, pagination: PaginationInput!):  InstallmentReportSearchOutput
  batchFileReport(filter: SearchBatchFileFilter!, pagination: PaginationInput!): BatchFileSearchOutput
  xenditRefundDetailedReport(filter: SearchXenditRefundDetailedReport, pagination: PaginationInput!): XenditDetailedReportSearchOutput!
  billingReports(filter: SearchTransactionLogsFilter!, pagination: PaginationInput!): BillingReportsSearchOutput
  failedReports(filter: SearchFailedLogsFilter!, pagination: PaginationInput!): SettlementLogsSearchOutput
  payByLink(filter: payByLinkFilterInput!, pagination: PaginationInput!):  SearchPayByLinkReport
}
`;
