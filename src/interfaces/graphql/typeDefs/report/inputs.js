module.exports = `
  input SearchTransactionLogsFilter {
    paymentId: String
    createDateTime: DateRange
    channelId: String
    customerId: String
    customerName: String
    gatewayProcessor: TransactionGatewayProcessor
    paymentMethod: TransactionPaymentMethod
    sessionId: String
    totalAmount: String
    accountId: String
    status: TransactionStatus
    amount: String
    mobileNumber: String
    msisdn: String
    email: String
    transactionType: TransactionType,
    paymentCode: String
    accountType: String
    brand: String
  }

  input ChannelTransactionCountFilter {
    channelId: String!
    range: DateRange!
  }
    
  input DownloadReportInput {
    type: DownloadReportType
  }

  input SearchInstallmentReportFilter {
    orderReference: String
    # Key for Fast Searching
    paymentId: String
    installmentTerm: Float
    status: TransactionStatus
    installmentPaymentId: String
    createDateTime: DateRange
  }

  input SearchBatchFileFilter {
    filename: String
    createdAt: DateRange
  }

  input DownloadS3Report {
    id: String
    type: DownloadReportType
  }

  input DownloadS3LukeBatchFiles {
    filename: String
  }

  input SearchXenditRefundDetailedReport {
    refundId: String
    reference: String
    channelId: String
    createdAt: DateRange
    billType: String
    hasConvenienceFee: Boolean
  }

  input SearchFailedLogsFilter {
    paymentId: String
    accountNumber: String
    channelId: String
    paymentMethod: TransactionPaymentMethod
    amountValue: Float
    createdAt: DateRange
    mobileNumber: String
  }

  input payByLinkFilterInput {
  # Key for Fast Searching
  reference: String
  paymentLinkId: String
  createdAt: DateRange
  status: TransactionStatus
  }

  input PayByLinkTableReportInput {
  reference: String
  merchantReference: String
  status: PayByLinkStatus
}
`;
