const { graphql } = require('graphql');
const Status = require('http-status');
const userAgentParser = require('express-useragent');
const Schema = require('./schema');

module.exports = async (req, res, next) => {
  const { query, variables } = req.body;
  // TODO: DATALOADER FB
  const handlers = req.container.resolve('handlers');
  const middlewares = req.container.resolve('middlewares');
  const jwt = req.container.resolve('jwt');
  const userAgent = userAgentParser.parse(req.get('user-agent'));
  const logger = req.container.resolve('logger');
  const httpInfo = {
    ipAddress: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
    userAgent: `${userAgent.browser} ${userAgent.version}`,
  };

  const { pswebtoken, psaccesstoken } = req.headers;
  let user = null;
  if (typeof pswebtoken === 'string' && typeof psaccesstoken === 'string') {
    const { data } = jwt.decode(pswebtoken);
    try {
      user = await middlewares.authentication.authenticate(data);
      req.container.resolve('authorization').user = user;
    } catch (error) {
      logger.error({
        messsage: error.message,
        error,
      });
    }
  }

  graphql({
    schema: Schema,
    source: query,
    rootValue: null,
    contextValue: {
      handlers,
      middlewares,
      user,
      httpInfo,
      psaccesstoken,
    },
    variableValues: variables,
  })
    .then((result) => {
      if (result.errors) {
        logger.error(result.errors);
        res.status(Status.INTERNAL_SERVER_ERROR).json({
          type: result.errors[0].name,
          message: result.errors[0].message,
        });
      } else {
        res.status(Status.OK).json(result);
      }
    })
    .catch(() => {
      next();
    });
};
