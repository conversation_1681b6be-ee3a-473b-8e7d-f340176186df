class Application {
  // TODO: Remove comment after DynamoDB provisioning
  // constructor({ server, logger, cron }) {
  constructor({ server, logger }) {
    this.server = server;
    this.logger = logger;
    // TODO: Remove comment after DynamoDB provisioning
    // this.cron = cron;
  }

  start() {
    return this.server.startSLS();
  }

  startExpress() {
    // Disable CRON JOB - Web Tool (Relocate to Payment Service via Lambda)
    // if (process.env.STAGE !== 'undefined' &&
    // (process.env.STAGE === 'prd' || process.env.STAGE === 'test')) {
    //   this.cron.start();
    // }
    return this.server.startExpress();
  }
}

module.exports = Application;
