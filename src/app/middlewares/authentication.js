const Service = require('../Service');
const Handlers = require('../handlers/index');

class Authentication extends Service {
  constructor(container) {
    const { userRepository, roleRepository, base64, logger, Mapper } = container;

    super(userRepository, container);
    this.roleRepository = roleRepository;
    this.base64 = base64;
    this.logger = logger;
    this.mapper = Mapper;
    this.handlers = Handlers;
    this.user = {};
  }

  async authenticate(data) {
    try {
      const userInfo = JSON.parse(data);
      this.logger.info({
        message: 'Authorization Attempt',
        payload: userInfo,
      });
      let user = await this.repository.getById(userInfo.id);
      if (!user) {
        throw new Error('Unauthorized');
      }
      const roles = await this.roleRepository.getById(user.roleId);
      user = this.mapper([user], roles, 'role', 'roleId');
      if (typeof user === 'undefined' || user === null) {
        throw new Error('Unauthorized');
      }
      return user[0];
    } catch (error) {
      this.logger.error({
        message: 'Login Attempt',
        payload: error,
      });
      throw new Error('Authorization Error!');
    }
  }
}

module.exports = Authentication;
