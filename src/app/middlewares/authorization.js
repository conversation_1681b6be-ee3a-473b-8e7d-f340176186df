const Permissions = require('../../domain/acl/useCases/permissions');

class Authorization {
  constructor(container) {
    const { loginVerifier, logger, userRepository } = container;
    this.client = loginVerifier;
    this.permissions = new Permissions();
    this.logger = logger;
    this.userRepository = userRepository;
  }

  async authorize(data) {
    try {
      if (data.action !== 'login' && data.psaccesstoken !== undefined) {
        this.logger.info({
          message: 'PS AccessToken',
          payload: data,
        });
        const user = await this.client.verifyAccessToken(data.user.accessToken, 'api://default');
        this.logger.info({
          message: 'Okta User Token Info',
          payload: user,
        });
      }

      if (!this.permissions.check(data)) {
        throw new Error('Unauthorized Access!');
      }
    } catch (error) {
      this.logger.error({
        message: 'Authorization Error!',
        payload: error,
      });
      throw new Error('Authorization Error!');
    }
  }
}

module.exports = Authorization;