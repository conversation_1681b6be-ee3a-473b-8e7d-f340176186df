const Operation = require('./Operation');
const auditUseCases = require('../domain/audit/useCases');
const transactionLogs = require('../domain/transactions/useCases/transactionsLogs');
const recurringLogs = require('../domain/transactions/useCases/recurringLogs');

class Service extends Operation {
  constructor(
    repository,
    {
      uuid,
      errors,
      logger,
      auditRepository,
      Category,
      userRepository,
      roleRepository,
      notifEsbClient,
      Notification,
      channelRepository,
      dataMask,
      settlementRepository,
    }
  ) {
    super();
    this.repository = repository;
    this.auditRepository = auditRepository;
    this.userRepository = userRepository;
    this.roleRepository = roleRepository;
    this.channelRepository = channelRepository;
    this.settlementRepository = settlementRepository;
    this.uuid = uuid;
    this.errors = errors;
    this.logger = logger;
    this.repoName = this.constructor.name;
    this.category = Category;
    this.notifEsbClient = notifEsbClient;
    this.urlLink = process.env.PAYMENT_GATEWAY_URL;
    this.notification = Notification;
    this.dataMask = dataMask;
  }

  async sms(payload) {
    try {
      const sendSMSNotificationPayload = {
        MSISDN: `63${payload.msisdn}`,
        NotifPatternId: payload.patternId,
        NotifParameters: {
          NotifParameter: payload.parameters,
        },
      };
      this.logger.info({
        message: 'Request to ESB SendSMSNotification',
        payload: JSON.stringify(sendSMSNotificationPayload),
      });

      // execute esb notification
      const smsNotification = await this.notifEsbClient.call('SendSMSNotification', sendSMSNotificationPayload);

      // log esb response
      this.logger.info({
        message: 'Response from ESB SendSMSNotification',
        payload: JSON.stringify(smsNotification),
      });
    } catch (error) {
      this.logger.error({
        message: 'Response from ESB SendSMSNotification Error',
        payload: JSON.stringify(error),
      });
    }
  }

  async email(payload) {
    try {
      const sendEmailNotificationPayload = {
        To: payload.to,
        NotifPatternId: payload.patternId,
        NotifParameterList: {
          NotifParameter: payload.parameters,
        },
      };

      // log esb request body
      this.logger.info({
        message: 'Request to ESB SendEmailNotification',
        payload: JSON.stringify(sendEmailNotificationPayload),
      });

      // execute esb notification
      const emailNotification = await this.notifEsbClient.call('SendEmailNotification', sendEmailNotificationPayload);

      // log esb response
      this.logger.info({
        message: 'Response from ESB SendEmailNotification',
        payload: JSON.stringify(emailNotification),
      });
    } catch (error) {
      this.logger.error({
        message: 'Response from ESB SendEmailNotification Error',
        payload: JSON.stringify(error),
      });
    }
  }

  async auditLogs(payload, currentUser, category) {
    try {
      let userInfo = {};
      const newValue = JSON.stringify(payload?.newValue);
      const oldValue = JSON.stringify(payload?.oldValue);

      if (typeof currentUser !== 'undefined' && currentUser !== null) {
        const user = await this.userRepository.getById(currentUser.id);
        let role = await this.roleRepository.getById(user.roleId);
        if (typeof role === 'undefined') {
          role = {
            name: undefined,
          };
        }
        userInfo = {
          userId: user.id,
          roleId: user.roleId,
          roleName: role[0].name,
          userEmail: user.email,
          userName: user.name,
        };
      }

      const cleanPayload = JSON.parse(JSON.stringify({ ...payload, newValue, oldValue }));
      const date = new Date().toISOString();
      const audit = auditUseCases.create({
        id: this.uuid.create(),
        ...cleanPayload,
        ...userInfo,
        category,
        createdAt: date,
        updatedAt: date,
      });
      await this.auditRepository.add(JSON.parse(JSON.stringify(audit)));
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async list(args) {
    try {
      return await this.repository.getAll(args);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async show(where) {
    try {
      return await this.repository.getById(Object.values(where)[0]);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async create(data, httpInfo, currentUser) {
    try {
      const category = this.category(this.repoName, 'create');
      const created = await this.repository.add({
        ...data,
      });
      await this.auditLogs(
        {
          newValue: data,
          ...httpInfo,
        },
        currentUser,
        category
      );
      return created;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async update(data, id, httpInfo, currentUser, oldVal) {
    try {
      const category = this.category(this.repoName, 'update');
      const updated = await this.repository.update(id, {
        ...data,
        updatedAt: new Date().toISOString(),
      });
      let newData = {};
      if (typeof data.reasonToUpdate !== 'undefined') {
        newData = {
          reasonToUpdate: data.reasonToUpdate,
        };
      }

      const auditData = {
        newValue: data,
        oldValue: oldVal,
        ...newData,
        ...httpInfo,
      };

      await this.auditLogs(auditData, currentUser, category);
      return updated;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async batchPut(data, httpInfo, currentUser, oldVal, newVal) {
    try {
      const category = this.category(this.repoName, 'update');
      const updated = await this.repository.batchPut(data);
      let newData = {};
      if (typeof data.reasonToUpdate !== 'undefined') {
        newData = {
          reasonToUpdate: data.reasonToUpdate,
        };
      }

      await this.auditLogs(
        {
          newValue: newVal,
          oldValue: oldVal,
          ...newData,
          ...httpInfo,
        },
        currentUser,
        category
      );
      return updated;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async delete(data, id, httpInfo, currentUser, oldVal) {
    try {
      // Delete Data
      const deleted = await this.repository.remove(id);
      const category = this.category(this.repoName, 'delete');

      await this.auditLogs(
        {
          oldValue: oldVal,
          ...data,
          ...httpInfo,
        },
        currentUser,
        category
      );
      return deleted;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async batchGet(hashKeys) {
    try {
      return await this.repository.batchGet(hashKeys);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async batchDelete(data, hashKeys, httpInfo, currentUser, items) {
    try {
      const modifiedKeys = hashKeys.map((key) => ({
        id: key,
      }));
      let userInfo = {};
      if (typeof currentUser !== 'undefined' && currentUser !== null) {
        const user = await this.userRepository.getById(currentUser.id);
        let role = await this.roleRepository.getById(user.roleId);
        if (typeof role === 'undefined') {
          role = {
            name: undefined,
          };
        }
        userInfo = {
          userId: user.id,
          roleId: user.roleId,
          roleName: role.name,
          userEmail: user.email,
          userName: user.name,
        };
      }
      const category = this.category(this.repoName, 'delete');
      const date = new Date().toISOString();
      const deleted = await this.repository.batchDelete(modifiedKeys);
      const auditBatchInsert = items.map((item) =>
        auditUseCases.create({
          id: this.uuid.create(),
          oldValue: { ...item },
          ...data,
          ...userInfo,
          ...httpInfo,
          category,
          createdAt: date,
          updatedAt: date,
        })
      );
      await this.auditRepository.batchPut(auditBatchInsert);
      return deleted;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  // Search Data using the Current Repository
  async searchData(data) {
    let filteredData = [];
    this.logger.info({
      filter: JSON.stringify(data.filter),
    });

    if (!data.filter || Object.keys(data.filter).length <= 0) {
      filteredData = await this.repository.listAll(data);
    } else {
      filteredData = await this.repository.searchFilter(data);
    }

    return {
      filteredData,
      count: filteredData.count,
      lastKey: JSON.stringify(filteredData.lastKey),
    };
  }

  async searchDataV2(data) {
    let filteredData = [];
    this.logger.info({
      filter: JSON.stringify(data.filter),
    });

    const transLogFilters = [
      'paymentId', // pk
      'paymentCode',
      'channelId',
      'paymentMethod',
      'totalAmount',
      'gatewayProcessor',
      'createDateTime',
      'sessionId',
      'customerId',
    ];
    const settlementBreakdownFields = [
      'accountId',
      'accountType',
      'mobileNumber',
      'email',
      'emailAddress',
      'transactionType',
      'status',
      'amountValue',
    ];
    let transLogResults = [];
    let settlementResults = [];

    if (!data.filter || Object.keys(data.filter).length <= 0) {
      filteredData = await this.repository.listAll(data);
    } else {
      /**
       * TransLog and Settlemt Filters
       */
      const { translogFilters, settlemntFilters } = Object.keys(data.filter).reduce(
        (acc, key) => {
          if (transLogFilters.includes(key)) {
            acc.translogFilters[key] = data.filter[key];
          }
          if (settlementBreakdownFields.includes(key)) {
            acc.settlemntFilters[key] = data.filter[key];
          }
          return acc;
        },
        { translogFilters: {}, settlemntFilters: {} }
      );

      if (Object.keys(translogFilters).length > 0) {
        transLogResults = await this.repository.searchFilterV2(translogFilters);
        filteredData = transLogResults;
      }

      if (Object.keys(settlemntFilters).length > 0) {
        settlementResults = await this.settlementRepository.searchFilterV2(settlemntFilters);
        /**
         * No transLog results exists
         */
        const paymentIds = [...new Set(settlementResults.map((item) => item.paymentId))];

        const transLog = await Promise.all(
          paymentIds.map(async (paymentId) => {
            return await this.repository.searchFilterV2({ paymentId });
          })
        );
        filteredData = transLog.flat();
        this.logger.info({
          count: filteredData.length,
          transLogFlat: JSON.stringify(filteredData),
        });
      }
      /**
       * Has filteredData but no settlements
       */
      if (filteredData.length > 0 && settlementResults.length <= 0) {
        const paymentIds = [...new Set(filteredData.map((item) => item.paymentId))];
        // Get the settlements for the paymentIds
        const settlementLog = await Promise.all(
          paymentIds.map(async (paymentId) => {
            return await this.settlementRepository.searchFilterV2({ paymentId });
          })
        );
        settlementResults = settlementLog.flat();
      }
      //filteredData = await this.repository.searchFilter(data);
    }

    /**
     * Build final results
     */
    let storage = [];
    if (filteredData.length > 0) {
      filteredData.map((item_translog) => {
        const settlemntBreakdown = item_translog?.settlementBreakdown[0] || {};
        /**
         * Extract Budget protect from settlementBreakdown
         * transactionType: S
         */
        const budgetProtect = item_translog.settlementBreakdown
          .filter((breakdown) => breakdown.transactionType === 'S')
          .map((breakdown) => breakdown.amountValue)
          .reduce((sum, val) => sum + val, 0);
        /**
         * Extract Oona from settlementBreakdown
         * transactionType: O
         */
        const oona = item_translog.settlementBreakdown
          .filter((breakdown) => breakdown.transactionType == 'O')
          .map((breakdown) => breakdown.amountValue)
          .reduce((sum, val) => sum + val, 0);

        if (settlementResults.length <= 0) {
          storage.push({ ...item_translog, ...settlemntBreakdown, budgetProtect, oona });
        } else {
          const item_settlemnt_log = settlementResults.find((t) => t.paymentId === item_translog.paymentId);
          if (item_settlemnt_log) {
            let email = item_settlemnt_log?.email;
            const combined = { ...item_translog, ...item_settlemnt_log, emailAddress: email, budgetProtect, oona };
            storage.push(combined);
          } else {
            storage.push({ ...item_translog, ...settlemntBreakdown, budgetProtect, oona });
          }
        }
      });
    }

    const finalResults = Object.assign(storage, {
      count: storage.length,
      lastKey: storage.lastKey,
    });

    return {
      filteredData: finalResults,
      count: finalResults.count,
      lastKey: JSON.stringify(finalResults.lastKey),
    };
  }

  async searchDataForReport(data) {
    try {
      const { filteredData, count, lastKey } = await this.searchData(data);
      return {
        filteredData,
        count,
        lastKey: JSON.stringify(lastKey),
      };
    } catch (error) {
      this.logger.error('Error in SearchDataForReport: ' + error);
      return {
        lastKey: undefined,
        filteredData: [],
        count: 0,
      };
    }
  }

  // Search Data using the Set Repository
  async searchDataRepository(repository, data, query, value) {
    this.logger.info({
      message: 'Search Data using the set Repository',
      payload: JSON.stringify(data),
    });
    let filteredData;
    if (Object.keys(data.filter).length <= 0) {
      filteredData = await repository.listAllQuery(data, query, value);
    } else {
      filteredData = await repository.searchFilter(data, query, value);
    }
    return {
      filteredData,
      count: filteredData.count,
      lastKey: filteredData.lastKey,
    };
  }

  async postedAndFailedReport(data) {
    let filteredData;
    if (Object.keys(data.filter).length <= 0) {
      filteredData = await this.repository.postedAndFailedList(data);
    } else {
      filteredData = await this.repository.postedAndFailedFilter(data);
    }
    return {
      filteredData,
      count: filteredData.count,
      lastKey: filteredData.lastKey,
    };
  }

  // Search for User, Roles and Provider Management
  async searchDataWithCursors(data) {
    let allIds, filteredData;
    const cursors = [''];
    if (Object.keys(data.filter).length <= 0) {
      filteredData = await this.repository.listAll(data);
      allIds = await this.repository.getAllId();
    } else {
      const searchData = await this.repository.searchFilter(data);
      allIds = await this.repository.searchFilterNoPaginate(data);
      filteredData = [];
      const filteredCount = searchData.count;
      for (let i = 0; i < data.pagination.limit; i += 1) {
        if (
          typeof searchData[i] !== 'undefined' &&
          (data.pagination.limit >= filteredCount || data.pagination.limit <= filteredCount)
        ) {
          filteredData.push(searchData[i]);
        }
      }
    }
    const filterCount = filteredData.count;
    if (filterCount <= 0) {
      return { filteredData: [], count: 0, cursors: [''] };
    }
    for (let i = 1; i < Math.ceil(allIds.count / data.pagination.limit); i += 1) {
      cursors.push(allIds[i * data.pagination.limit - 1].id);
    }
    return {
      filteredData,
      count: allIds.count,
      cursors,
    };
  }

  async updateLoginTime(id) {
    try {
      return await this.repository.update(id, {
        loginTime: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async reportTransformation(transactions, reportType) {
    const results = [];
    let type = reportType;
    if (transactions.count > 0 || type === 'archive') {
      const channels = await this.channelRepository.getChannelForTransaction();
      const getChannelName = (transaction) => {
        const result = channels.filter((channel) => channel.id === transaction.channelId).shift();
        let channelName = transaction.channelId;
        if (result) {
          channelName = result.name;
        }
        return channelName;
      };
      if (type === 'archive') {
        type = 'transactionLogs';
      }
      transactions.forEach((transaction) => {
        if (Object.keys(transaction).length > 0) {
          const channelName = getChannelName(transaction);
          switch (type) {
            case 'transactionLogs':
              results.push(
                transactionLogs({
                  ...transaction,
                  channelName,
                })
              );
              break;
            case 'recurringLogs':
              results.push(
                recurringLogs({
                  ...transaction,
                  channelName,
                })
              );
              break;
            case 'xenditRefundLogs':
              // eslint-disable-next-line no-case-declarations
              let totalPaidAmount = 0;
              if (transaction.isMiscellaneous) {
                //Parse transaction.state to update amountValue
                const state = JSON.parse(transaction.state);
                const { xenditPaymentInfo, paymentInfo, amountValue } = state;
                const payment = paymentInfo || xenditPaymentInfo;
                const { miscellaneous: { budgetProtectValue = 0 } = {} } = payment;
                totalPaidAmount = amountValue + budgetProtectValue;
                state.amountValue = totalPaidAmount;
                //Stringify state for transactionLogs()
                transaction.state = JSON.stringify(state);
              }
              results.push(
                transactionLogs({
                  channelName,
                  ...transaction,
                })
              );
              break;
            default:
              results.push({
                ...transaction,
                channelName,
              });
          }
        }
      });
      return results;
    }
    return results;
  }

  gCashBindReportTransformation(gCashBindData) {
    const results = [];

    if (gCashBindData.count > 0) {
      gCashBindData.forEach((data) => {
        if (Object.keys(data).length > 0) {
          results.push({
            ...data,
            bindingTokenId: this.dataMask.maskMiddleData(data.bindingTokenId ?? '', 8, 4),
            validUntil: data.validity ? new Date(data.validity).toISOString() : '',
          });
        }
      });
    }

    return results;
  }
}

Service.setOutputs(['SUCCESS', 'ERROR', 'VALIDATION_ERROR', 'NOT_FOUND']);

module.exports = Service;
