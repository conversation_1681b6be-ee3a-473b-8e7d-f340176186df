const Service = require('../Service');
const convenienceFeeBrandUseCases = require('../../domain/convenienceFeeBrand/useCases');

class ConvenienceFeeBrand extends Service {
  constructor(container) {
    const { convenienceFeeBrandRepository, uuid } = container;
    super(convenienceFeeBrandRepository, container);
    this.uuid = uuid;
  }

  async show(where) {
    try {
      const data = await this.repository.getById(Object.values(where)[0].id);
      return data[0];
    } catch (error) {
      this.logger.error('Show Convenience Fee Brand', error.message);
      throw error;
    }
  }

  async search(data) {
    try {
      return this.searchDataWithCursors(data);
    } catch (error) {
      this.logger.error('Search Convenience Fee Search', error.message);
      throw error;
    }
  }

  async searchDataWithCursors(data) {
    let allIds, filteredData;
    const cursors = [''];
    if (Object.keys(data.filter).length <= 0) {
      filteredData = await this.repository.listAll(data);
      allIds = await this.repository.getAllId();
    } else {
      const searchData = await this.repository.searchFilter(data);
      allIds = await this.repository.searchFilterNoPaginate(data);
      filteredData = [];
      const filteredCount = searchData.count;
      for (let i = 0; i < data.pagination.limit; i += 1) {
        if (
          typeof searchData[i] !== 'undefined' &&
          (data.pagination.limit >= filteredCount || data.pagination.limit <= filteredCount)
        ) {
          filteredData.push(searchData[i]);
        }
      }
    }
    const filterCount = filteredData.count;
    if (filterCount <= 0) {
      return { filteredData: [], count: 0, cursors: [''] };
    }
    for (let i = 1; i < Math.ceil(allIds.count / data.pagination.limit); i += 1) {
      cursors.push(allIds[i * data.pagination.limit - 1].id);
    }
    return {
      filteredData,
      count: allIds.count,
      cursors,
    };
  }

  async createConvenienceFeeBrand(data, httpInfo, currentUser) {
    try {
      const { name } = data;
      const date = new Date().toISOString();
      const convenienceFeeBrandDetails = convenienceFeeBrandUseCases.create({
        id: this.uuid.create(),
        name,
        createdAt: date,
        updatedAt: date,
      });
      return await this.create(convenienceFeeBrandDetails, httpInfo, currentUser);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async updateConvenienceFeeBrand(data, id, httpInfo, currentUser) {
    try {
      const { name } = data;
      const convenienceFeeDetailsUpdated = convenienceFeeBrandUseCases.update({
        id: id.id,
        name,
      });
      return await this.update(convenienceFeeDetailsUpdated, id, httpInfo, currentUser, data);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async deleteConvenienceFeeBrand(id, httpInfo, currentUser) {
    try {
      const convenienceFeeBrand = await this.repository.getById(id.id);
      if (!convenienceFeeBrand.count) {
        throw this.errors.notFound();
      }
      return this.delete({}, id, httpInfo, currentUser, convenienceFeeBrand[0]);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}

module.exports = ConvenienceFeeBrand;
