const microtime = require('microtime');
const payByLinkUseCases = require('../../domain/payByLink/useCases');
const { getPhDateTime } = require('../../infra/utils/date');
const { generateSessionId } = require('../../infra/utils/sessionIdGenerator');

class PayByLink {
  constructor(container) {
    const { logger, uuid, channelRepository, payByLinkRepository, transactionRepository, xenditService } = container;
    this.logger = logger;
    this.uuid = uuid;
    this.channelRepository = channelRepository;
    this.payByLinkRepository = payByLinkRepository;
    this.transactionRepository = transactionRepository;
    this.xenditService = xenditService;
  }

  async createPayByLink(data) {
    try {
      this.logger.info({
        message: 'Pay by link params',
        params: JSON.stringify(data),
      });

      const { channelId, amountValue, description, emailAddress, mobileNumber, notificationPreference } =
        payByLinkUseCases.create({ ...data });

      const channel = await this.channelRepository.getVerifiedPayByLinkChannelById(channelId);

      if (!channel) {
        throw new Error('Channel not found.');
      }

      if (!channel.channelCode) {
        throw new Error('Channel code not found.');
      }

      const paymentId = channel.channelCode + microtime.now().toString();
      const payByLinkPaymentMethods = (process.env.PAYBYLINK_PAYMENT_METHODS || '')
        .split(',')
        .map((item) => item.trim())
        .filter(Boolean);

      const payload = {
        external_id: paymentId,
        amount: amountValue,
        description: description,
        invoice_duration: process.env.PAYBYLINK_INVOICE_DURATION, // 24 hrs
        customer: {
          email: emailAddress,
          mobile_number: mobileNumber,
        },
        customer_notification_preference: {
          invoice_created: notificationPreference,
        },
        payment_methods: payByLinkPaymentMethods,
      };

      this.logger.info({
        message: 'Pay by link Xendit payload',
        params: JSON.stringify(payload),
      });

      const response = await this.xenditService.createInvoice(payload);

      await this.payByLinkRepository.createPayByLinkReportEntry({
        paymentId,
        channelId,
        paymentGateway: 'xendit',
        paymentLink: response.invoice_url,
        status: response.status,
        merchantAccount: response.merchant_name,
        merchantReference: response.id,
        amount: response.amount,
        description: response.description,
        linkType: '', // No value provided since T1
      });

      await this.transactionRepository.createPayByLinkTransaction({
        paymentId,
        customerId: `PBL-${this.uuid.create()}`,
        sessionId: generateSessionId(),
        createDateTime: getPhDateTime(),
        channelId,
        status: response.status,
        gatewayProcessor: 'xendit',
        paymentMethod: 'paybylink',
        totalAmount: response.amount,
      });

      return {
        externalId: response.external_id,
        paymentGateway: 'xendit',
        paymentLink: response.invoice_url,
        status: response.status,
        merchantAccount: response.merchant_name,
        merchantReference: response.id,
        amount: response.amount,
        description: response.description,
        linkType: '',
      };
    } catch (error) {
      throw new Error(error);
    }
  }
}

module.exports = PayByLink;
