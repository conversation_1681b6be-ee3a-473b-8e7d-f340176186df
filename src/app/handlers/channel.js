const Service = require('../Service');
const bcrypt = require('bcrypt');
const channelUseCases = require('../../domain/channels/useCases');
const auditUseCases = require('../../domain/audit/useCases');

class Channel extends Service {
  constructor(container) {
    const { channelRepository, authClient, auditRepository, userRepository, configRepository, config, randomString } =
      container;
    super(channelRepository, container);
    this.authClient = authClient;
    this.auditRepository = auditRepository;
    this.repoName = this.constructor.name;
    this.userRepository = userRepository;
    this.configRepository = configRepository;
    this.config = config;
    this.randomString = randomString;
  }

  async createChannel(data, httpInfo, currentUser) {
    let channel;
    const id = this.uuid.create();
    const { channelCode } = data;

    const clientSecretLength = process.env.CLIENT_SECRET_LENGTH || 24;
    const clientId = this.uuid.create();
    const clientSecret = this.randomString.generateRandomString(clientSecretLength);

    const checkChannelCode = await this.repository.getByChannelCode(channelCode);
    if (checkChannelCode.count > 0) {
      throw this.errors.channelCodeError();
    }

    try {
      const saltRounds = process.env.BCRYPT_SALT_ROUNDS || 10;
      const hashedClientSecret = await bcrypt.hash(clientSecret, saltRounds);

      // timestamps in UTC
      const timestamp = new Date().toISOString();
      channel = channelUseCases.create({
        id,
        ...data,
        clientId,
        clientSecret: hashedClientSecret,
        createdDateTime: timestamp,
        updatedDateTime: timestamp,
      });

      // TODO: Uncomment this when email notification is ready
      // const emailPatternId = this.config.notifPatternId.emails[this.repoName].create;
      // this.email({
      //   to: email,
      //   patternId: emailPatternId,
      //   parameters: [
      //     {
      //       Name: 'CHANNEL_NAME',
      //       Value: name,
      //     },
      //     {
      //       Name: 'EMAIL',
      //       Value: email,
      //     },
      //   ],
      // });

      const addChannel = await this.create(channel, httpInfo, currentUser);

      // return plaintext clientSecret for user's last view
      return {
        ...addChannel,
        clientSecret,
      };
    } catch (error) {
      this.logger.error(error);
      if (error.message === 'RegistrationValidationError') {
        this.logger.error(error.details);
        throw this.errors.channelDataValidationError();
      }
      throw error;
    }
  }

  async channelsLoose(args) {
    try {
      return await this.repository.getVerified(args);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async payByLinkChannels() {
    try {
      return await this.repository.getVerifiedPayByLinkChannels();
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async updateChannel(data, id, httpInfo, currentUser) {
    try {
      const newData = { ...data };

      // Merchant Service Type
      const channelSubMerchants = newData.subMerchants ?? [];

      channelSubMerchants.forEach((item) => {
        if (item.merchant === 'ECPay' && item.serviceType) {
          newData.ecpayServiceType = item.serviceType;
        } else if (item.merchant === 'Globe' && item.serviceType) {
          newData.serviceType = item.serviceType;
        }
      });

      // Validation of Unique Email, ChannelId, ChannelCode, and GCredit SubMerchant
      const channelPayload = channelUseCases.update(newData);
      const currentData = await this.repository.getById(id);
      if (typeof currentData === 'undefined' || currentData.count < 1) {
        throw this.errors.notFound();
      } else if (typeof channelPayload.email !== 'undefined') {
        // Validate if Email Already Exist
        const checkUser = await this.repository.getByEmail(channelPayload.email);
        if (checkUser.count > 0) {
          throw this.errors.channelCreationError();
        }
      } else if (typeof channelPayload.channelId !== 'undefined') {
        // Validate if Channel ID Already Exist
        const checkChannelId = await this.repository.getByChannelId(channelPayload.channelId);
        if (checkChannelId.count > 0) {
          throw this.errors.channelIdError();
        }
      } else if (typeof channelPayload.channelCode !== 'undefined') {
        // Validate if Channel Code Already Exist
        const checkChannelCode = await this.repository.getByChannelCode(channelPayload.channelCode);
        if (checkChannelCode.count > 0) {
          throw this.errors.channelCodeError();
        }
      } else if (typeof channelPayload.gcreditSubMerchantId !== 'undefined') {
        // Validate if Channel Code Already Exist
        const checkGcreditSubMerchantId = await this.repository.getByGcreditSubMerchantId(
          channelPayload.gcreditSubMerchantId
        );
        if (checkGcreditSubMerchantId.count > 0) {
          throw this.errors.channelGcreditSubMerchant();
        }
      }
      // CarHolderName and CallBackEncrypted Data Transformation
      if (typeof channelPayload.enableCardHolderName !== 'undefined') {
        if (channelPayload.enableCardHolderName === false) {
          channelPayload.cardHolderNameType = 'OPTIONAL';
        }
      }
      if (typeof channelPayload.isCallbackEncrypted !== 'undefined') {
        let callbackPass = null;
        if (typeof currentData.callbackPassPhrase === 'undefined' || currentData.callbackPassPhrase === null) {
          if (channelPayload.isCallbackEncrypted === true) {
            callbackPass = this.uuid.callBackPass();
          }
          channelPayload.callbackPassPhrase = callbackPass;
        }
      }
      // Update Data & Saving to Audit Logs
      const updated = await this.update(
        {
          ...channelPayload,
        },
        id,
        httpInfo,
        currentUser,
        currentData
      );
      // Email Notification via Raven
      // TODO: Uncomment this when email notification is ready
      // const emailPatternId = this.config.notifPatternId.emails[this.repoName].update;
      // this.email({
      //   to: currentData.email,
      //   patternId: emailPatternId,
      //   parameters: [
      //     {
      //       Name: 'CHANNEL_NAME',
      //       Value: currentData.name,
      //     },
      //     {
      //       Name: 'EMAIL',
      //       Value: currentData.email,
      //     },
      //   ],
      // });
      // Handles SubMerchants if Undefined
      if (typeof newData.subMerchants !== 'undefined') {
        updated.subMerchants = newData.subMerchants;
      }
      return updated;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async deleteChannel(data, { id }, httpInfo, currentUser) {
    try {
      const channel = await this.repository.getById(id);
      // const { clientId, clientSecret } = channel;
      if (typeof channel === 'undefined' || channel.count < 1) {
        throw this.errors.notFound();
      }
      const deleted = await this.delete(data, id, httpInfo, currentUser, channel);

      // TODO: Uncomment this when email notification is ready
      // const emailPatternId = this.config.notifPatternId.emails[this.repoName].delete;
      // this.email({
      //   to: channel.email,
      //   patternId: emailPatternId,
      //   parameters: [
      //     {
      //       Name: 'CHANNEL_NAME',
      //       Value: channel.name,
      //     },
      //     {
      //       Name: 'EMAIL',
      //       Value: channel.email,
      //     },
      //   ],
      // });

      return deleted;
      // }
      // throw JSON.stringify(authResponse.message);
    } catch (error) {
      this.logger.error(error);
      if (typeof error.message !== 'undefined') {
        throw error.message;
      }
      throw error;
    }
  }

  async deleteChannels(data, hashKeys, httpInfo, currentUser) {
    try {
      const channels = await this.batchGet(hashKeys.ids);

      // await Promise.all(
      //   channels.map(async (channel) => {
      //     const { clientId, clientSecret } = channel;
      //     await this.authClient.deleteUser({ clientId, clientSecret }, this.config);
      //   })
      // );

      let userInfo = {};
      if (typeof currentUser !== 'undefined' && currentUser !== null) {
        const user = await this.userRepository.getById(currentUser.id);
        let role = await this.roleRepository.getById(user.roleId);
        if (typeof role === 'undefined') {
          role = {
            name: undefined,
          };
        }
        userInfo = {
          userId: user.id,
          roleId: user.roleId,
          roleName: role.name,
          userEmail: user.email,
          userName: user.name,
        };
      }
      const modifiedKeys = hashKeys.ids.map((key) => ({
        id: key,
      }));
      const deleted = await this.repository.batchDelete(modifiedKeys);
      const category = this.category(this.repoName, 'delete');
      const date = new Date().toISOString();
      const auditBatchInsert = channels.map((channel) =>
        auditUseCases.create({
          id: this.uuid.create(),
          oldValue: { ...channel },
          ...data,
          ...userInfo,
          ...httpInfo,
          category,
          createdAt: date,
          updatedAt: date,
        })
      );

      // TODO: Uncomment this when email notification is ready
      // Promise.all(
      //   channels.map(async (channel) => {
      //     const emailPatternId = this.config.notifPatternId.emails[this.repoName].delete;

      //     this.email({
      //       to: channel.email,
      //       patternId: emailPatternId,
      //       parameters: [
      //         {
      //           Name: 'CHANNEL_NAME',
      //           Value: channel.name,
      //         },
      //         {
      //           Name: 'EMAIL',
      //           Value: channel.email,
      //         },
      //       ],
      //     });
      //   })
      // );

      await this.auditRepository.batchPut(auditBatchInsert);

      this.logger.info({
        message: 'Batch Channel Delete',
        payload: JSON.stringify(deleted),
      });

      return {
        unprocessedItems: Object.keys(deleted.unprocessedItems).length,
      };
    } catch (error) {
      this.logger.error('Delete Batch Channels', error);
      if (typeof error.message !== 'undefined') {
        throw error.message;
      }
      throw error;
    }
  }

  async search(data) {
    try {
      let results;

      if (!data.filter || Object.keys(data.filter).length <= 0) {
        results = await this.repository.getAllData(data);
      } else {
        results = await this.repository.searchFilter(data);
      }

      // Object.keys(results).forEach((key) => {
      //   if (!Number.isNaN(Number(key))) {
      //     if (results[key].isVerified === false) {
      //       results[key].clientId = null;
      //       results[key].clientSecret = null;
      //     }
      //   }
      // });
      return {
        count: results.count,
        filteredData: results,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async show(data) {
    const { filter } = data;
    const { id } = filter;
    try {
      const subMerchants = [];
      const data = await this.repository.getById(id);
      if (data.isVerified === false) {
        data.clientId = null;
        data.clientSecret = null;
      }
      if (data.serviceType !== undefined && data.serviceType !== null) {
        subMerchants.push({
          merchant: 'Globe',
          serviceType: data.serviceType,
        });
      }
      if (data.ecpayServiceType !== undefined && data.ecpayServiceType !== null) {
        subMerchants.push({
          merchant: 'ECPay',
          serviceType: data.ecpayServiceType,
        });
      }
      data.subMerchants = subMerchants;
      return data;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async verify(data) {
    try {
      // TODO: Remove this when okta verification is ready
      if (data.confirmationCode !== '000000') {
        throw this.errors.verificationError();
      }

      await this.repository.update(data.channelId, {
        isVerified: true,
      });

      // TODO: Add email notification from Raven before returning
      return {
        verified: true,
      };
    } catch (error) {
      this.logger.error('Channel Verification', error);
      throw this.errors.verificationError();
    }
  }

  async availableSubMerchant(data) {
    try {
      return await this.configRepository.getSubMerchants(data.merchant);
    } catch (error) {
      this.logger.error('Sub Merchant Error: ', error);
      throw error;
    }
  }

  async checkChannelSubMerchant(data) {
    try {
      const checkGlobeSubMerchant = await this.repository.getByGlobeSubMerchant(data.id);
      const checkECPaySubMerchant = await this.repository.getByECPaySubMerchants(data.id);

      if (
        (checkGlobeSubMerchant.count === 0 || typeof checkGlobeSubMerchant === 'undefined') &&
        (checkECPaySubMerchant.count === 0 || typeof checkECPaySubMerchant === 'undefined')
      ) {
        return {
          exist: false,
        };
      }
      return {
        exist: true,
      };
    } catch (error) {
      this.logger.error('CheckGlobeChannelSubMerchant', error);
      throw error;
    }
  }

  async channelsBillType(data) {
    try {
      if (data.billType === 'Both') {
        return await this.repository.getAllData(data.billType);
      }
      return await this.repository.getChannelByBillType(data.billType);
    } catch (error) {
      this.logger.error({
        message: 'Get Channel By Bill Type Error!',
        payload: JSON.stringify(error),
      });
      throw error;
    }
  }
}

module.exports = Channel;
