const Service = require('../Service');
const midUseCases = require('../../domain/mid/useCases');

class Mid extends Service {
  constructor(container) {
    const { midRepository, uuid } = container;
    super(midRepository, container);
    this.uuid = uuid;
  }

  static async adaInstallmentDuplicationValidation(data, type) {
    const duplicationHandler = [];
    switch (type) {
      case 'AutoDebit':
        await Promise.all(
          data.map(async (item) => {
            const { enrollmentType } = item;
            if (!duplicationHandler.includes(enrollmentType)) {
              duplicationHandler.push(enrollmentType);
            } else {
              const error = new Error('AutoDebit Duplication Error!');
              error.message = [
                {
                  message: 'AutoDebit Duplication Error!',
                  data: item,
                },
              ];
              throw error;
            }
          })
        );
        break;
      case 'Installment':
        await Promise.all(
          data.map(async (item) => {
            const key = `${item.bank}-${item.bankTerm}`;
            if (!duplicationHandler.includes(key)) {
              duplicationHandler.push(key);
            } else {
              const error = new Error('Installment Duplication Error!');
              error.message = [
                {
                  message: 'Installment Duplication Error!',
                  data: item,
                },
              ];
              throw error;
            }
          })
        );
        break;
      default:
        throw new Error('Payment Type Not Found');
    }
  }

  async createMid(data, httpInfo, currentUser) {
    try {
      // Validate data
      const merchantIdDetails = midUseCases.create({
        id: this.uuid.create(),
        ...data,
      });
      const {
        name,
        paymentType,
        depositoryBankName,
        depositoryBankAccount,
        company,
        billType,
        channelId,
        bankDiscount,
        withholdingTax,
        costCenter,
        businessUnit,
        ada,
        installments,
      } = merchantIdDetails;
      this.logger.info({
        message: 'Create MID - Merchant Details',
        payload: JSON.stringify(merchantIdDetails),
      });
      let checkUnique;
      const created = [];
      switch (billType) {
        case 'Bill':
          if (channelId) {
            checkUnique = await this.repository.getMIdUniquessGlobal(merchantIdDetails);
          } else {
            checkUnique = await this.repository.getMIdUniquessBillWithOutChannel(merchantIdDetails);
          }
          if (checkUnique.count > 0) {
            throw this.errors.mIdBillUniqueness();
          }
          if (paymentType === 'AutoDebit') {
            merchantIdDetails.installments = '';
            if (ada) {
              await Mid.adaInstallmentDuplicationValidation(ada, paymentType);
              await Promise.all(
                ada.map(async (adaDetail) => {
                  const createAda = {
                    id: this.uuid.create(),
                    enrollmentType: adaDetail.enrollmentType,
                    merchantId: adaDetail.adaMid,
                    name,
                    paymentType,
                    depositoryBankName,
                    depositoryBankAccount,
                    company,
                    billType,
                    channelId,
                    bankDiscount,
                    withholdingTax,
                    costCenter,
                    businessUnit,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                  };
                  await this.logger.info({
                    message: 'ADA Details - ADA Details',
                    payload: JSON.stringify(createAda),
                  });
                  created.push(await this.create(createAda, httpInfo, currentUser));
                })
              );
            }
            return created;
          }
          created.push(await this.create(merchantIdDetails, httpInfo, currentUser));
          break;
        case 'NonBill':
          if (company) {
            checkUnique = await this.repository.getMIdUniquessGlobal(merchantIdDetails);
          } else {
            checkUnique = await this.repository.getMIdUniquessNonBillWithOutCompanyOtherId(merchantIdDetails);
          }
          if (checkUnique.count > 0) {
            throw this.errors.mIdNonBillUniqueness();
          }
          if (paymentType === 'Installment') {
            if (installments) {
              await Mid.adaInstallmentDuplicationValidation(installments, paymentType);
              await Promise.all(
                installments.map(async (installment) => {
                  const newInstallment = {
                    ...merchantIdDetails,
                    id: this.uuid.create(),
                    bankTerm: installment.bankTerm,
                    bank: installment.bank,
                    merchantId: installment.bankMid,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                  };
                  this.logger.info({
                    message: 'Installment Details - Installment Details',
                    payload: JSON.stringify(newInstallment),
                  });
                  const result = await this.create(newInstallment, httpInfo, currentUser);
                  created.push(result);
                })
              );
            }
            return created;
          }
          created.push(await this.create(merchantIdDetails, httpInfo, currentUser));
          break;
        default:
          throw new Error('Bill Type Not Found');
      }
      return created;
    } catch (error) {
      this.logger.info({
        message: 'Create MID',
        payload: error,
      });
      throw error;
    }
  }

  async deleteADAInstallment(existingMerchant) {
    await this.repository.batchDelete(existingMerchant);
  }

  async updateMid(data, id, httpInfo, currentUser) {
    try {
      const merchantIdDetails = midUseCases.update({
        id,
        ...data,
      });
      this.logger.info({
        message: 'Update MID - Merchant Details',
        payload: JSON.stringify(merchantIdDetails),
      });
      const { billType, paymentType, company, installments, ada, channelId } = merchantIdDetails;
      const updated = [];
      let adaInstallmentData, isUnique;
      const currentMIdDetails = await this.repository.getById(id);
      if (!currentMIdDetails.count) {
        throw this.errors.mIdNotFound();
      }

      if (channelId && company) {
        adaInstallmentData = await this.repository.getMIdUniquessGlobal(currentMIdDetails[0]);
      } else if (billType === 'Bill' && !channelId) {
        adaInstallmentData = await this.repository.getMIdUniquessBillWithOutChannel(currentMIdDetails[0]);
      } else if (billType === 'NonBill' && !company) {
        adaInstallmentData = await this.repository.getMIdUniquessNonBillWithOutCompany(currentMIdDetails[0]);
      }
      switch (billType) {
        case 'Bill':
          if (paymentType === 'AutoDebit') {
            await Mid.adaInstallmentDuplicationValidation(ada, paymentType);
            this.logger.info({
              message: 'Delete Existing Details',
              payload: JSON.stringify(adaInstallmentData),
            });
            await this.deleteADAInstallment(adaInstallmentData);
            if (ada) {
              await Promise.all(
                ada.map(async (adaDetail) => {
                  const newADA = {
                    id: this.uuid.create(),
                    enrollmentType: adaDetail.enrollmentType,
                    merchantId: adaDetail.adaMid,
                    name: data.name ? data.name : currentMIdDetails[0].name,
                    billType: data.billType,
                    paymentType: data.paymentType,
                    depositoryBankName: data.depositoryBankName
                      ? data.depositoryBankName
                      : currentMIdDetails[0].depositoryBankName,
                    depositoryBankAccount: data.depositoryBankAccount
                      ? data.depositoryBankAccount
                      : currentMIdDetails[0].depositoryBankAccount,
                    bankDiscount: data.bankDiscount ? data.bankDiscount : currentMIdDetails[0].bankDiscount,
                    withholdingTax: data.withholdingTax ? data.withholdingTax : currentMIdDetails[0].withholdingTax,
                    costCenter: data.costCenter ? data.costCenter : currentMIdDetails[0].costCenter,
                    businessUnit: data.businessUnit ? data.businessUnit : currentMIdDetails[0].businessUnit,
                    company: data.company ? data.company : currentMIdDetails[0].company,
                    channelId: data.channelId ? data.channelId : currentMIdDetails[0].channelId,
                    previousMerchantId: data.previousMerchantId
                      ? data.previousMerchantId
                      : currentMIdDetails[0].previousMerchantId,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                  };
                  this.logger.info({
                    message: 'ADA Details - ADA Details',
                    payload: JSON.stringify(newADA),
                  });
                  updated.push(await this.create(newADA, httpInfo, currentUser));
                })
              );
            }
          } else {
            // Straight Bills Merchant Id
            if (channelId && currentMIdDetails[0].paymentType === 'Straight') {
              isUnique = await this.repository.getMIdUniquessGlobalOtherId(merchantIdDetails, id);
            } else if (currentMIdDetails[0].paymentType === 'Straight') {
              isUnique = await this.repository.getMIdUniqBillWOChannelId(merchantIdDetails, id);
            }
            if (isUnique.count > 0) {
              throw this.errors.mIdBillUniqueness();
            }
            if (
              (currentMIdDetails[0].billType === billType && currentMIdDetails[0].paymentType === paymentType) ||
              (currentMIdDetails[0].billType === 'NonBill' && currentMIdDetails[0].paymentType === paymentType)
            ) {
              const oldValue = await this.repository.getById(id);
              // Update Data
              updated.push(await this.update(merchantIdDetails, id, httpInfo, currentUser, oldValue[0]));
            } else {
              const billStraightDetails = {
                id: this.uuid.create(),
                ...merchantIdDetails,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                name: data.name ? data.name : currentMIdDetails[0].name,
                depositoryBankName: data.depositoryBankName
                  ? data.depositoryBankName
                  : currentMIdDetails[0].depositoryBankName,
                depositoryBankAccount: data.depositoryBankAccount
                  ? data.depositoryBankAccount
                  : currentMIdDetails[0].depositoryBankAccount,
                bankDiscount: data.bankDiscount ? data.bankDiscount : currentMIdDetails[0].bankDiscount,
                withholdingTax: data.withholdingTax ? data.withholdingTax : currentMIdDetails[0].withholdingTax,
                costCenter: data.costCenter ? data.costCenter : currentMIdDetails[0].costCenter,
                businessUnit: data.businessUnit ? data.businessUnit : currentMIdDetails[0].businessUnit,
                channelId: data.channelId ? data.channelId : currentMIdDetails[0].channelId,
                previousMerchantId: data.previousMerchantId
                  ? data.previousMerchantId
                  : currentMIdDetails[0].previousMerchantId,
              };
              this.logger.info({
                message: 'Bill - Straight Details',
                payload: JSON.stringify(billStraightDetails),
              });
              updated.push(await this.create(billStraightDetails, httpInfo, currentUser));
            }
            if (currentMIdDetails[0].paymentType !== 'Straight') {
              this.logger.info({
                message: 'Delete ADA Details',
                payload: JSON.stringify(adaInstallmentData),
              });
              await this.deleteADAInstallment(adaInstallmentData);
            }
          }
          break;
        case 'NonBill':
          if (paymentType === 'Installment') {
            if (installments) {
              await Mid.adaInstallmentDuplicationValidation(installments, paymentType);
              this.logger.info({
                message: 'Delete Existing Details',
                payload: JSON.stringify(adaInstallmentData),
              });
              await this.deleteADAInstallment(adaInstallmentData);
              await Promise.all(
                installments.map(async (installment) => {
                  const newInstallment = {
                    id: this.uuid.create(),
                    bankTerm: installment.bankTerm,
                    bank: installment.bank,
                    merchantId: installment.bankMid,
                    name: data.name ? data.name : currentMIdDetails[0].name,
                    billType: data.billType,
                    paymentType: data.paymentType,
                    depositoryBankName: data.depositoryBankName
                      ? data.depositoryBankName
                      : currentMIdDetails[0].depositoryBankName,
                    depositoryBankAccount: data.depositoryBankAccount
                      ? data.depositoryBankAccount
                      : currentMIdDetails[0].depositoryBankAccount,
                    bankDiscount: data.bankDiscount ? data.bankDiscount : currentMIdDetails[0].bankDiscount,
                    withholdingTax: data.withholdingTax ? data.withholdingTax : currentMIdDetails[0].withholdingTax,
                    costCenter: data.costCenter ? data.costCenter : currentMIdDetails[0].costCenter,
                    businessUnit: data.businessUnit ? data.businessUnit : currentMIdDetails[0].businessUnit,
                    company: data.company ? data.company : currentMIdDetails[0].company,
                    channelId: data.channelId ? data.channelId : currentMIdDetails[0].channelId,
                    previousMerchantId: data.previousMerchantId
                      ? data.previousMerchantId
                      : currentMIdDetails[0].previousMerchantId,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                  };
                  this.logger.info({
                    message: 'Installment Details - Installment Details',
                    payload: JSON.stringify(newInstallment),
                  });
                  updated.push(await this.create(newInstallment, httpInfo, currentUser));
                })
              );
            }
          } else {
            // Straight Non-Bill Merchant Id
            if (company) {
              isUnique = await this.repository.getMIdUpdateUniquessGlobal(merchantIdDetails);
            } else {
              isUnique = await this.repository.getMIdUpdateUniquessNonBillWithOutCompanyOtherId(merchantIdDetails);
            }
            if (isUnique.count > 0) {
              throw this.errors.mIdNonBillUniqueness();
            }
            if (
              (currentMIdDetails[0].billType === billType &&
                currentMIdDetails[0].paymentType === paymentType &&
                currentMIdDetails[0].channelId === channelId) ||
              (currentMIdDetails[0].billType === 'Bill' && currentMIdDetails[0].paymentType === paymentType)
            ) {
              const oldValue = await this.repository.getById(id);
              // Update Data
              updated.push(await this.update(merchantIdDetails, id, httpInfo, currentUser, oldValue[0]));
            } else {
              const nonBillStraightMid = {
                ...merchantIdDetails,
                id: this.uuid.create(),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                name: data.name ? data.name : currentMIdDetails[0].name,
                depositoryBankName: data.depositoryBankName
                  ? data.depositoryBankName
                  : currentMIdDetails[0].depositoryBankName,
                depositoryBankAccount: data.depositoryBankAccount
                  ? data.depositoryBankAccount
                  : currentMIdDetails[0].depositoryBankAccount,
                bankDiscount: data.bankDiscount ? data.bankDiscount : currentMIdDetails[0].bankDiscount,
                withholdingTax: data.withholdingTax ? data.withholdingTax : currentMIdDetails[0].withholdingTax,
                costCenter: data.costCenter ? data.costCenter : currentMIdDetails[0].costCenter,
                businessUnit: data.businessUnit ? data.businessUnit : currentMIdDetails[0].businessUnit,
                channelId: data.channelId ? data.channelId : currentMIdDetails[0].channelId,
                previousMerchantId: data.previousMerchantId
                  ? data.previousMerchantId
                  : currentMIdDetails[0].previousMerchantId,
              };
              this.logger.info({
                message: 'NonBill - Straight Details',
                payload: JSON.stringify(nonBillStraightMid),
              });
              updated.push(await this.create(nonBillStraightMid, httpInfo, currentUser));
            }
            if (currentMIdDetails[0].paymentType !== 'Straight') {
              this.logger.info({
                message: 'Delete ADA/Installment Details',
                payload: JSON.stringify(adaInstallmentData),
              });
              await this.deleteADAInstallment(adaInstallmentData);
            }
          }
          break;
        default:
          throw new Error('Bill Type Not Found');
      }
      return updated;
    } catch (error) {
      this.logger.error({
        header: 'Update MID',
        message: error,
      });
      throw error;
    }
  }

  async deleteMid(data, id, httpInfo, currentUser) {
    try {
      const merchantIdDetails = await this.repository.getById(id);
      if (!merchantIdDetails.count) {
        throw this.errors.notFound();
      }
      return await this.delete(data, id, httpInfo, currentUser, merchantIdDetails[0]);
    } catch (error) {
      this.logger.error('Delete MID', error.message);
      throw error;
    }
  }

  async show(id) {
    try {
      const data = await this.repository.getById(id);
      if (data.count < 1) {
        throw this.errors.mIdNotFound();
      }
      const {
        paymentType,
        billType,
        company,
        channelId,
        name,
        depositoryBankName,
        depositoryBankAccount,
        bankDiscount,
        withholdingTax,
        createdAt,
        updatedAt,
        costCenter,
        businessUnit,
      } = data[0];
      let autoDebitData, installmentsData;
      const adaData = [];
      const installmentData = [];
      switch (paymentType) {
        case 'AutoDebit':
          if (channelId) {
            autoDebitData = await this.repository.getMIdUniquessGlobal(data[0]);
          } else {
            autoDebitData = await this.repository.getMIdUniquessBillWithOutChannel(data[0]);
          }
          await Promise.all(
            autoDebitData.map(async (autoDebit) => {
              if (typeof autoDebit === 'object') {
                adaData.push({
                  adaMid: autoDebit.merchantId,
                  enrollmentType: autoDebit.enrollmentType,
                });
              }
            })
          );
          return {
            id,
            paymentType,
            billType,
            company,
            channelId,
            name,
            depositoryBankName,
            depositoryBankAccount,
            bankDiscount,
            withholdingTax,
            createdAt,
            updatedAt,
            costCenter,
            businessUnit,
            ada: adaData,
            installments: installmentData,
          };
        case 'Installment':
          if (company) {
            installmentsData = await this.repository.getMIdUniquessGlobal(data[0]);
          } else {
            installmentsData = await this.repository.getMIdUniquessNonBillWithOutCompany(data[0]);
          }
          await Promise.all(
            installmentsData.map(async (installment) => {
              if (typeof installment === 'object') {
                installmentData.push({
                  bankMid: installment.merchantId,
                  bankTerm: installment.bankTerm,
                  bank: installment.bank,
                });
              }
            })
          );
          return {
            id,
            paymentType,
            billType,
            company,
            channelId,
            name,
            depositoryBankName,
            depositoryBankAccount,
            bankDiscount,
            withholdingTax,
            createdAt,
            updatedAt,
            costCenter,
            businessUnit,
            ada: adaData,
            installments: installmentData,
          };
        default:
      }
      return {
        ...data[0],
        ada: adaData,
        installments: installmentData,
      };
    } catch (error) {
      this.logger.error('Get MID', error.message);
      throw error;
    }
  }

  async search(data) {
    try {
      return await this.searchDataWithCursors(data);
    } catch (error) {
      this.logger.error('Search MID', error.message);
      throw error;
    }
  }
}

module.exports = Mid;
