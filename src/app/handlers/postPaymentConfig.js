const Service = require('../Service');
const postPaymentConfigUseCases = require('../../domain/postPaymentConfig/useCases');

class PostPaymentConfig extends Service {
  constructor(container) {
    const { postPaymentConfigRepository, configRepository, channelRepository } = container;
    super(postPaymentConfigRepository, container);
    this.configRepository = configRepository;
    this.channelRepository = channelRepository;
  }

  async search(data, currentUser) {
    const { filter, pagination } = data;
    const { channelId } = filter;

    if (!currentUser || !currentUser.postPaymentConfigChannels) {
      return {
        filteredResult: [],
        count: 0,
        lastKey: null,
      };
    }

    this.logger.info({
      message: '[INFO] Current User PPC Chanels',
      payload: currentUser.postPaymentConfigChannels,
    });

    const { postPaymentConfigChannels } = currentUser;

    let assignedChannels;
    if (postPaymentConfigChannels) {
      assignedChannels = JSON.parse(postPaymentConfigChannels);
    }

    if (!assignedChannels || assignedChannels.length < 1) {
      return {
        filteredResult: [],
        count: 0,
        lastKey: null,
      };
    }

    if (!assignedChannels.some((channel) => channelId === channel.channelId)) {
      return {
        filteredResult: [],
        count: 0,
        lastKey: null,
      };
    }

    try {
      if (!pagination.limit || pagination.limit <= 0) {
        pagination.limit = 10;
      }

      // for FE knowing the last page
      pagination.limit += 1;

      const result = await this.repository.getByChannelId(channelId, pagination);

      this.logger.info({
        message: '[INFO] Search Result',
        payload: JSON.stringify(result),
      });

      // remove excess results and adjust lastKey
      let lastKey;
      if (result.count === pagination.limit) {
        result.splice(pagination.limit - 1, 1);
        result.count -= 1;

        const lastKeyObj = result[result.count - 1];

        lastKey = {
          channelId: lastKeyObj.channelId,
          compositeKey: lastKeyObj.compositeKey,
        };
      } else {
        lastKey = null;
      }

      return {
        filteredResult: result,
        count: result.count,
        lastKey,
      };
    } catch (error) {
      this.logger.error('[ERROR] Search PostPaymentConfig', error.message);
      throw error;
    }
  }

  async getGatewayMethods(data) {
    const { where } = data;
    const { channelId } = where;

    let channel;
    try {
      channel = await this.channelRepository.getById(channelId);
    } catch (error) {
      this.logger.error('[ERROR] Fetching Channel', error.message);
      throw error;
    }

    this.logger.info({
      message: '[INFO] Fetched Channel',
      payload: JSON.stringify(channel),
    });

    if (!channel) {
      return {
        result: null,
      };
    }

    let gatewayMethods;
    try {
      const gatewayMethodsConfig = await this.configRepository.getByName('PayO-GatewayMethods');

      gatewayMethods = JSON.parse(gatewayMethodsConfig[0].value);
    } catch (error) {
      this.logger.error('[ERROR] Fetching GatewayMethods', error.message);
      throw error;
    }

    if (!gatewayMethods) {
      this.logger.error('[ERROR] Missing PayO-GatewayMethods from Configurations');

      return {
        result: null,
      };
    }

    this.logger.info({
      message: '[INFO] Fetched Gateway Methods',
      payload: JSON.stringify(gatewayMethods),
    });
    // if (!channel.xenditEnabled) {
    //   delete gatewayMethods.xendit;
    // }

    // // Remove `|| true` if GCash methods are to be listed
    // if (!channel.gcashEnabled || true) {
    //   delete gatewayMethods.gcash;
    // }

    // // Remove `|| true` if Adyen methods are to be listed
    // if (!channel.adyenEnabled || true) {
    //   delete gatewayMethods.adyen;
    // }

    this.logger.info({
      message: '[INFO] Gateway Methods',
      payload: JSON.stringify(gatewayMethods),
    });

    return {
      result: gatewayMethods,
    };
  }

  async createPostPaymentConfig(data, httpInfo, currentUser) {
    try {
      if (data && Object.keys(data).length > 0) {
        //Validate Data
        const payload = await postPaymentConfigUseCases.create(data);
        return this.create(payload, httpInfo, currentUser);
      }
    } catch (error) {
      this.logger.error('[ERROR] Create PostPaymentConfig', error.message);
      throw error;
    }
  }

  async updatePostPaymentConfig(data, where, httpInfo, currentUser) {
    try {
      if (data && Object.keys(data).length > 0) {
        const oldValue = await this.repository.getByKeys(where);
        //Validate Data
        const payload = await postPaymentConfigUseCases.update(data);
        return this.update(payload, where, httpInfo, currentUser, oldValue);
      }
      return this.searchData(where);
    } catch (error) {
      this.logger.error('[ERROR] Update PostPaymentConfig', error.message);
      throw error;
    }
  }

  async deletePostPaymentConfig(where, httpInfo, currentUser) {
    try {
      let oldValue;
      if (where && Object.keys(where).length > 0) {
        oldValue = await this.repository.getByKeys(where);
      }

      if (oldValue) {
        //Validate Data
        const id = await postPaymentConfigUseCases.remove(where);
        await this.delete({}, id, httpInfo, currentUser, oldValue);
        return oldValue;
      } else {
        throw new Error('Key not found');
      }
    } catch (error) {
      this.logger.error('[ERROR] Delete PostPaymentConfig', error.message);
      throw error;
    }
  }
}

module.exports = PostPaymentConfig;
