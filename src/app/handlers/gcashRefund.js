const Service = require('../Service');
const refundSearchInput = require('../../domain/transactions/useCases/gcashRefundSearchInput');
const refundApprovalUsecases = require('../../domain/transactions/useCases/gcashRefundRequest');
const refundDetailedUsecases = require('../../domain/transactions/useCases/refundTransTransformation');
const gcashRefundTransactionLogs = require('../../domain/transactions/useCases/gcashRefundTransactionLogs');

class GCashRefund extends Service {
  constructor(container) {
    const { transactionRepository, snapshotRepository, refundRepository, authClient, config, configRepository } =
      container;
    super(refundRepository, container);
    this.transactionRepository = transactionRepository;
    this.snapshotRepository = snapshotRepository;
    this.authClient = authClient;
    this.config = config;
    this.configRepository = configRepository;
  }

  static async refundTransactionTransformation(transactions) {
    if (transactions.count > 0) {
      return transactions.map((transaction) => refundDetailedUsecases(transaction));
    }
    return [];
  }

  async reportTransformation(transactions) {
    if (transactions.count > 0) {
      const { value } = await this.configRepository.getById('refundDaysValidity');
      const channels = await this.channelRepository.getChannelForTransaction();
      return transactions.map((transaction) => {
        // Retrieve Channel Details
        const result = channels.filter((channel) => channel.id === transaction.channelId).shift();
        let channelName = transaction.channelId;
        if (typeof result !== 'undefined') {
          channelName = result.name;
        }
        return gcashRefundTransactionLogs({
          ...transaction,
          channelName,
          refundValidity: value,
        });
      });
    }
    return [];
  }

  // Module/Page
  async refundRequestModule(data, user) {
    try {
      let payload = data;
      const { channelId } = payload.filter;
      const { assignedChannels, billType } = user;
      if (!assignedChannels || !billType) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      const parsedAssignedChannel = JSON.parse(assignedChannels);
      if (parsedAssignedChannel.length < 1) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      const userAssignedChannels = await parsedAssignedChannel.map((channel) => channel.channelId);
      if (!channelId) {
        payload.filter.userAssignedChannels = userAssignedChannels;
      } else if (!userAssignedChannels.includes(channelId)) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      if (user.billType !== 'Both') {
        payload.filter.billType = user.billType;
      }
      // Validate Filter Input
      payload = refundSearchInput(payload);
      this.logger.info({
        message: 'GCash Refund Request Search Payload',
        payload: JSON.stringify(payload),
      });
      const result = await this.transactionRepository.searchFilter(payload);
      const filteredData = await this.reportTransformation(result, 'transactionLogs');
      return {
        filteredData,
        lastKey: JSON.stringify(result.lastKey),
      };
    } catch (error) {
      this.logger.error('Error Search Gcash Refund Data: ', error.message);
      throw error;
    }
  }

  // Module/Page
  async refundApprovalModule(data, user) {
    try {
      let payload = data;
      const { channelId } = payload.filter;
      if (!user.assignedChannels || !user.billType) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      const assignedChannels = JSON.parse(user.assignedChannels);
      if (assignedChannels.length < 1) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      const userAssignedChannels = await assignedChannels.map((channel) => channel.channelId);
      if (!channelId) {
        payload.filter.userAssignedChannels = userAssignedChannels;
      } else if (!userAssignedChannels.includes(channelId)) {
        delete payload.filter.channelId;
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      if (user.billType !== 'Both') {
        payload.filter.billType = user.billType;
      }
      // Validate Filter Input
      payload = refundSearchInput(payload);
      payload.filter.refundApprovalStatus = 'For Approval';
      const filteredData = await this.repository.searchFilter(payload);
      const lastKey = JSON.stringify(filteredData.lastKey);
      const result = await GCashRefund.refundTransactionTransformation(filteredData);
      return {
        filteredData: result,
        lastKey,
      };
    } catch (error) {
      this.logger.error('Error Search Gcash Refund Data: ', error.message);
      throw error;
    }
  }

  // Module/Page
  async refundApprovalModuleHistory(data) {
    try {
      let payload = data;
      // Validate Filter Input
      payload = refundSearchInput(payload);
      payload.filter.refundApprovalStatus = 'Rejected';
      const filteredData = await this.repository.searchFilter(payload);
      const lastKey = JSON.stringify(filteredData.lastKey);
      const result = await GCashRefund.refundTransactionTransformation(filteredData);
      return {
        filteredData: result,
        lastKey,
      };
    } catch (error) {
      this.logger.error('Error Search Gcash Refund Data: ', error.message);
      throw error;
    }
  }

  async requestForRefund(data, httpInfo, currentUser) {
    try {
      const category = this.category(this.repoName, 'request');
      const transactions = await this.transactionRepository.getByReference(data.reference);
      const refundValidity = await this.configRepository.getById('refundDaysValidity');
      const { value } = refundValidity;
      if (!value) {
        throw new Error('Refund Valid Days Not Configure in the Database');
      }
      const differeneeInTime = new Date().getTime() - new Date(transactions[0].timestamp).getTime();
      const differenceInDays = differeneeInTime / (1000 * 3600 * 24);
      if (parseInt(differenceInDays.toFixed(2), 10) >= parseInt(value, 10)) {
        throw new Error('Unable to request for refund. The transaction was beyond the processing period');
      }
      const filteredData = await this.reportTransformation(transactions, 'transactionLogs');
      const forApproval = 'For Approval';
      switch (filteredData[0].refundApprovalStatus) {
        case 'For Approval':
          throw new Error('Request for refund already filed');
        case 'Approved':
          throw new Error('Request for refund already approved');
        default:
          filteredData[0].refundApprovalStatus = forApproval;
      }
      if (data.refundAmount > parseFloat(filteredData[0].finalAmount || filteredData[0].amountValue)) {
        throw new Error('Refund Amount is greater than Payment amount');
      }
      const requestTimeStamp = new Date().toISOString();
      const payload = await refundApprovalUsecases({
        ...filteredData[0],
        ...data,
        requestTimeStamp,
      });
      // Added in Request in Refund Approval
      await this.repository.add(payload);
      // Update Transaction Logs
      await this.transactionRepository.update({
        id: transactions[0].id,
        transactionId: transactions[0].transactionId,
        refundApprovalStatus: forApproval,
        refundStatus: forApproval,
        refundAmount: data.refundAmount,
        refundReason: data.refundReason,
      });
      await this.auditLogs(
        {
          ...httpInfo,
          newValue: payload,
        },
        currentUser,
        category
      );
      return {
        approve: true,
        refundApprovalStatus: forApproval,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async gcashRefundDetailedReport(data) {
    try {
      let payload = data;
      // Validate Filter Input
      payload = refundSearchInput(payload);
      payload.filter.refundApprovalStatus = 'Approved';
      const filteredData = await this.repository.searchFilter(payload);
      const lastKey = JSON.stringify(filteredData.lastKey);
      const result = await GCashRefund.refundTransactionTransformation(filteredData);
      return {
        filteredData: result,
        lastKey,
      };
    } catch (error) {
      this.logger.error('Error Search Gcash Refund Detailed Report: ', error.message);
      throw error;
    }
  }

  async gcashRefundSummaryReport(data) {
    try {
      const payload = data.filter;
      const { refundRange, channelId } = payload;
      const refundStats = {};
      const refundApprovedPayloadLoop = async (refundPayload) => {
        if (channelId) {
          const key = channelId;
          if (refundPayload[key]) {
            if (refundStats[key] && refundPayload[key].gcash) {
              refundStats[key].totalApprovedRefundAmount += refundPayload[key].gcash.totalApprovedRefundAmount;
              refundStats[key].totalApprovedRefundCount += refundPayload[key].gcash.totalApprovedRefundCount;
            } else if (refundPayload[key]?.gcash) {
              refundStats[key] = {
                ...refundPayload[key].gcash,
                channelName: refundPayload[key].name,
                totalForApprovalAmount: 0,
                totalForApprovalCount: 0,
                totalAutoRefundAmount: 0,
                totalAutoRefundCount: 0,
              };
            }
          }
        } else {
          const keys = Object.keys(refundPayload);
          keys.forEach((key) => {
            if (refundStats[key]) {
              if (refundPayload[key].gcash) {
                refundStats[key].totalApprovedRefundAmount += refundPayload[key].gcash.totalApprovedRefundAmount;
                refundStats[key].totalApprovedRefundCount += refundPayload[key].gcash.totalApprovedRefundCount;
              }
            } else if (refundPayload[key]) {
              if (refundPayload[key].gcash) {
                refundStats[key] = {
                  ...refundPayload[key].gcash,
                  channelName: refundPayload[key].name,
                  totalForApprovalAmount: 0,
                  totalForApprovalCount: 0,
                  totalAutoRefundAmount: 0,
                  totalAutoRefundCount: 0,
                };
              }
            }
          });
        }
      };
      const refundForApprovalPayloadLoop = async (transaction) => {
        const key = transaction.channelId;
        if (refundStats[key]) {
          refundStats[key].totalForApprovalAmount += parseFloat(transaction.refundAmount);
          refundStats[key].totalForApprovalCount += 1;
        } else {
          refundStats[key] = {
            channelName: transaction.channelName,
            totalForApprovalAmount: parseFloat(transaction.refundAmount),
            totalForApprovalCount: 1,
            totalApprovedRefundAmount: 0,
            totalApprovedRefundCount: 0,
            totalAutoRefundAmount: 0,
            totalAutoRefundCount: 0,
          };
        }
      };
      const refundAutoPayloadLoop = async (refundPayload) => {
        if (payload.channelId) {
          const key = payload.channelId;
          if (refundStats[key]) {
            refundStats[key].totalAutoRefundAmount += refundPayload[key].gcash.totalApprovedRefundAmount;
            refundStats[key].totalAutoRefundCount += refundPayload[key].gcash.totalApprovedRefundCount;
          } else if (refundPayload[key]) {
            const { totalApprovedRefundAmount, totalApprovedRefundCount } = refundPayload[key].gcash;
            refundStats[key] = {
              totalAutoRefundAmount: totalApprovedRefundAmount,
              totalAutoRefundCount: totalApprovedRefundCount,
              channelName: refundPayload[key].name,
              totalApprovedRefundAmount: 0,
              totalApprovedRefundCount: 0,
              totalForApprovalAmount: 0,
              totalForApprovalCount: 0,
            };
          }
        } else {
          const keys = Object.keys(refundPayload);
          keys.forEach((key) => {
            if (refundStats[key] && refundPayload[key].gcash) {
              refundStats[key].totalAutoRefundAmount += refundPayload[key].gcash.totalApprovedRefundAmount;
              refundStats[key].totalAutoRefundCount += refundPayload[key].gcash.totalApprovedRefundCount;
            } else if (refundPayload[key].gcash) {
              refundStats[key] = {
                totalAutoRefundAmount: refundPayload[key].gcash.totalApprovedRefundAmount,
                totalAutoRefundCount: refundPayload[key].gcash.totalApprovedRefundCount,
                channelName: refundPayload[key].name,
                totalApprovedRefundAmount: 0,
                totalApprovedRefundCount: 0,
                totalForApprovalAmount: 0,
                totalForApprovalCount: 0,
              };
            }
          });
        }
      };
      const refundApprovedStats = async (snapshots) => {
        await snapshots.forEach(async (snapshot) => {
          await refundApprovedPayloadLoop(JSON.parse(snapshot.payload));
        });
      };
      const refundForApprovalStats = async (transactions) => {
        await transactions.forEach(async (transaction) => {
          await refundForApprovalPayloadLoop(transaction);
        });
      };
      const refundAutoStats = async (snapshots) => {
        await snapshots.forEach(async (snapshot) => {
          await refundAutoPayloadLoop(JSON.parse(snapshot.payload));
        });
      };

      const refundFATransaction = (filters, startKeys) =>
        this.repository.getFARefundTransaction(filters, startKeys, 'gcash');
      const getRefundForApprovalLoop = async (startKeys) => {
        const transactions = await refundFATransaction(payload, startKeys);
        if (transactions.count > 0) {
          await refundForApprovalStats(transactions);
        }
        if (typeof transactions.lastKey !== 'undefined') {
          await getRefundForApprovalLoop(transactions.lastKey);
        }
      };
      if (refundRange) {
        const refundSnapshot = await this.snapshotRepository.getSnapshotByDateRange(
          'refund',
          refundRange.start,
          refundRange.end
        );
        await refundApprovedStats(refundSnapshot);
        const refundAutoSnapshot = await this.snapshotRepository.getSnapshotByDateRange(
          'autoRefund',
          refundRange.start,
          refundRange.end
        );
        await refundAutoStats(refundAutoSnapshot);
        await getRefundForApprovalLoop('');
      } else {
        const todayDate = new Date();
        const year = todayDate.getFullYear();
        const refundSnapshot = await this.snapshotRepository.getSnapshotByYear('refund', year);
        await refundApprovedStats(refundSnapshot);
        const refundAutoSnapshot = await this.snapshotRepository.getSnapshotByYear('autoRefund', year);
        await refundAutoStats(refundAutoSnapshot);
        await getRefundForApprovalLoop('');
      }
      return Object.values(refundStats);
    } catch (error) {
      this.logger.error('Error Search Gcash Summary Report: ', error.message);
      throw error;
    }
  }

  async refundapproval(data, httpInfo, currentUser) {
    try {
      const refundForApprovalData = await this.repository.getByReferenceForApproval(data.reference);
      const { id, mobileNumber, refundAmount, refundReason, requestTimeStamp, transactionId } =
        refundForApprovalData[0];
      const rejected = 'Rejected';
      let category = this.category(this.repoName, 'approved');
      if (data.action === 'reject') {
        if (mobileNumber) {
          const gcashRejectedPatternId = await this.configRepository.getById('gcashRejectedPatternId');
          const { value } = gcashRejectedPatternId;
          await Promise.all([
            this.sms({
              msisdn: mobileNumber,
              patternId: value,
              parameters: [
                {
                  Name: 'REFNO',
                  Value: id,
                },
              ],
            }),
          ]);
        }
        category = this.category(this.repoName, 'rejected');
        await this.transactionRepository.update({
          id,
          transactionId,
          refundApprovalStatus: rejected,
          refundRejectedTimestamp: new Date().toISOString(),
          refundAmount,
          refundReason,
          refundStatus: rejected,
        });
        await this.repository.update({
          id,
          requestTimeStamp,
          refundApprovalStatus: rejected,
          approverRemarks: data.approverRemarks,
          refundRejectedTimestamp: new Date().toISOString(),
        });
        await this.auditLogs(
          {
            ...httpInfo,
          },
          currentUser,
          category
        );
        return {
          success: true,
          code: 200,
        };
      }

      // PS Call to GCash For Refund
      if (refundForApprovalData.count === 0) {
        throw new Error('No Refund Request to be approved');
      }
      const channel = await this.channelRepository.getById(refundForApprovalData[0].channelId);
      const { clientId, clientSecret } = channel;
      const token = await this.authClient.accessToken({ clientId, clientSecret }, this.config);
      const gcashRefundRequest = {
        paymentId: data.reference,
        transactionId: refundForApprovalData[0].transactionId,
        requestTimeStamp: refundForApprovalData[0].requestTimeStamp,
        mobileNumber: refundForApprovalData[0].mobileNumber,
        refundAmount: refundForApprovalData[0].refundAmount,
        refundReason: refundForApprovalData[0].refundReason,
        acquirementId: refundForApprovalData[0].acquirementId,
      };
      this.logger.info(`GCash Refund Request: ${JSON.stringify(gcashRefundRequest)}`);
      const gcashRefundResponse = await this.authClient.gcashRefund(gcashRefundRequest, this.config, token.accessToken);
      this.logger.info(`GCash Refund Response: ${JSON.stringify(gcashRefundResponse)}`);
      if (gcashRefundResponse.status === 200) {
        return {
          success: true,
          code: gcashRefundResponse.status,
        };
      }
      if (gcashRefundResponse.message === 'Internal server error') {
        throw new Error('Internal server error');
      }
      throw new Error('GCash Refund Error');
    } catch (error) {
      this.logger.error(JSON.stringify(error));
      throw new Error(error.message);
    }
  }
}

module.exports = GCashRefund;
