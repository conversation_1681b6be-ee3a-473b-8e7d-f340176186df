const csv = require('csvtojson');
const Service = require('../Service');
const roleUseCases = require('../../domain/role/useCases');

class Role extends Service {
  constructor(container) {
    const { roleRepository, userRepository, config } = container;
    super(roleRepository, container);
    this.userRepository = userRepository;
    this.config = config;
  }

  static async roleMapping(data, numberOfUsers) {
    return data.map((role) => {
      for (let index = 0; index < numberOfUsers.length; index += 1) {
        if (role.id === numberOfUsers[index].id) {
          const { count } = numberOfUsers[index];
          return Object.assign(role, { numberOfUsers: count.count });
        }
      }
      return [];
    });
  }

  async list(args) {
    try {
      const roles = await this.repository.getAll(args);
      const roleIds = roles.reduce((accumulator, currentValue) => {
        accumulator.push(currentValue.id);
        return accumulator;
      }, []);
      const counts = roleIds.map(async (id) => ({
        count: await this.userRepository.filteredCount({ roleId: id }),
        id,
      }));
      const numberOfUsers = await Promise.all(counts);
      return Role.roleMapping(roles, numberOfUsers);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async rolesLoose(args) {
    try {
      return await this.repository.getAll(args);
    } catch (error) {
      this.logger.error('Role Loose', error);
      throw error;
    }
  }

  async show(where) {
    try {
      const roleId = Object.values(where)[0].id;
      const data = await this.repository.getById(roleId);
      const userCounts = await this.userRepository.filteredCount({ roleId });

      // return Object.assign(data[0], { numberOfUsers: userCounts });
      return Object.assign(data[0], { numberOfUsers: userCounts[0] });
    } catch (error) {
      this.logger.error('Show Roles', error);
      throw error;
    }
  }

  async search(data) {
    try {
      const results = await this.searchDataWithCursors(data);
      results.filteredData = await this.getNumberOfUsers(results.filteredData);
      return results;
    } catch (error) {
      this.logger.error('Search Role', error);
      throw error;
    }
  }

  async getNumberOfUsers(data) {
    const roleIds = data.map((fdata) => fdata.id);

    const counts = roleIds.map(async (id) => ({
      count: await this.userRepository.filteredCount({ roleId: id }),
      id,
    }));

    const numberOfUsers = await Promise.all(counts);
    return Role.roleMapping(data, numberOfUsers);
  }

  async createRole(data, httpInfo, currentUser) {
    try {
      const timestamp = new Date().toISOString();
      // Validate registration
      const role = roleUseCases.create({
        id: this.uuid.create(),
        ...data,
        createdAt: timestamp,
        updatedAt: timestamp,
      });

      const checkCode = await this.repository.getByRoleCode(role.code);
      if (checkCode.count > 0) {
        throw this.errors.roleIdError();
      }

      // Insert Data
      return await this.create(role, httpInfo, currentUser);
    } catch (error) {
      this.logger.error('Create Role', error);
      throw error;
    }
  }

  async updateRole(data, id, httpInfo, currentUser) {
    try {
      const rolePayload = roleUseCases.update(data);
      const oldValue = await this.repository.getById(id.id);

      if (typeof data.code !== 'undefined') {
        const checkCode = await this.repository.getByRoleCode(data.code);
        if (checkCode.count > 0) {
          throw this.errors.roleIdError();
        }
      }
      // Update Data
      const updated = await this.update(
        {
          ...rolePayload,
        },
        id,
        httpInfo,
        currentUser,
        oldValue[0]
      );
      const roles = await this.userRepository.filterRole({ roleId: id.id });
      Object.keys(roles).forEach((key) => {
        if (!Number.isNaN(Number(key))) {
          if (updated.emailNotif === false) {
            const emailPatternId = this.config.notifPatternId.emails[this.repoName].update;
            this.email({
              to: roles[key].email,
              patternId: emailPatternId,
              parameters: [
                {
                  Name: 'NAME',
                  Value: roles[key].name,
                },
                {
                  Name: 'ROLE_NAME',
                  Value: updated.name,
                },
              ],
            });
          }
          if (updated.smsNotif === false) {
            const smsPatternId = this.config.notifPatternId.sms[this.repoName].update;
            this.sms({
              msisdn: roles[key].mobileNumber,
              patternId: smsPatternId,
              parameters: [
                {
                  Name: 'NAME',
                  Value: roles[key].name,
                },
                {
                  Name: 'ROLE_NAME',
                  Value: updated.name,
                },
                {
                  Name: 'URL_LINK',
                  Value: this.urlLink,
                },
              ],
            });
          }
        }
      });
      return updated;
    } catch (error) {
      this.logger.error('Update Role', error);
      throw error;
    }
  }

  async deleteRole(data, id, httpInfo, currentUser) {
    try {
      const role = await this.repository.getById(Object.values(id)[0]);
      if (typeof role === 'undefined' || role.count < 1) {
        throw this.errors.notFound();
      }

      const numberOfUsers = await this.userRepository.filteredCount({ roleId: Object.values(id)[0] });
      if (numberOfUsers > 0) {
        throw this.errors.deletionError();
      }

      return await this.delete(data, id, httpInfo, currentUser, role[0]);
    } catch (error) {
      this.logger.error('Delete Role', error);
      throw error;
    }
  }

  async deleteRoles(data, ids, httpInfo, currentUser) {
    try {
      const roles = await this.batchGet(ids.ids);
      if (roles === undefined || roles.length === 0) {
        throw this.errors.notFound();
      }
      await Promise.all(
        roles.map(async (role) => {
          const numberOfUsers = await this.userRepository.filteredCount({ roleId: role.id });
          if (numberOfUsers > 0) {
            throw this.errors.deletionError();
          }
        })
      );
      const deleted = await this.batchDelete(data, ids.ids, httpInfo, currentUser, roles);
      return {
        unprocessedItems: Object.keys(deleted.unprocessedItems).length,
      };
    } catch (error) {
      this.logger.error('Delete Batch Roles', error.message);
      throw error;
    }
  }

  async upload(args, httpInfo, currentUser) {
    try {
      const timestamp = new Date().toISOString();
      const { filename, mimetype, createReadStream } = await args.file.file;
      const re = /.+(\.csv)$/;
      if (typeof filename === 'undefined' || !re.test(filename)) {
        throw new Error('Invalid File');
      }

      const readStream = await createReadStream();
      const csvData = await csv({
        headers: ['name', 'code', 'notes', 'permissions'],
        checkColumn: true,
      }).fromStream(readStream);
      if (csvData.length === 0) {
        throw new Error('No Data Found');
      }
      const existingRole = await this.roleRepository.getAllName();
      const roles = csvData.map((role) => {
        const checkCode = existingRole.filter((data) => data.code === role.code);
        if (checkCode.length > 0) {
          throw this.errors.roleIdError();
        }
        const permissions = role.permissions.split('|').reduce((obj, str) => {
          const strData = str.split('.');
          if (strData[0] in obj) {
            obj[strData[0]].push(strData[1]);
          } else {
            obj[strData[0]] = [strData[1]];
          }
          return obj;
        }, {});
        return roleUseCases.create({
          id: this.uuid.create(),
          name: role.name,
          code: role.code,
          notes: role.notes,
          permissions,
          createdAt: timestamp,
          updatedAt: timestamp,
        });
      });

      await roles.forEach(async (role) => {
        await this.create(role, httpInfo, currentUser);
      });

      return {
        filename,
        mimetype,
      };
    } catch (error) {
      this.logger.error('Upload Roles', error);
      throw error;
    }
  }
}

module.exports = Role;
