const Service = require('../Service');
const billLinerConfigUseCases = require('../../domain/billLiner/useCases/index');

class BillLinerConfig extends Service {
  constructor(container) {
    const { configRepository } = container;
    super(configRepository, container);
  }

  async listBillLinerConfig() {
    try {
      return await this.repository.getByType('billLinerMid');
    } catch (error) {
      this.logger.error('List Bill Liner Config Error: ', error.message);
      throw error;
    }
  }

  async deleteBillLinerConfig(data, id, httpInfo, currentUser) {
    try {
      // Check if Exist
      const billLinerConfig = await this.repository.getByName(`BL-${id}`);
      if (billLinerConfig.count < 1) {
        throw new Error(
          JSON.stringify({
            message: 'Could Not Delete Bill Liner',
            details: 'Bill Liner Not Found',
          })
        );
      }
      return await this.delete(data, `BL-${id}`, httpInfo, currentUser, billLinerConfig[0]);
    } catch (error) {
      this.logger.error({
        message: 'Delete Bill Liner Config Error: ',
        payload: JSON.stringify(error),
      });
      throw error;
    }
  }

  async createBillLinerConfig(data, httpInfo, currentUser) {
    try {
      const { content } = data;
      // Validate data
      const billLinerDetails = billLinerConfigUseCases.create({
        name: `BL-${content}`,
        ...data,
      });
      this.logger.info({
        message: 'Create Bill Liner Config Details',
        payload: JSON.stringify(billLinerDetails),
      });
      // Check if Exist
      const billLinerConfig = await this.repository.getByName(`BL-${content}`);
      if (billLinerConfig.count >= 1) {
        throw new Error(
          JSON.stringify({
            message: 'Could Not Create Bill Liner',
            details: 'Bill Liner Already Exist',
          })
        );
      }
      // Insert data
      return await this.create(billLinerDetails, httpInfo, currentUser);
    } catch (error) {
      this.logger.error({
        message: 'Create Bill Liner Config Error',
        payload: JSON.stringify(error),
      });
      throw error;
    }
  }
}

module.exports = BillLinerConfig;
