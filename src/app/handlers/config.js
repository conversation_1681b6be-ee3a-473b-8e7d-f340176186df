const Service = require('../Service');
const configUseCases = require('../../domain/config/useCases/index');

class Config extends Service {
  constructor(container) {
    const { configRepository, channelRepository } = container;
    super(configRepository, container);
    this.channelRepository = channelRepository;
    this.whitelists = ['subMerchants', 'swipeORPaymentType'];
    this.jsonToStringValues = ['refundReason', 'PSORPaymentType', 'cardRefundReason', 'configIpWhitelist'];
    this.notificationRecipient = [
      'dailyContentGCashReportRecipient',
      'monthlyContentGCashReportRecipient',
      'ecpayReportRecipient',
      'globeOneReportRecipient',
      'collectionReportRecipient',
      'creditCardReportRecipient',
      'channelReportRecipient',
      'billingReportRecipient',
    ];
  }

  async show() {
    try {
      const allData = await this.repository.getAllData();
      const flattenConfigs = allData.reduce((accumulator, currentValue) => accumulator.concat(currentValue), []);
      const configs = {
        subMerchants: [],
        cardRefundReason: [],
        refundReason: [],
        dailyContentGCashReportRecipient: [],
        monthlyContentGCashReportRecipient: [],
        ecpayReportRecipient: [],
        globeOneReportRecipient: [],
        collectionReportRecipient: [],
        creditCardReportRecipient: [],
        channelReportRecipient: [],
        billingReportRecipient: [],
        configIpWhitelist: [],
      };
      flattenConfigs.forEach((item) => {
        if (item.type === 'subMerchant' || item.type === 'ecpaySubMerchant') {
          configs.subMerchants.push({
            id: item.name,
            subMerchantId: item.subMerchantId,
            serviceType: item.serviceType,
            merchant: item.merchant,
          });
        } else if (item.name === 'refundReason') {
          configs.refundReason = JSON.parse(item.value);
        } else if (item.name === 'configIpWhitelist') {
          configs.configIpWhitelist = JSON.parse(item.value);
        } else if (item.name === 'PSORPaymentType') {
          if (item.value) {
            configs.PSORPaymentType = JSON.parse(item.value);
          } else {
            configs.PSORPaymentType = [];
          }
        } else if (item.name === 'cardRefundReason') {
          configs.cardRefundReason = JSON.parse(item.value);
        } else if (item.name === 'swipeORPaymentType') {
          if (item?.value?.length) {
            const swipORPaymentData = item.value;
            configs.swipeORPaymentType = swipORPaymentData;
          } else {
            configs.swipeORPaymentType = [];
          }
        } else if (this.notificationRecipient.includes(item.name)) {
          let reciepientFormat = [];
          if (item.value) {
            const reciepientValue = item.value.split(',');
            reciepientFormat = reciepientValue.map((recipient) => ({
              email: recipient,
            }));
          }
          configs[item.name] = reciepientFormat;
        } else if (item.value === 'true' || item.value === 'false') {
          configs[item.name] = JSON.parse(item.value);
        } else {
          configs[item.name] = item.value;
        }
      });
      return configs;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  static async newSubMerchants(submerchants, type, prefix) {
    const newSubMerchants = [];
    const config = [];
    Object.keys(submerchants).forEach((key) => {
      config.push({
        name: `${prefix}-${submerchants[key].serviceType}`,
        serviceType: submerchants[key].serviceType,
        subMerchantId: submerchants[key].subMerchantId,
        type,
        merchant: submerchants[key].merchant,
        updatedAt: new Date().toISOString(),
      });
      newSubMerchants.push(submerchants[key].serviceType);
    });
    return {
      newSubMerchants,
      config,
    };
  }

  async updateConfig(data, httpInfo, currentUser) {
    try {
      const configuration = configUseCases.update(data);
      const { subMerchants, PSORPaymentType, swipeORPaymentType } = configuration;
      let configs = [];
      const globeSubMerchants = [];
      const ecpaySubMerchants = [];
      const errorSubMerchant = [];
      const nonExistingSubMerchant = [];
      const errorOrPaymentType = [];
      const checkPaymentTypeDuplicates = [];
      const checkPSPaymentTypeDuplicates = [];
      const errorPSPaymentType = [];

      if (typeof subMerchants !== 'undefined') {
        // Validations for Sub Merchants
        await subMerchants.forEach((subMerchant) => {
          if (subMerchant.merchant === 'Globe') {
            globeSubMerchants.push(subMerchant);
          } else {
            ecpaySubMerchants.push(subMerchant);
          }
        });
      }

      // Validations For PS OR Payment Type Uniqueness
      if (typeof swipeORPaymentType !== 'undefined') {
        await swipeORPaymentType.forEach((swipeOrPaymentType) => {
          const { name } = swipeOrPaymentType;
          if (checkPaymentTypeDuplicates.includes(name)) {
            errorOrPaymentType.push(name);
          } else {
            checkPaymentTypeDuplicates.push(name);
          }
        });
        if (errorOrPaymentType.length > 0) {
          throw new Error(`Duplicate Swipe OR Payment Type: [${errorOrPaymentType}]`);
        }
      }

      // Validation For Swippe Payment Type Uniqueness
      if (typeof PSORPaymentType !== 'undefined') {
        await PSORPaymentType.forEach((orPaymentType) => {
          const { or } = orPaymentType;
          if (checkPSPaymentTypeDuplicates.includes(or)) {
            errorPSPaymentType.push(or);
          } else {
            checkPaymentTypeDuplicates.push(or);
          }
        });
        if (errorPSPaymentType.length > 0) {
          throw new Error(`Duplicate OR Payment Type: [${errorPSPaymentType}]`);
        }
      }

      // Globe sub merchants
      const globeNewSubMerchants = await Config.newSubMerchants(globeSubMerchants, 'subMerchant', 'SM');

      // Get Globe SubMerchant that is used by channels
      const channelGlobeSubMerchants = await this.channelRepository.getGlobeSubMerchant();
      const globeSubMerchantIds = await this.repository.getAllGlobeSubMerchant();
      const flattenGlobeSubMerchantIds = globeSubMerchantIds.reduce((accumulator, currentValue) => {
        accumulator.push(currentValue.name);
        return accumulator;
      }, []);

      const flattenGlobeSubMerchants = channelGlobeSubMerchants.reduce(
        (accumulator, currentValue) => accumulator.concat(currentValue.serviceType),
        []
      );
      flattenGlobeSubMerchants.forEach((key) => {
        if (!globeNewSubMerchants.newSubMerchants.includes(key)) {
          errorSubMerchant.push(key);
        }
      });
      if (errorSubMerchant.length > 0) {
        throw new Error(`GLOBE_SUB_MERCHANTS_ALREADY_INUSED: [${errorSubMerchant}]`);
      }
      await flattenGlobeSubMerchantIds.forEach((id) => {
        if (!globeNewSubMerchants.newSubMerchants.includes(id)) {
          nonExistingSubMerchant.push({
            name: id,
          });
        }
      });

      // ECPay sub merchants
      const ecpayNewSubMerchants = await Config.newSubMerchants(ecpaySubMerchants, 'ecpaySubMerchant', 'ECP');
      const ecpaySubMerchantIds = await this.repository.getAllECPaySubMerchant();
      const flattenECPaySubMerchantIds = ecpaySubMerchantIds.reduce((accumulator, currentValue) => {
        accumulator.push(currentValue.name);
        return accumulator;
      }, []);

      // Get ECPay SubMerchant that is used by channels
      const channelEcpaySubMerchants = await this.channelRepository.getECPaySubMerchant();
      const flattenECPaySubMerchants = channelEcpaySubMerchants.reduce(
        (accumulator, currentValue) => accumulator.concat(currentValue.ecpayServiceType),
        []
      );
      flattenECPaySubMerchants.forEach((key) => {
        if (!ecpayNewSubMerchants.newSubMerchants.includes(key)) {
          errorSubMerchant.push(key);
        }
      });
      if (errorSubMerchant.length > 0) {
        throw new Error(`ECPAY_SUB_MERCHANTS_ALREADY_INUSED: [${errorSubMerchant}]`);
      }
      await flattenECPaySubMerchantIds.forEach((id) => {
        if (!ecpayNewSubMerchants.newSubMerchants.includes(id)) {
          nonExistingSubMerchant.push({
            name: id,
          });
        }
      });

      configs = configs.concat(globeNewSubMerchants.config, ecpayNewSubMerchants.config);
      // Saving or Updating
      Object.keys(configuration).forEach((key) => {
        if (this.jsonToStringValues.includes(key)) {
          configs.push({
            name: key,
            value: JSON.stringify(configuration[key]),
            type: 'default',
            updatedAt: new Date().toISOString(),
          });
        } else if (this.notificationRecipient.includes(key)) {
          const recipientValue = configuration[key].map((recipient) => recipient.email);
          configs.push({
            name: key,
            value: recipientValue.join(','),
            type: 'default',
            updatedAt: new Date().toISOString(),
          });
        } else if (!this.whitelists.includes(key)) {
          configs.push({
            name: key,
            value: configuration[key],
            type: 'default',
            updatedAt: new Date().toISOString(),
          });
        }
      });
      const oldData = await this.show();
      if (nonExistingSubMerchant.length > 0) {
        await this.repository.batchDelete(nonExistingSubMerchant);
      }
      if (typeof swipeORPaymentType !== 'undefined') {
        await this.repository.updateSwipeORType(swipeORPaymentType);
      }
      await this.batchPut(configs, httpInfo, currentUser, oldData, configuration);
      return configuration;
    } catch (error) {
      this.logger.error(JSON.stringify(error.message));
      throw JSON.stringify(error.message);
    }
  }

  async getRefundReasons() {
    const reasons = await this.repository.getByName('refundReason');
    return JSON.parse(reasons[0].value);
  }

  async getCardRefundReason() {
    const reasons = await this.repository.getByName('cardRefundReason');
    return JSON.parse(reasons[0].value);
  }

  async refundValidity() {
    const refundValidity = await this.repository.getById('refundDaysValidity');
    const { value } = refundValidity;
    return {
      days: value,
    };
  }

  async getPSPaymentType() {
    const orPaymentType = await this.repository.getByName('PSORPaymentType');
    if (orPaymentType.count < 1) {
      return [];
    }
    return orPaymentType[0].value;
  }
}

module.exports = Config;
