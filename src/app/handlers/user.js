const csv = require('csvtojson');
const Service = require('../Service');
const userUseCases = require('../../domain/users/useCases');
const auditUseCases = require('../../domain/audit/useCases');

class User extends Service {
  constructor(container) {
    const { userRepository, roleRepository, loginVerifier, config, Mapper, authorization, logger, jwt, DateHelper } =
      container;
    super(userRepository, container);
    this.roleRepository = roleRepository;
    this.client = loginVerifier;
    this.mapper = Mapper;
    this.logger = logger;
    this.authorization = authorization;
    this.authorize = true;
    this.jwt = jwt;
    this.dateHelper = DateHelper;
    this.config = config;
  }

  async login(data, httpInfo) {
    try {
      this.logger.info({
        message: 'Login Attempt',
        payload: data,
      });

      const jwt = await this.client.verifyAccessToken(data.idToken, 'api://default');

      this.logger.info({
        message: 'Okta User Token Info',
        payload: JSON.stringify(jwt),
      });

      if (!jwt) {
        throw new Error('Okta authentication failed!');
      }
      const email = jwt?.claims?.email_address ?? jwt?.claims?.appUserEmail ?? null;
      userUseCases.login({
        ...data,
        email,
        hostedDomain: email?.split('@')[1] ?? null,
      });
      const user = await this.repository.getUserByEmail(email);
      if (typeof user === 'undefined' || user.isActive === false) {
        throw new Error('User Either Active or Not Registered');
      }
      const permission = await this.roleRepository.getById(user.roleId);

      const role = permission[0];
      user.role = role;
      delete user.roleId;

      const token = await this.jwt.encode(
        JSON.stringify({
          id: user.id,
          email: user.email,
        })
      );

      this.updateLoginTime(user.id);
      //save access_token and refresh_token
      this.repository.saveOauthTokens(user.id, 'refresh_token_sample', data.idToken);

      // Audit Logs
      const category = this.category(this.repoName, 'login');
      await this.auditLogs(
        {
          ...httpInfo,
        },
        {
          id: user.id,
          email: user.email,
        },
        category
      );

      if (user.assignedChannels) {
        user.assignedChannels = JSON.parse(user.assignedChannels);
      } else {
        user.assignedChannels = [];
      }
      if (user.cardAssignedChannels) {
        user.cardAssignedChannels = JSON.parse(user.cardAssignedChannels);
      } else {
        user.cardAssignedChannels = [];
      }
      if (user.ewalletAssignedChannels) {
        user.ewalletAssignedChannels = JSON.parse(user.ewalletAssignedChannels);
      } else {
        user.ewalletAssignedChannels = [];
      }
      if (user.postPaymentConfigChannels) {
        user.postPaymentConfigChannels = JSON.parse(user.postPaymentConfigChannels);
      } else {
        user.postPaymentConfigChannels = [];
      }

      return {
        token,
        accessToken: data.idToken,
        user,
        permissions: permission.permissions,
      };
    } catch (error) {
      this.logger.error({
        message: 'Login Attempt',
        payload: error,
      });
      throw new Error('Login Failed!');
    }
  }

  async logout(data) {
    try {
      this.logger.info({
        message: 'Logout Attempt',
        payload: data,
      });
      // return await this.client.revokeToken(data.accessToken);
      return {
        status: 'success',
      };
    } catch (error) {
      this.logger.error({
        message: 'Logout Attempt',
        payload: error,
      });
      throw new Error('Logout Failed!');
    }
  }

  async validateToken(data) {
    const userInfo = this.jwt.validate(data.token);
    if (userInfo) {
      const user = await this.repository.getById(userInfo.id);
      const roles = await this.roleRepository.getById(user.roleId);
      const role = roles[0];
      user.role = role;
      return user;
    }

    return {
      user: null,
    };
  }

  async registration(data, httpInfo, currentUser) {
    try {
      this.logger.info({
        message: 'Create User Payload',
        payload: JSON.stringify(data),
      });
      let channelName = 'NoAssignedChannel';
      let roleName = 'NoAssignedRole';
      // Validate registration
      const user = userUseCases.registration({
        id: this.uuid.create(),
        ...data,
      });
      // Check if User Exist
      const checkUser = await this.repository.getByEmail(user.email);
      if (checkUser.count > 0) {
        throw this.errors.userCreateError();
      }
      // Insert Data
      const created = await this.create(user, httpInfo, currentUser);

      // Get Role Details
      const role = await this.roleRepository.getById(created.roleId);

      if (typeof role !== 'undefined') {
        roleName = role[0].name;
      }

      // Get Channel Details
      if (typeof created.channel !== 'undefined') {
        const channel = await this.channelRepository.getById(created.channel);
        if (typeof channel !== 'undefined') {
          channelName = channel.name;
        }
      }
      // Sending of Email
      const emailPatternId = this.config.notifPatternId.emails[this.repoName].registration;
      this.email({
        to: created.email,
        patternId: emailPatternId,
        parameters: [
          {
            Name: 'NAME',
            Value: created.name,
          },
          {
            Name: 'ROLE_NAME',
            Value: roleName,
          },
          {
            Name: 'EMAIL',
            Value: created.email,
          },
          {
            Name: 'CHANNEL_NAME',
            Value: channelName,
          },
          {
            Name: 'URL_LINK',
            Value: this.urlLink,
          },
        ],
      });
      // Sending of SMS
      const smsPatternId = this.config.notifPatternId.sms[this.repoName].registration;
      this.sms({
        msisdn: created.mobileNumber,
        patternId: smsPatternId,
        parameters: [
          {
            Name: 'NAME',
            Value: created.name,
          },
          {
            Name: 'ROLE_NAME',
            Value: roleName,
          },
          {
            Name: 'EMAIL',
            Value: created.email,
          },
          {
            Name: 'CHANNEL_NAME',
            Value: channelName,
          },
          {
            Name: 'URL_LINK',
            Value: this.urlLink,
          },
        ],
      });
      return created;
    } catch (error) {
      this.logger.error({
        message: 'User Creation Error!',
        payload: JSON.stringify(error),
      });
      throw error;
    }
  }

  async updateUser(data, id, httpInfo, currentUser) {
    try {
      let role;
      let roleName = 'No Role';
      const payload = data;
      const { assignedChannels, cardAssignedChannels, ewalletAssignedChannels, postPaymentConfigChannels } = data;
      if (assignedChannels) {
        payload.assignedChannels = await data.assignedChannels.map((channel) => channel.channelId);
      }
      if (cardAssignedChannels) {
        payload.cardAssignedChannels = await data.cardAssignedChannels.map((channel) => channel.channelId);
      }
      if (ewalletAssignedChannels) {
        payload.ewalletAssignedChannels = await data.ewalletAssignedChannels.map((channel) => channel.channelId);
      }
      if (postPaymentConfigChannels) {
        payload.postPaymentConfigChannels = await data.postPaymentConfigChannels.map((channel) => channel.channelId);
      }
      // Validate Payload
      const userPayload = userUseCases.update({
        ...id,
        ...payload,
      });
      userPayload.assignedChannels = JSON.stringify(assignedChannels);
      userPayload.cardAssignedChannels = JSON.stringify(cardAssignedChannels);
      userPayload.ewalletAssignedChannels = JSON.stringify(ewalletAssignedChannels);
      userPayload.postPaymentConfigChannels = JSON.stringify(postPaymentConfigChannels);
      const check = await this.repository.getById(id);
      if (typeof check === 'undefined' || check.count < 1) {
        throw this.errors.notFound();
      } else if (typeof userPayload.email !== 'undefined') {
        // Validate if Email Already Exist
        const checkUser = await this.repository.getByEmail(userPayload.email);
        if (checkUser.count > 0) {
          throw this.errors.userUpdateError();
        }
      }
      let updated = '';
      const timestamp = new Date().toISOString();
      if (Object.prototype.hasOwnProperty.call(userPayload, 'isActive')) {
        updated = await this.update(
          {
            ...userPayload,
            isActiveAt: timestamp,
          },
          id,
          httpInfo,
          currentUser,
          check
        );
        // Get Role Details
        role = await this.roleRepository.getById(updated.roleId);
        if (typeof role !== 'undefined') {
          roleName = role[0].name;
        }
        // Sending of Email
        if (userPayload.isActive === true && updated.emailNotif === true) {
          const emailPatternId = this.config.notifPatternId.emails[this.repoName].activate;
          this.email({
            to: updated.email,
            patternId: emailPatternId,
            parameters: [
              {
                Name: 'NAME',
                Value: updated.name,
              },
              {
                Name: 'ROLE_NAME',
                Value: roleName,
              },
              {
                Name: 'EMAIL',
                Value: updated.email,
              },
              {
                Name: 'URL_LINK',
                Value: this.urlLink,
              },
            ],
          });
        }
        if (userPayload.isActive === false && updated.emailNotif === true) {
          const emailPatternId = this.config.notifPatternId.emails[this.repoName].deactivate;
          this.email({
            to: updated.email,
            patternId: emailPatternId,
            parameters: [
              {
                Name: 'NAME',
                Value: updated.name,
              },
              {
                Name: 'ROLE_NAME',
                Value: roleName,
              },
              {
                Name: 'EMAIL',
                Value: updated.email,
              },
              {
                Name: 'URL_LINK',
                Value: this.urlLink,
              },
            ],
          });
        }
        if (updated.smsNotif === true && userPayload.isActive === false) {
          // Sending of SMS
          const smsPatternId = this.config.notifPatternId.sms[this.repoName].activate;
          this.sms({
            msisdn: updated.mobileNumber,
            patternId: smsPatternId,
            parameters: [
              {
                Name: 'NAME',
                Value: updated.name,
              },
              {
                Name: 'ROLE_NAME',
                Value: roleName,
              },
              {
                Name: 'EMAIL',
                Value: updated.email,
              },
            ],
          });
        }
        if (updated.smsNotif === true && userPayload.isActive === false) {
          // Sending of SMS
          const smsPatternId = this.config.notifPatternId.sms[this.repoName].deactivate;
          this.sms({
            msisdn: updated.mobileNumber,
            patternId: smsPatternId,
            parameters: [
              {
                Name: 'NAME',
                Value: updated.name,
              },
              {
                Name: 'ROLE_NAME',
                Value: roleName,
              },
              {
                Name: 'EMAIL',
                Value: updated.email,
              },
            ],
          });
        }
      } else {
        updated = await this.update(
          {
            ...userPayload,
          },
          id,
          httpInfo,
          currentUser,
          check
        );
      }
      // Get Role Details
      role = await this.roleRepository.getById(updated.roleId);
      if (typeof role !== 'undefined') {
        roleName = role[0].name;
      }

      if (updated.emailNotif === true) {
        // Sending of Email
        const emailPatternId = this.config.notifPatternId.emails[this.repoName].update;
        this.email({
          to: updated.email,
          patternId: emailPatternId,
          parameters: [
            {
              Name: 'NAME',
              Value: updated.name,
            },
            {
              Name: 'ROLE_NAME',
              Value: roleName,
            },
            {
              Name: 'EMAIL',
              Value: updated.email,
            },
            {
              Name: 'URL_LINK',
              Value: this.urlLink,
            },
          ],
        });
      }
      if (updated.smsNotif === true) {
        // Sending of SMS
        const smsPatternId = this.config.notifPatternId.sms[this.repoName].update;
        this.sms({
          msisdn: updated.mobileNumber,
          patternId: smsPatternId,
          parameters: [
            {
              Name: 'NAME',
              Value: updated.name,
            },
            {
              Name: 'ROLE_NAME',
              Value: roleName,
            },
            {
              Name: 'EMAIL',
              Value: updated.email,
            },
            {
              Name: 'URL_LINK',
              Value: this.urlLink,
            },
          ],
        });
      }
      if (updated.assignedChannels) {
        updated.assignedChannels = JSON.parse(updated.assignedChannels);
      }
      if (updated.cardAssignedChannels) {
        updated.cardAssignedChannels = JSON.parse(updated.cardAssignedChannels);
      }
      if (updated.ewalletAssignedChannels) {
        updated.ewalletAssignedChannels = JSON.parse(updated.ewalletAssignedChannels);
      }
      if (updated.postPaymentConfigChannels) {
        updated.postPaymentConfigChannels = JSON.parse(updated.postPaymentConfigChannels);
      }
      return updated;
    } catch (error) {
      this.logger.error({
        message: 'User Update Error!',
        payload: JSON.stringify(error),
      });
      throw error;
    }
  }

  async search(data) {
    try {
      const results = await this.searchDataWithCursors(data);
      if (results.filteredData.length > 0) {
        const roleIds = results.filteredData.map((fdata) => fdata.roleId);
        const roles = await this.roleRepository.whereIn(roleIds);
        if (roles.length > 0) {
          results.filteredData = await this.mapper(results.filteredData, roles, 'role', 'roleId');
        }
      }
      if (typeof results.lastKey === 'undefined') {
        results.lastKey = null;
      } else {
        results.lastKey = results.lastKey.id;
      }
      return results;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async show(where) {
    try {
      const data = await this.repository.getById(Object.values(where)[0]);
      if (data) {
        const roles = await this.roleRepository.getById(data.roleId);
        Object.assign(data, { role: roles[0] });
        if (!data.assignedChannels) {
          data.assignedChannels = [];
        } else {
          data.assignedChannels = JSON.parse(data.assignedChannels);
        }
        if (!data.cardAssignedChannels) {
          data.cardAssignedChannels = [];
        } else {
          data.cardAssignedChannels = JSON.parse(data.cardAssignedChannels);
        }
        if (!data.ewalletAssignedChannels) {
          data.ewalletAssignedChannels = [];
        } else {
          data.ewalletAssignedChannels = JSON.parse(data.ewalletAssignedChannels);
        }
        if (!data.postPaymentConfigChannels) {
          data.postPaymentConfigChannels = [];
        } else {
          data.postPaymentConfigChannels = JSON.parse(data.postPaymentConfigChannels);
        }
        return data;
      }
      return [];
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async userNotification(user) {
    let roleName = '';
    if (user.smsNotif === true) {
      // Get Role Details
      const role = await this.roleRepository.getById(user.roleId);
      if (typeof role !== 'undefined') {
        roleName = role[0].name;
      }
      if (user.emailNotif === true) {
        // Sending of Email
        const emailPatternId = this.config.notifPatternId.emails[this.repoName].delete;
        this.email({
          to: user.email,
          patternId: emailPatternId,
          parameters: [
            {
              Name: 'NAME',
              Value: user.name,
            },
            {
              Name: 'ROLE_NAME',
              Value: roleName,
            },
            {
              Name: 'EMAIL',
              Value: user.email,
            },
          ],
        });
      }
      if (user.smsNotif === true) {
        // Sending of SMS
        const smsPatternId = this.config.notifPatternId.sms[this.repoName].delete;
        this.sms({
          msisdn: user.mobileNumber,
          patternId: smsPatternId,
          parameters: [
            {
              Name: 'NAME',
              Value: user.name,
            },
            {
              Name: 'ROLE_NAME',
              Value: roleName,
            },
            {
              Name: 'EMAIL',
              Value: user.email,
            },
          ],
        });
      }
    }
  }

  async deleteUser(data, id, httpInfo, currentUser) {
    try {
      const user = await this.repository.getById(Object.values(id)[0]);
      if (typeof user === 'undefined' || user.count < 1) {
        throw this.errors.notFound();
      }
      if (currentUser.id === Object.values(id)[0]) {
        throw this.errors.deletionError();
      }
      const deleted = await this.delete(data, id, httpInfo, currentUser, user);
      await this.userNotification(user);
      return deleted;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async deleteUsers(data, hashKeys, httpInfo, currentUser) {
    try {
      const users = await this.batchGet(hashKeys.ids);
      if (users === undefined || users.length === 0) {
        throw this.errors.notFound();
      }
      const checkUser = await users.filter((user) => user.id === currentUser.id);
      if (checkUser.length > 0) {
        throw this.errors.deletionError();
      }
      const deleted = await this.batchDelete(data, hashKeys.ids, httpInfo, currentUser, users);
      Promise.all(
        users.map(async (user) => {
          await this.userNotification(user);
        })
      );
      return {
        unprocessedItems: Object.keys(deleted.unprocessedItems).length,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async summary(filter) {
    try {
      const inactiveUsersPromise = [];
      const activeUsersPromise = [];
      const monthRange = this.dateHelper.dateRange(filter.range.start, filter.range.end);

      for (let i = 0; i < monthRange.length; i += 1) {
        inactiveUsersPromise.push(this.repository.getInactiveUser(monthRange[i]));
        activeUsersPromise.push(this.repository.getActiveUser(monthRange[i]));
      }

      const inactiveUsersResults = await Promise.all(inactiveUsersPromise);
      const activeUsersResults = await Promise.all(activeUsersPromise);

      const inactiveUsers = monthRange.map((month, index) => ({
        month: month.month,
        count: inactiveUsersResults[index]?.count ?? 0,
      }));

      const activeUsers = monthRange.map((month, index) => ({
        month: month.month,
        count: activeUsersResults[index]?.count ?? 0,
      }));

      return {
        activeUsers,
        inactiveUsers,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async download(httpInfo, currentUser) {
    try {
      const users = await this.repository.extractAllUsers();
      const category = this.category(this.repoName, 'download');
      await this.auditLogs(
        {
          ...httpInfo,
        },
        currentUser,
        category
      );
      return users.map((user) => {
        let status = 'Active';
        if (user.isActive === false) {
          status = 'Inactive';
        }
        return {
          id: user.id,
          name: user.name,
          emailAddress: user.email,
          roleName: user.roleId.name,
          roleDescription: user.roleId.notes,
          group: user.group,
          permission: JSON.stringify(user.roleId.permissions),
          createdAt: user.createdAt,
          loginTime: user.loginTime,
          status,
        };
      });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async upload(args, httpInfo, currentUser) {
    try {
      const timestamp = new Date().toISOString();
      const { filename, mimetype, createReadStream } = await args.file.file;
      const re = /.+(\.csv)$/;
      if (typeof filename === 'undefined' || !re.test(filename)) {
        throw new Error('Invalid File');
      }
      const readStream = await createReadStream();
      const csvData = await csv({
        headers: ['name', 'email', 'mobileNumber', 'role', 'channel', 'group', 'division', 'department'],
        checkColumn: true,
      }).fromStream(readStream);

      if (csvData.length === 0) {
        throw new Error('No Data Found');
      }
      const existingUser = await this.repository.getAllName();
      const existingRole = await this.roleRepository.getAllName();
      const existingChannel = await this.channelRepository.getAllName();
      const users = csvData.map((user) => {
        let channelId = '';
        let channelName = '';
        const checkUser = existingUser.filter((data) => data.email === user.email);
        if (checkUser.length > 0) {
          throw this.errors.userCreateError();
        }

        const checkRole = existingRole.filter((data) => data.name === user.role);
        if (checkRole.length <= 0) {
          throw new Error(`ROLE_NOT_FOUND: ${user.role}`);
        }

        const checkChannel = existingChannel.filter((data) => data.name === user.channel);
        if (checkChannel.length > 0) {
          channelId = checkChannel[0].id;
          channelName = checkChannel[0].name;
        }
        let userNumber;
        if (user.mobileNumber !== '') {
          const { mobileNumber } = user;
          userNumber = mobileNumber;
        }
        return userUseCases.registration({
          id: this.uuid.create(),
          name: user.name,
          email: user.email,
          mobileNumber: userNumber,
          roleId: checkRole[0].id,
          roleName: checkRole[0].name,
          channel: channelId,
          channelName,
          group: user.group,
          division: user.division,
          department: user.department,
          createdAt: timestamp,
          updatedAt: timestamp,
          isActiveAt: timestamp,
        });
      });
      await users.forEach(async (user) => {
        const created = this.create(user, httpInfo, currentUser);
        // Sending of Email
        const emailPatternId = this.config.notifPatternId.emails[this.repoName].registration;
        this.email({
          to: created.email,
          patternId: emailPatternId,
          parameters: [
            {
              Name: 'NAME',
              Value: created.name,
            },
            {
              Name: 'ROLE_NAME',
              Value: user.roleName,
            },
            {
              Name: 'EMAIL',
              Value: created.email,
            },
            {
              Name: 'CHANNEL_NAME',
              Value: user.channelName,
            },
            {
              Name: 'URL_LINK',
              Value: this.urlLink,
            },
          ],
        });
        // Sending of SMS
        const smsPatternId = this.config.notifPatternId.sms[this.repoName].registration;
        this.sms({
          msisdn: created.mobileNumber,
          patternId: smsPatternId,
          parameters: [
            {
              Name: 'NAME',
              Value: created.name,
            },
            {
              Name: 'ROLE_NAME',
              Value: user.roleName,
            },
            {
              Name: 'EMAIL',
              Value: created.email,
            },
            {
              Name: 'CHANNEL_NAME',
              Value: user.channelName,
            },
            {
              Name: 'URL_LINK',
              Value: this.urlLink,
            },
          ],
        });
      });
      return {
        filename,
        mimetype,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async deactivateUser(data, hashKeys, httpInfo, currentUser) {
    try {
      // Get Users using ID
      const users = await this.batchGet(hashKeys.ids);
      const { ids } = hashKeys;
      if (users === undefined || users.length === 0) {
        throw this.errors.notFound();
      }
      // Check if User Exist
      const checkUsers = await users.filter((user) => user.id === currentUser.id);
      if (checkUsers.length > 0) {
        throw this.errors.deativationUserNotFound();
      }
      // Checking of Current User
      let userInfo = {};
      if (typeof currentUser !== 'undefined' && currentUser !== null) {
        const user = await this.userRepository.getById(currentUser.id);
        userInfo = {
          userId: user.id,
          isActive: false,
          userEmail: user.email,
          userName: user.name,
          roleId: user.roleId,
          roleName: currentUser.role.name,
          reasonToDeactivate: user.reasonToDeactivate,
        };
      }
      // Process
      const modifiedKeys = ids.map((id) => {
        const userFound = users.find((item) => item.id === id);
        if (userFound) {
          return {
            ...userFound,
            isActive: false,
            reasonToDeactivate: data.reasonToDeactivate,
          };
        }
        return {};
      });
      const category = this.category(this.repoName, 'deactivate');
      const date = new Date().toISOString();
      const updated = await this.repository.batchPut(modifiedKeys);
      const auditBatchInsert = modifiedKeys.map((item) => {
        const oldValue = users.find((oldData) => oldData.id === item.id);
        return auditUseCases.create({
          id: this.uuid.create(),
          oldValue: { ...oldValue },
          newValue: { ...item },
          ...data,
          ...userInfo,
          ...httpInfo,
          category,
          createdAt: date,
          updatedAt: date,
        });
      });
      await this.auditRepository.batchPut(auditBatchInsert);
      return {
        unprocessedItems: Object.keys(updated[0]?.unprocessedItems).length,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async uploadUsersStatusUpdate(args, httpInfo, currentUser) {
    try {
      const invalidUsers = [];
      const validUsers = [];
      const { filename, mimetype, createReadStream } = await args.file.file;
      const re = /.+(\.csv)$/;
      if (typeof filename === 'undefined' || !re.test(filename)) {
        throw new Error('Invalid File');
      }
      const readStream = await createReadStream();
      const csvData = await csv({
        headers: ['email', 'status'],
        checkColumn: true,
      }).fromStream(readStream);
      // If row data exceed 100
      if (csvData.length > 100) {
        this.logger.error('CSV Row Data Exceed to 100 rows');
        throw new Error('CSV Row Data Exceed to 100 rows');
      }
      // Check if File has Data
      if (csvData.length === 0) {
        throw new Error('No Data Found');
      }
      const existingUser = await this.repository.getAllName();
      await csvData.forEach((user) => {
        const { email, status } = user;
        let isActive = true;
        // Validate Payload
        const validateUser = userUseCases.UpdateUserStatus({
          email,
          status: status.toLowerCase(),
        });
        if (!validateUser.errors) {
          // Convert Status to Boolean
          if (status.toLowerCase() === 'inactive') {
            isActive = false;
          }
          // Validate if User Exist
          const checkUser = existingUser.filter((data) => data.email === user.email);
          if (checkUser.length > 0) {
            const checkDuplicate = validUsers.filter((data) => data.email === user.email && data.isActive === isActive);
            if (checkDuplicate.length <= 0) {
              if (isActive === false && checkUser[0].isActive !== false) {
                validUsers.push({
                  id: checkUser[0].id,
                  email,
                  isActive: false,
                });
              } else if (isActive === true && checkUser[0].isActive !== true) {
                validUsers.push({
                  id: checkUser[0].id,
                  email,
                  isActive: true,
                });
              } else {
                invalidUsers.push({
                  email,
                  errorReason: JSON.stringify([
                    {
                      message: 'status is already updated',
                      path: 'status',
                    },
                  ]),
                });
              }
            } else {
              invalidUsers.push({
                email,
                errorReason: JSON.stringify([
                  {
                    message: 'email and status is already updated',
                    path: 'email and status',
                  },
                ]),
              });
            }
          } else {
            invalidUsers.push({
              email,
              errorReason: JSON.stringify([
                {
                  message: 'email not registered',
                  path: 'email',
                },
              ]),
            });
          }
        } else {
          invalidUsers.push({
            email,
            errorReason: JSON.stringify(validateUser.errors),
          });
        }
      });
      // Email Notification
      await validUsers.forEach(async (user) => {
        const updated = await this.update(
          {
            ...user,
            isActiveAt: new Date().toISOString(),
          },
          user.id,
          httpInfo,
          currentUser,
          user
        );
        this.logger.info({
          message: 'Bulk - User Update Status',
          payload: updated,
        });
        const role = await this.roleRepository.getById(updated.roleId);
        // Sending of Email
        if (user.isActive === true && updated.emailNotif === true) {
          const emailPatternId = this.config.notifPatternId.emails[this.repoName].activate;
          this.email({
            to: updated.email,
            patternId: emailPatternId,
            parameters: [
              {
                Name: 'NAME',
                Value: updated.name,
              },
              {
                Name: 'ROLE_NAME',
                Value: role[0].name,
              },
              {
                Name: 'EMAIL',
                Value: updated.email,
              },
              {
                Name: 'URL_LINK',
                Value: this.urlLink,
              },
            ],
          });
        }
        if (user.isActive === false && updated.emailNotif === true) {
          const emailPatternId = this.config.notifPatternId.emails[this.repoName].deactivate;
          this.email({
            to: updated.email,
            patternId: emailPatternId,
            parameters: [
              {
                Name: 'NAME',
                Value: updated.name,
              },
              {
                Name: 'ROLE_NAME',
                Value: role[0].name,
              },
              {
                Name: 'EMAIL',
                Value: updated.email,
              },
              {
                Name: 'URL_LINK',
                Value: this.urlLink,
              },
            ],
          });
        }
        if (updated.smsNotif === true && updated.isActive === false) {
          // Sending of SMS
          const smsPatternId = this.config.notifPatternId.sms[this.repoName].activate;
          this.sms({
            msisdn: updated.mobileNumber,
            patternId: smsPatternId,
            parameters: [
              {
                Name: 'NAME',
                Value: updated.name,
              },
              {
                Name: 'ROLE_NAME',
                Value: role[0].name,
              },
              {
                Name: 'EMAIL',
                Value: updated.email,
              },
            ],
          });
        }
        if (updated.smsNotif === true && updated.isActive === false) {
          // Sending of SMS
          const smsPatternId = this.config.notifPatternId.sms[this.repoName].deactivate;
          this.sms({
            msisdn: updated.mobileNumber,
            patternId: smsPatternId,
            parameters: [
              {
                Name: 'NAME',
                Value: updated.name,
              },
              {
                Name: 'ROLE_NAME',
                Value: role[0].name,
              },
              {
                Name: 'EMAIL',
                Value: updated.email,
              },
            ],
          });
        }
      });
      return {
        filename,
        mimetype,
        validUsers,
        invalidUsers,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
module.exports = User;
