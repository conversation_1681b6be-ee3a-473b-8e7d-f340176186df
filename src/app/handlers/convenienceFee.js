const Service = require('../Service');
const convenienceFeeUseCases = require('../../domain/convenienceFee/useCases');

class ConvenienceFee extends Service {
  constructor(container) {
    const { convenienceFeeRepository, uuid, channelRepository, formatter } = container;
    super(convenienceFeeRepository, container);
    this.uuid = uuid;
    this.formatter = formatter;
    this.channelRepository = channelRepository;
  }

  async show(where) {
    try {
      const data = await this.repository.getById(Object.values(where)[0].id);
      return data[0];
    } catch (error) {
      this.logger.error('Show Convenience Fee', error.message);
      throw error;
    }
  }

  async search(data) {
    try {
      return this.searchDataWithCursors(data);
    } catch (error) {
      this.logger.error('Search Convenience Fee', error.message);
      throw error;
    }
  }

  async searchDataWithCursors(data) {
    let allIds, filteredData;
    const cursors = [''];
    if (Object.keys(data.filter).length <= 0) {
      filteredData = await this.repository.listAll(data);
      allIds = await this.repository.getAllId();
    } else {
      const searchData = await this.repository.searchFilter(data);
      allIds = await this.repository.searchFilterNoPaginate(data);
      filteredData = [];
      const filteredCount = searchData.count;
      for (let i = 0; i < data.pagination.limit; i += 1) {
        if (
          typeof searchData[i] !== 'undefined' &&
          (data.pagination.limit >= filteredCount || data.pagination.limit <= filteredCount)
        ) {
          filteredData.push(searchData[i]);
        }
      }
    }
    const filterCount = filteredData.count;
    if (filterCount <= 0) {
      return { filteredData: [], count: 0, cursors: [''] };
    }
    for (let i = 1; i < Math.ceil(allIds.count / data.pagination.limit); i += 1) {
      cursors.push(allIds[i * data.pagination.limit - 1].id);
    }
    return {
      filteredData,
      count: allIds.count,
      cursors,
    };
  }

  async createConvenienceFee(data, httpInfo, currentUser) {
    try {
      if (Array.isArray(data.brands) && Array.isArray(data.paymentMethods)) {
        return this.createConvenienceFeeBulk(data, httpInfo, currentUser);
      }

      const { channelId, brand, gatewayProcessor, paymentMethod, transactionType } = data;

      const compositeKey = {
        channelId,
        brand,
        gatewayProcessor,
        paymentMethod,
        transactionType,
      };

      const hasExistingChannel = await this.channelRepository.getByChannelId(channelId);
      if (!hasExistingChannel.count) {
        throw this.errors.channelIdNotFound();
      }

      const hasExistingCF = await this.repository.getCompositeKey(compositeKey);
      if (hasExistingCF.count) {
        throw this.errors.convenienceFeeUniqueness();
      }

      const { name } = hasExistingChannel[0];
      const encodedKey = this.formatter.createCompositeKey(compositeKey, ['channelId']);
      const date = new Date().toISOString();
      const convenienceFeeDetails = convenienceFeeUseCases.create({
        id: this.uuid.create(),
        name,
        ...data,
        createdAt: date,
        updatedAt: date,
        compositeKey: encodedKey,
      });

      const createdEntry = await this.create(convenienceFeeDetails, httpInfo, currentUser);
      return {
        created: 1,
        entries: [createdEntry],
        skipped: 0,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async createConvenienceFeeBulk(data, httpInfo, currentUser) {
    try {
      const { channelId, brands, gatewayProcessor, paymentMethods, transactionType, ...cfConfig } = data;

      const hasExistingChannel = await this.channelRepository.getByChannelId(channelId);
      if (!hasExistingChannel.count) {
        throw this.errors.channelIdNotFound();
      }

      const compositeKeys = [];
      const combinations = [];
      const date = new Date().toISOString();
      const { name } = hasExistingChannel[0];

      brands.forEach((brand) => {
        paymentMethods.forEach((paymentMethod) => {
          const compositeKeyData = { channelId, brand, gatewayProcessor, paymentMethod, transactionType };
          const encodedKey = this.formatter.createCompositeKey(compositeKeyData, ['channelId']);

          compositeKeys.push(encodedKey);
          combinations.push({
            id: this.uuid.create(),
            name,
            channelId,
            brand,
            gatewayProcessor,
            paymentMethod,
            transactionType,
            ...cfConfig,
            createdAt: date,
            updatedAt: date,
            compositeKey: encodedKey,
          });
        });
      });

      const conflictingEntries = [];

      for (const combo of combinations) {
        const compositeKeyData = {
          channelId,
          brand: combo.brand,
          gatewayProcessor,
          paymentMethod: combo.paymentMethod,
          transactionType,
        };

        const hasExistingCF = await this.repository.getCompositeKey(compositeKeyData);
        if (hasExistingCF.count) {
          conflictingEntries.push(`${combo.brand} + ${combo.paymentMethod}`);
        }
      }

      // Filter out conflicting combinations, keep the valid ones
      const validCombinations = combinations.filter((combo) => {
        return !conflictingEntries.includes(`${combo.brand} + ${combo.paymentMethod}`);
      });

      // If no valid combinations
      if (validCombinations.length === 0) {
        const error = new Error('CONVENIENCE_FEE_ALL_CONFLICTS');
        error.message = `All Convenience Fee combinations already exist: ${conflictingEntries.join(', ')}`;
        throw error;
      }

      // Validate only the valid combinations
      const validatedCombinations = validCombinations.map((combo) => {
        return convenienceFeeUseCases.create(combo);
      });

      const createdEntries = await Promise.all(
        validatedCombinations.map((cfData) => this.create(cfData, httpInfo, currentUser))
      );

      return {
        created: createdEntries.length,
        entries: createdEntries,
        skipped: conflictingEntries.length,
      };
    } catch (error) {
      this.logger.error('Bulk Convenience Fee Creation', error);
      throw error;
    }
  }

  async updateConvenienceFee(data, id, httpInfo, currentUser) {
    try {
      const { channelId, brand, gatewayProcessor, paymentMethod, transactionType } = data;

      const compositeKey = {
        channelId,
        brand,
        gatewayProcessor,
        paymentMethod,
        transactionType,
      };

      const hasExistingEntry = await this.repository.getById(id.id);
      if (!hasExistingEntry.count) {
        throw this.errors.notFound();
      }

      const hasExistingChannel = await this.channelRepository.getByChannelId(channelId);
      if (!hasExistingChannel.count) {
        throw this.errors.channelIdNotFound();
      }

      const currentData = await this.repository.getCompositeKey(compositeKey);
      if (currentData.count && currentData[0].id !== id.id) {
        throw this.errors.convenienceFeeUniqueness();
      }

      const newEncodedKey = this.formatter.createCompositeKey(compositeKey, ['channelId']);
      const convenienceFeeDetailsUpdated = convenienceFeeUseCases.update({
        id: id.id,
        ...currentData[0],
        ...data,
        compositeKey: newEncodedKey,
      });

      return await this.update(convenienceFeeDetailsUpdated, id, httpInfo, currentUser, currentData[0]);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async deleteConvenienceFee(data, id, httpInfo, currentUser) {
    try {
      const convenienceFee = await this.repository.getById(id.id);
      if (!convenienceFee.count) {
        throw this.errors.notFound();
      }
      return this.delete(data, id, httpInfo, currentUser, convenienceFee[0]);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}

module.exports = ConvenienceFee;
