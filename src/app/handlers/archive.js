const { AthenaClient } = require('@aws-sdk/client-athena');
const Service = require('../Service');
const voucherLogs = require('../../domain/voucher/useCases/voucherTransaction');

class Archive extends Service {
  constructor(container) {
    const {
      paybillReferenceCodesGateway,
      paybillTransactionLogsGateway,
      auditRepository,
      archivedAuditLogs,
      archivedTransactionLogs,
      archivedSwipeLogs,
    } = container;
    super(auditRepository, container);
    this.athena = new AthenaClient();
    this.paybillReferenceCodesGateway = paybillReferenceCodesGateway;
    this.paybillTransactionLogsGateway = paybillTransactionLogsGateway;
    this.ArchivedAuditLogs = archivedAuditLogs;
    this.ArchivedTransactionLogs = archivedTransactionLogs;
    this.ArchivedSwipeLogs = archivedSwipeLogs;
    this.tableColumns = [];
  }

  // Old Paybill Payment Service
  async searchReferenceCodes(data) {
    const result = await this.paybillReferenceCodesGateway.search(data, '');

    return {
      filteredData: result,
    };
  }

  // Old Paybill Payment Service
  async searchTransactionLogsGateway(data) {
    const result = await this.paybillTransactionLogsGateway.search(data, ' order by timestamp desc ');
    return {
      filteredData: result,
    };
  }

  async searchArchiveAuditLogs(data) {
    const result = await this.ArchivedAuditLogs.search(data, ' order by createdAt desc ');
    return {
      filteredData: result,
    };
  }

  async searchArchiveTransactionLogs(data) {
    const result = await this.ArchivedTransactionLogs.search(data, ' order by timestamp desc ');
    const filteredData = await this.reportTransformation(result, 'archive');
    return {
      filteredData,
    };
  }

  async searchArchiveSwipeLogs(data) {
    const digitalPortalTransaction = await this.ArchivedSwipeLogs.search(data, ' order by date desc ');
    const filteredData = digitalPortalTransaction.map((transaction) =>
      voucherLogs({
        ...transaction,
      })
    );
    return {
      filteredData,
    };
  }

  async verifyDownload(data, httpInfo, currentUser) {
    try {
      const category = this.category(this.repoName, data.type);
      await this.auditLogs(
        {
          ...httpInfo,
        },
        currentUser,
        category
      );
      return {
        authorized: true,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}

module.exports = Archive;
