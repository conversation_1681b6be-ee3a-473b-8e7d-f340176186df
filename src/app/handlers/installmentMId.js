const Service = require('../Service');
const installmentMIdUseCases = require('../../domain/installmentMid/useCases');

class InstallmentMid extends Service {
  constructor(container) {
    const { installmentMidRepository, midRepository } = container;
    super(installmentMidRepository, container);
    this.midRepository = midRepository;
  }

  // Search for User, Roles and Provider Management
  async searchDataWithCursors(data) {
    let allIds, filteredData;
    const cursors = [''];
    if (Object.keys(data.filter).length <= 0) {
      filteredData = await this.repository.listAll(data);
      allIds = await this.repository.getAllBankTerm();
    } else {
      const searchData = await this.repository.searchFilter(data);
      allIds = await this.repository.searchFilterNoPaginate(data);
      filteredData = [];
      const filteredCount = searchData.count;
      for (let i = 0; i < data.pagination.limit; i += 1) {
        if (
          typeof searchData[i] !== 'undefined' &&
          (data.pagination.limit >= filteredCount || data.pagination.limit <= filteredCount)
        ) {
          filteredData.push(searchData[i]);
        }
      }
    }
    const filterCount = filteredData.count;
    if (filterCount <= 0) {
      return { filteredData: [], count: 0, cursors: [''] };
    }
    for (let i = 1; i < Math.ceil(allIds.count / data.pagination.limit); i += 1) {
      const installmentCursor = allIds[i * data.pagination.limit - 1];
      cursors.push(
        JSON.stringify({
          bank: { S: installmentCursor.bank },
          term: { S: installmentCursor.term },
        })
      );
    }
    return {
      filteredData,
      count: allIds.count,
      cursors,
    };
  }

  async searchInstallment(data) {
    try {
      return await this.searchDataWithCursors(data);
    } catch (error) {
      this.logger.error('Search MID', error.message);
      throw error;
    }
  }

  async listInstallment() {
    try {
      const installments = await this.repository.getAll();
      const banks = installments.map((data) => data.bank);
      return banks.filter((elem, pos) => banks.indexOf(elem) === pos);
    } catch (error) {
      this.logger.error('Search MID', error.message);
      throw error;
    }
  }

  async createInstallment(data, httpInfo, currentUser) {
    try {
      // Validate data
      const installmentDetails = installmentMIdUseCases.create({
        ...data,
      });
      this.logger.info({
        message: 'Create Installment Merchant Id Details',
        payload: JSON.stringify(installmentDetails),
      });
      const { bank, term } = installmentDetails;
      // Business Validation
      const termString = term.toString();
      const checkIfExist = await this.repository.getUniqueBankTerm(bank, termString);
      if (checkIfExist.count > 0) {
        throw this.errors.installmentMIdUniqueness();
      }
      // Insert data
      return await this.create(installmentDetails, httpInfo, currentUser);
    } catch (error) {
      this.logger.info({
        message: 'Create Installment Merchant Id Error',
        payload: JSON.stringify(error),
      });
      throw error;
    }
  }

  async updateInstallment(data, where, httpInfo, currentUser) {
    try {
      // Validate data
      const installmentDetails = installmentMIdUseCases.update({
        bank: where.bank,
        term: where.term,
        ...data,
      });
      this.logger.info({
        message: 'Update Installment Merchant Id Details',
        payload: JSON.stringify(installmentDetails),
      });

      // Business Validation
      const checkIfExist = await this.repository.getUniqueBankTerm(installmentDetails);
      if (checkIfExist.count > 1) {
        throw this.errors.installmentMIdUniqueness();
      }

      const oldValue = await this.repository.getUniqueBankTerm(where.bank, where.term);
      // Update Data
      delete installmentDetails.bank;
      return await this.update(installmentDetails, where, httpInfo, currentUser, oldValue[0]);
    } catch (error) {
      this.logger.info({
        message: 'Update Installment Merchant Id Error',
        payload: JSON.stringify(error),
      });
      throw error;
    }
  }

  async deleteInstallment(data, where, httpInfo, currentUser) {
    try {
      const [installment] = await this.repository.getUniqueBankTerm(where.bank, where.term);
      if (typeof installment === 'undefined' || installment.count < 1) {
        throw this.errors.notFound();
      }
      const { bank, term } = installment;
      const inUseInstallment = await this.midRepository.getInstallmentBankName(bank, term);
      if (inUseInstallment.count >= 1) {
        throw new Error(
          JSON.stringify({
            message: 'Could Not Delete Installment Bank',
            details: `Bank Name: ${bank} with a term of ${term} is already inused in the merchant details`,
          })
        );
      }
      return await this.delete(data, where, httpInfo, currentUser, installment);
    } catch (error) {
      this.logger.info({
        message: 'Delete Installment Merchant Id Error',
        payload: JSON.stringify(error),
      });
      throw error;
    }
  }

  async downloadInstallment(data, httpInfo, currentUser) {
    try {
      const installments = await this.searchDataWithCursors(data);
      const category = this.category(this.repoName, 'download');
      await this.auditLogs(
        {
          ...httpInfo,
        },
        currentUser,
        category
      );
      return installments;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}

module.exports = InstallmentMid;
