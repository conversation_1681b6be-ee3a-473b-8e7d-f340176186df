const Service = require('../Service');
const voucherLogs = require('../../domain/voucher/useCases/voucherTransaction');

class Voucher extends Service {
  constructor(container) {
    const { digitalPortalTransactionRepository, logger } = container;
    super(digitalPortalTransactionRepository, container);
    this.logger = logger;
  }

  static async voucherFilterData(transactions) {
    return transactions.map((transaction) =>
      voucherLogs({
        ...transaction,
      })
    );
  }

  async contentGcashSearch(data) {
    try {
      const payload = data;
      payload.filter.paymentGateway = 'gcash';
      const digitalPortalTransaction = await this.repository.searchFilter(payload);
      const filteredData = await Voucher.voucherFilterData(digitalPortalTransaction);
      return {
        filteredData,
        lastKey: JSON.stringify(digitalPortalTransaction.lastKey),
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async contentFraudSearch(data) {
    try {
      const digitalPortalTransaction = await this.repository.searchFilter(data);
      const filteredData = await Voucher.voucherFilterData(digitalPortalTransaction);
      return {
        filteredData,
        lastKey: JSON.stringify(digitalPortalTransaction.lastKey),
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
module.exports = Voucher;
