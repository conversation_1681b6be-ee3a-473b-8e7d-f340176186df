const fs = require('fs');
const path = require('path');
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const { Upload } = require('@aws-sdk/lib-storage');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const csv = require('csvtojson');
// const sftp = require('sftp-node');
const SftpClient = require('ssh2-sftp-client');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const Service = require('../Service');
const cardType = require('../../domain/transactions/cardType');
const accountTypes = require('../../domain/transactions/accountTypes');
const bulkSearchUsesCases = require('../../domain/transactions/useCases/bulksearch');
const { injectChannelName, withRefundData } = require('../../infra/utils/aggregates');

class Reports extends Service {
  constructor(container) {
    const {
      channelRepository,
      transactionRepository,
      // snapshotRepository,
      // generatedReportRepository,
      DateHelper,
      logger,
      config,
      authClient,
      uploadFile,
      batchFileRepository,
      // recurringTransactionRepository,
      payByLinkRepository,
      refundRepository,
      settlementRepository,
    } = container;
    super(transactionRepository, container);
    this.dateHelper = DateHelper;
    this.channelRepository = channelRepository;
    // this.snapshotRepository = snapshotRepository;
    // this.generatedReportRepository = generatedReportRepository;
    this.logger = logger;
    this.config = config;
    this.authClient = authClient;
    this.uploadFile = uploadFile;
    this.retries = 0;
    this.batchFileRepository = batchFileRepository;
    this.refundRepository = refundRepository;
    this.settlementRepository = settlementRepository;
    // this.recurringTransRepository = recurringTransactionRepository;
    this.payByLinkRepository = payByLinkRepository;
  }

  static async deleteFile(filePath) {
    if (!fs.existsSync(filePath)) {
      return;
    }

    fs.access(filePath, fs.F_OK, (err) => {
      if (!err) {
        fs.unlinkSync(filePath);
      }
    });
  }

  static async sendDailyEmail(recipient, patternId, date, url) {
    await this.email({
      to: recipient,
      patternId,
      parameters: [
        {
          Name: 'DATE',
          Value: date,
        },
        {
          Name: 'URL_LINK',
          Value: `<![CDATA[${url}]]>`,
        },
      ],
    });
  }

  determineSearchType(filters = {}, pagination = {}) {
    // base cases:
    if (Object.keys(filters).includes(this.repository.hashKey)) {
      return 'transactionLogs';
    }

    const lastKey = pagination.lastKey && JSON.parse(pagination.lastKey);

    const getSameFilters = (repositoryFilter, dataFilter) =>
      dataFilter.filter((item) => repositoryFilter.includes(item));

    if (!lastKey) {
      const sameFilters = getSameFilters(this.settlementRepository.validFilters, Object.keys(filters));
      if (sameFilters.includes('status') && this.settlementRepository.validStatus.includes(filters['status'])) {
        return 'settlement';
      }
    } else {
      const settlementHashKey = this.settlementRepository.hashKey;
      const settlementRangeKey = this.settlementRepository.rangeKey;
      if (Object.keys(lastKey).includes(settlementHashKey) && Object.keys(lastKey).includes(settlementRangeKey)) {
        return 'settlement';
      }
    }

    return 'transactionLogs';
  }

  async searchTypeSearch(searchType = 'transactionLogs', filters = {}, pagination = {}) {
    let searchResult;
    if (searchType === 'settlement') {
      searchResult = await this.settlementRepository.searchFilter({ filter: filters, pagination });
      if (searchResult.count > 0) {
        // add metadata to searchResult array
        searchResult.searchType = 'settlement';
        return searchResult;
      }
    }

    if (searchType === 'transactionLogs' || (searchResult && searchResult.count === 0)) {
      searchResult = await this.repository.searchFilter({ filter: filters, pagination });
      searchResult.searchType = 'transactionLogs';
      return searchResult;
    }
  }

  async searchTransactionLogs(data) {
    const { filter = {}, pagination = {} } = data;
    const searchType = this.determineSearchType(filter, pagination);
    try {
      const firstSearchResult = await this.searchTypeSearch(searchType, filter, pagination);
      if (!firstSearchResult || firstSearchResult.count === 0) {
        return { filteredData: [], count: 0, lastKey: null };
      }

      const isSettlementFirst = searchType === 'settlement';
      const secondaryRepo = isSettlementFirst ? this.repository : this.settlementRepository;
      const secondaryPagination = isSettlementFirst ? { limit: 1 } : {};

      const allPromises = [];
      // set chunk size to avoid throttling; might see issues for pagination limit = 100+ and promise.allSettled
      const CHUNK_SIZE = process.env.CHUNK_SIZE || 20;

      for (let i = 0; i < firstSearchResult.length; i += CHUNK_SIZE) {
        const chunk = firstSearchResult.slice(i, i + CHUNK_SIZE);
        const chunkPromises = chunk.map((item) =>
          secondaryRepo.searchFilter({
            filter: { ...filter, paymentId: item.paymentId },
            pagination: secondaryPagination,
          })
        );

        const chunkResults = await Promise.allSettled(chunkPromises);
        allPromises.push(...chunkResults);
      }

      const fulfilledResults = allPromises
        .filter((result) => result.status === 'fulfilled' && result.value.count > 0)
        .flatMap((result) => result.value); // Use flatMap to flatten the results into a single array

      // Mapping TransactionLogs to each settlement
      let aggregateResult = [];
      if (isSettlementFirst) {
        const transLogsMap = new Map(fulfilledResults.map((fr) => [fr.paymentId, fr]));

        aggregateResult = firstSearchResult.map((settlement) => ({
          ...transLogsMap.get(settlement.paymentId),
          settlement,
        }));
      } else {
        const settlementsMap = new Map();
        for (const settlement of fulfilledResults) {
          if (!settlementsMap.has(settlement.paymentId)) {
            settlementsMap.set(settlement.paymentId, []);
          }
          settlementsMap.get(settlement.paymentId).push(settlement);
        }

        aggregateResult = firstSearchResult.flatMap((transactionLog) => {
          const matchingSettlements = settlementsMap.get(transactionLog.paymentId);
          if (!matchingSettlements) {
            return [transactionLog]; // Return transaction log as is if no settlements found
          }
          return matchingSettlements.map((settlement) => ({
            ...transactionLog,
            settlement,
          }));
        });
      }

      return {
        filteredData: aggregateResult,
        count: aggregateResult.length,
        lastKey: JSON.stringify(firstSearchResult.lastKey),
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async search(data) {
    try {
      const result = await this.searchDataV2(data);
      const injectedData = await withRefundData(
        result.filteredData,
        async (data) => this.refundRepository.getByPaymentAndTransactionId(data),
        data.filter
      );

      return { ...result, filteredData: injectedData };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async failedReports(data) {
    try {
      const payload = data;
      payload.filter.status = 'POSTING_FAILED';
      const res = await this.searchDataForReport(payload);

      const injectedResult = await injectChannelName(res, async (channelIds) =>
        this.channelRepository.getBatch(channelIds)
      );

      return injectedResult;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async billing(data) {
    try {
      const payload = data;
      if (!payload.filter.status) {
        payload.filter.status = ['POSTED', 'POSTING_FAILED', 'POSTED_LUKE'];
      }
      const res = await this.searchDataForReport(payload);

      const injectedResult = await injectChannelName(res, async (channelIds) =>
        this.channelRepository.getBatch(channelIds)
      );
      return injectedResult;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async revenueWireline(data) {
    try {
      const transactions = await this.postedAndFailedReport(data);
      const filteredData = await this.reportTransformation(transactions.filteredData, 'transactionLogs');
      return {
        lastKey: JSON.stringify(transactions.lastKey),
        filteredData,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async raWirelessMonthly(data) {
    try {
      let transactions = null;
      transactions = await this.repository.revenueWirelessMonthlyList(data);
      const filteredData = await this.reportTransformation(transactions, 'transactionLogs');
      return {
        lastKey: JSON.stringify(transactions.lastKey),
        filteredData,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async gateway(data) {
    try {
      let transactions = null;
      if (Object.keys(data.filter).length <= 0) {
        transactions = await this.repository.postedAndFailedList(data);
      } else {
        transactions = await this.repository.postedAndFailedFilter(data);
      }
      const filteredData = await this.reportTransformation(transactions, 'transactionLogs');
      return {
        lastKey: JSON.stringify(transactions.lastKey),
        filteredData,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async transactionCount(data) {
    try {
      const { channelId, range } = data.filter;
      const transactions = await this.repository.getTransactionCount(channelId, range);
      return {
        count: transactions.count,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async collection(args) {
    try {
      const { startMonth, endMonth } = args.where;
      const months = this.dateHelper.monthsCovered(startMonth, endMonth);
      if (args.type === 'company') {
        return await this.snapshotRepository.transactionsByMonth(months, 'company');
      }
      if (args.type === 'mid') {
        return await this.snapshotRepository.transactionsByMonth(months, 'mid');
      }
      return [];
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async treasuryTotalByYear(args) {
    try {
      const { filter, reportType, year, months } = args;
      const result = {};
      const transactionSnapshot = await this.snapshotRepository.getSnapshotByMonth('transactions', months, year);
      await transactionSnapshot.forEach(async (snapshot) => {
        const payloadSnapshot = JSON.parse(snapshot.payload);
        if (typeof payloadSnapshot[reportType] !== 'undefined') {
          const payload = payloadSnapshot[reportType];
          Object.keys(payload).forEach((key) => {
            let cardTypeData;
            if (reportType === 'payloadCardBrand') {
              const splitKeys = key.split('-');
              cardTypeData = cardType[splitKeys[0] + splitKeys[1]];
            } else {
              cardTypeData = cardType[key];
            }
            // Payload Credit Debit & PAyload Card Brand
            if (typeof cardTypeData !== 'undefined') {
              const resultPayload = payload[key];
              const itemKey = `${cardTypeData.cardType}-${cardTypeData.codeType}-${payload[key].cardBrand}-${snapshot.year}`;
              if (typeof filter.accountType !== 'undefined') {
                if (filter.accountType === cardTypeData.codeType) {
                  if (typeof result[itemKey] !== 'undefined') {
                    result[itemKey].count += resultPayload.transCount;
                    result[itemKey].amount += resultPayload.transAmount;
                  } else {
                    result[itemKey] = {
                      ...cardTypeData,
                      accountType: accountTypes[cardTypeData.codeType],
                      year: snapshot.year,
                      count: resultPayload.transCount,
                      amount: resultPayload.transAmount,
                      cardBrand: resultPayload.cardBrand,
                    };
                  }
                }
              } else if (typeof result[itemKey] !== 'undefined') {
                result[itemKey].count += resultPayload.transCount;
                result[itemKey].amount += resultPayload.transAmount;
              } else {
                result[itemKey] = {
                  ...cardTypeData,
                  accountType: accountTypes[cardTypeData.codeType],
                  year: snapshot.year,
                  count: resultPayload.transCount,
                  amount: resultPayload.transAmount,
                  cardBrand: resultPayload.cardBrand,
                };
              }
              // GCASH
            } else if (payload[key].name === 'GCash') {
              const resultPayload = payload[key];
              const itemKey = `${payload[key].name}-${payload[key].accountType}`;
              if (typeof filter.accountType !== 'undefined') {
                if (filter.accountType === key) {
                  if (typeof result[itemKey] !== 'undefined') {
                    result[itemKey].count += resultPayload.transCount;
                    result[itemKey].amount += resultPayload.transAmount;
                  } else {
                    result[itemKey] = {
                      name: payload[key].name,
                      accountType: resultPayload.accountType,
                      month: snapshot.month,
                      year: snapshot.year,
                      count: resultPayload.transCount,
                      amount: resultPayload.transAmount,
                    };
                  }
                }
              } else if (typeof result[itemKey] !== 'undefined') {
                result[itemKey].count += resultPayload.transCount;
                result[itemKey].amount += resultPayload.transAmount;
              } else {
                result[itemKey] = {
                  name: payload[key].name,
                  accountType: resultPayload.accountType,
                  month: snapshot.month,
                  year: snapshot.year,
                  count: resultPayload.transCount,
                  amount: resultPayload.transAmount,
                };
              }
              // xendit
            } else if (reportType === 'payloadXenditEwallet') {
              const xenditEwalletPayload = payload[key];
              const itemKey = `Xendit-${null}-${xenditEwalletPayload.transCount}`;

              result[itemKey] = {
                name: xenditEwalletPayload.name,
                paymentType: xenditEwalletPayload.paymentType,
                paymentMethod: Reports.getPaymentMethodDisplayName(xenditEwalletPayload.paymentMethod),
                month: snapshot.month,
                year: snapshot.year,
                count: xenditEwalletPayload.transCount,
                amount: xenditEwalletPayload.transAmount,
              };
            } else if (reportType === 'payloadXenditDirectDebit') {
              const xenditDirectDebitPayload = payload[key];
              const itemKey = `Xendit-${null}-${xenditDirectDebitPayload.transCount}`;

              result[itemKey] = {
                name: xenditDirectDebitPayload.name,
                paymentType: xenditDirectDebitPayload.paymentType,
                paymentMethod: Reports.getPaymentMethodDisplayName(xenditDirectDebitPayload.paymentMethod),
                month: snapshot.month,
                year: snapshot.year,
                count: xenditDirectDebitPayload.transCount,
                amount: xenditDirectDebitPayload.transAmount,
              };
            }
          });
        }
      });
      return result;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async treasuryYTD(args) {
    try {
      const { filter, reportType } = args;
      const dataOfCurrentYear = {};
      const result = [];
      const previousYears = [];
      const monthsCovered = this.dateHelper.monthsCovered(filter.month.start, filter.month.end);
      const months = monthsCovered.map((date) => date.month);
      const transactionSnapshot = await this.snapshotRepository.getSnapshotByMonth('transactions', months, filter.year);
      await transactionSnapshot.forEach(async (snapshot) => {
        const payloadSnapshot = JSON.parse(snapshot.payload);
        if (typeof payloadSnapshot[reportType] !== 'undefined') {
          const payload = payloadSnapshot[reportType];
          Object.keys(payload).forEach((key) => {
            let cardTypeData;
            if (reportType === 'payloadCardBrand') {
              const splitKeys = key.split('-');
              cardTypeData = cardType[splitKeys[0] + splitKeys[1]];
            } else {
              const keyType = key.replace('-', '');
              cardTypeData = cardType[keyType];
            }
            // Payload Credit Debit & PAyload Card Brand
            if (typeof cardTypeData !== 'undefined') {
              const resultPayload = payload[key];
              if (typeof filter.accountType !== 'undefined') {
                if (filter.accountType === cardTypeData.codeType) {
                  dataOfCurrentYear[
                    `${cardTypeData.cardType}-${cardTypeData.codeType}-${payload[key].cardBrand}-${snapshot.month}`
                  ] = {
                    ...cardTypeData,
                    accountType: accountTypes[cardTypeData.codeType],
                    gatewayProcessor: resultPayload?.gatewayProcessor || null,
                    month: snapshot.month,
                    year: snapshot.year,
                    count: resultPayload.transCount,
                    amount: resultPayload.transAmount,
                    cardBrand: resultPayload.cardBrand,
                  };
                }
              } else {
                dataOfCurrentYear[
                  `${cardTypeData.cardType}-${cardTypeData.codeType}-${payload[key].cardBrand}-${snapshot.month}`
                ] = {
                  ...cardTypeData,
                  accountType: accountTypes[cardTypeData.codeType],
                  gatewayProcessor: resultPayload?.gatewayProcessor || null,
                  month: snapshot.month,
                  year: snapshot.year,
                  count: resultPayload.transCount,
                  amount: resultPayload.transAmount,
                  cardBrand: resultPayload.cardBrand,
                };
              }
              // GCASH
            } else if (payload[key].name === 'GCash') {
              const resultPayload = payload[key];
              if (typeof filter.accountType !== 'undefined') {
                if (filter.accountType === key) {
                  dataOfCurrentYear[`${payload[key].name}-${payload[key].accountType}-${payload[key].transCount}`] = {
                    name: payload[key].name,
                    accountType: resultPayload.accountType,
                    month: snapshot.month,
                    year: snapshot.year,
                    count: resultPayload.transCount,
                    amount: resultPayload.transAmount,
                  };
                }
              } else {
                dataOfCurrentYear[`${payload[key].name}-${payload[key].accountType}-${payload[key].transCount}`] = {
                  name: payload[key].name,
                  accountType: resultPayload.accountType,
                  month: snapshot.month,
                  year: snapshot.year,
                  count: resultPayload.transCount,
                  amount: resultPayload.transAmount,
                };
              }
              // xendit
            } else if (reportType === 'payloadXenditEwallet') {
              const xenditEwalletPayload = payload[key];

              dataOfCurrentYear[`Xendit-${null}-${xenditEwalletPayload.transCount}`] = {
                name: xenditEwalletPayload.name,
                paymentType: xenditEwalletPayload.paymentType,
                paymentMethod: Reports.getPaymentMethodDisplayName(xenditEwalletPayload.paymentMethod),
                month: snapshot.month,
                year: snapshot.year,
                count: xenditEwalletPayload.transCount,
                amount: xenditEwalletPayload.transAmount,
              };
            } else if (reportType === 'payloadXenditDirectDebit') {
              const xenditDirectDebitPayload = payload[key];

              dataOfCurrentYear[`Xendit-${null}-${xenditDirectDebitPayload.transCount}`] = {
                name: xenditDirectDebitPayload.name,
                paymentType: xenditDirectDebitPayload.paymentType,
                paymentMethod: Reports.getPaymentMethodDisplayName(xenditDirectDebitPayload.paymentMethod),
                month: snapshot.month,
                year: snapshot.year,
                count: xenditDirectDebitPayload.transCount,
                amount: xenditDirectDebitPayload.transAmount,
              };
            }
          });
        }
      });
      const lastYear = await this.treasuryTotalByYear({
        filter,
        reportType,
        year: filter.year - 1,
        months,
      });
      const last2Years = await this.treasuryTotalByYear({
        filter,
        reportType,
        year: filter.year - 2,
        months,
      });
      Object.keys(lastYear).forEach((data) => {
        previousYears.push({
          ...lastYear[data],
        });
      });
      Object.keys(last2Years).forEach((data) => {
        previousYears.push({
          ...last2Years[data],
        });
      });
      Object.keys(dataOfCurrentYear).forEach((data) => {
        result.push(dataOfCurrentYear[data]);
      });
      return {
        filteredData: result,
        previousYears,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async monthlyTreasury(args) {
    try {
      const { filter, reportType } = args;
      const result = [];
      const startMonth = this.dateHelper.monthNumberConverted(filter.month.start, 'start', filter.year.start);
      const endMonth = this.dateHelper.monthNumberConverted(filter.month.end, 'end', filter.year.end);
      const transactionSnapshot = await this.snapshotRepository.getSnapshotByDateRange(
        'transactions',
        startMonth,
        endMonth
      );
      const returnValue = {};
      let type, isEqualValue, cardBrandData;

      await transactionSnapshot.forEach(async (snapshot) => {
        const payloadSnapshot = JSON.parse(snapshot.payload) || {};

        if (payloadSnapshot[reportType] !== undefined) {
          Object.keys(payloadSnapshot[reportType]).forEach((key) => {
            let cardTypeData;
            let cardtypeName = {
              G: 'Globe',
              I: 'Innove',
              B: 'Bayan',
              N: 'Innove',
              // add another card type name for xendit?
            };
            const splitKeys = key.split('-');
            if (reportType === 'payloadCardBrand') {
              cardTypeData = cardType[splitKeys[0] + splitKeys[1]];
              if (typeof cardtypeName[splitKeys[0]] === 'undefined') {
                const keyType = key.charAt(0);
                cardtypeName = cardtypeName[keyType];
              } else {
                cardtypeName = cardtypeName[splitKeys[0]];
              }
            } else {
              cardTypeData = cardType[key.replace('-', '')];
              if (typeof cardtypeName[splitKeys[0]] === 'undefined') {
                const keyType = key.charAt(0);
                cardtypeName = cardtypeName[keyType];
              } else {
                cardtypeName = cardtypeName[splitKeys[0]];
              }
            }
            if (typeof cardTypeData !== 'undefined') {
              const resultPayload = payloadSnapshot[reportType][key];
              if (reportType === 'payloadCreditDebit') {
                if (cardTypeData.codeType === 'N') {
                  type = `${cardTypeData.cardType}-I`;
                } else {
                  type = `${cardTypeData.cardType}-${cardTypeData.codeType}`;
                }
              } else if (cardTypeData.codeType === 'N') {
                type = `${cardTypeData.cardType}-I-${payloadSnapshot[reportType][key].cardBrand}`;
                cardBrandData = payloadSnapshot[reportType][key].cardBrand;
              } else {
                type = `${cardTypeData.cardType}-${cardTypeData.codeType}-${payloadSnapshot[reportType][key].cardBrand}`;
                cardBrandData = payloadSnapshot[reportType][key].cardBrand;
              }
              if (cardTypeData.codeType === 'N') {
                isEqualValue = filter.accountType === 'I';
              } else {
                isEqualValue = filter.accountType === cardTypeData.codeType;
              }
              if (typeof filter.accountType !== 'undefined') {
                if (isEqualValue === true) {
                  if (type in returnValue) {
                    returnValue[type].count += resultPayload.transCount;
                    returnValue[type].amount += resultPayload.transAmount;
                    returnValue[type].startMonth = filter.month.start;
                    returnValue[type].startYear = filter.year.start;
                    returnValue[type].endMonth = filter.month.end;
                    returnValue[type].endYear = filter.year.end;
                    returnValue[type].name = cardTypeData.cardType;
                    returnValue[type].cardType = cardTypeData.cardType;
                    returnValue[type].accountType = cardtypeName;
                    returnValue[type].cardBrand = cardBrandData;
                    returnValue[type].paymentType = resultPayload.paymentType;
                    returnValue[type].paymentGateway = resultPayload?.gatewayProcessor || null;
                  } else {
                    returnValue[type] = {
                      count: resultPayload.transCount,
                      amount: resultPayload.transAmount,
                      startMonth: filter.month.start,
                      startYear: filter.year.start,
                      endMonth: filter.month.end,
                      endYear: filter.year.end,
                      name: cardTypeData.cardType,
                      cardType: cardTypeData.cardType,
                      accountType: cardtypeName,
                      cardBrand: cardBrandData,
                      paymentType: resultPayload.paymentType,
                      paymentGateway: resultPayload?.gatewayProcessor || null,
                    };
                  }
                }
              } else if (type in returnValue) {
                // handle xendit cc_dc display name
                if (cardBrandData === 'cc_dc') cardBrandData = 'Online Credit Card';
                returnValue[type].count += resultPayload.transCount;
                returnValue[type].amount += resultPayload.transAmount;
                returnValue[type].startMonth = filter.month.start;
                returnValue[type].startYear = filter.year.start;
                returnValue[type].endMonth = filter.month.end;
                returnValue[type].endYear = filter.year.end;
                returnValue[type].name = cardTypeData.cardType;
                returnValue[type].cardType = cardTypeData.cardType;
                returnValue[type].accountType = cardtypeName;
                returnValue[type].cardBrand = cardBrandData;
                returnValue[type].paymentType = resultPayload.paymentType;
                returnValue[type].paymentGateway = resultPayload?.gatewayProcessor || null;
              } else {
                // handle xendit cc_dc display name
                if (cardBrandData === 'cc_dc') cardBrandData = 'Online Credit Card';
                returnValue[type] = {
                  count: resultPayload.transCount,
                  amount: resultPayload.transAmount,
                  startMonth: filter.month.start,
                  startYear: filter.year.start,
                  endMonth: filter.month.end,
                  endYear: filter.year.end,
                  name: cardTypeData.cardType,
                  cardType: cardTypeData.cardType,
                  accountType: cardtypeName,
                  cardBrand: cardBrandData,
                  paymentType: resultPayload.paymentType,
                  paymentGateway: resultPayload?.gatewayProcessor || null,
                };
              }
            } else if (reportType === 'payloadGcash') {
              const resultPayload = payloadSnapshot[reportType][key];
              if (key === 'N') {
                type = 'I';
              } else {
                type = key;
              }
              cardBrandData = null;
              isEqualValue = filter.accountType === type;
              if (typeof filter.accountType !== 'undefined') {
                if (isEqualValue === true) {
                  if (type in returnValue) {
                    returnValue[type].count += resultPayload.transCount;
                    returnValue[type].amount += resultPayload.transAmount;
                    returnValue[type].startMonth = filter.month.start;
                    returnValue[type].startYear = filter.year.start;
                    returnValue[type].endMonth = filter.month.end;
                    returnValue[type].endYear = filter.year.end;
                    returnValue[type].name = 'GCASH';
                    returnValue[type].cardType = 'GCASH';
                    returnValue[type].accountType = cardtypeName;
                    returnValue[type].cardBrand = cardBrandData;
                  } else {
                    returnValue[type] = {
                      count: resultPayload.transCount,
                      amount: resultPayload.transAmount,
                      startMonth: filter.month.start,
                      startYear: filter.year.start,
                      endMonth: filter.month.end,
                      endYear: filter.year.end,
                      name: 'GCASH',
                      cardType: 'GCASH',
                      accountType: cardtypeName,
                      cardBrand: cardBrandData,
                    };
                  }
                }
              } else if (type in returnValue) {
                returnValue[type].count += resultPayload.transCount;
                returnValue[type].amount += resultPayload.transAmount;
                returnValue[type].startMonth = filter.month.start;
                returnValue[type].startYear = filter.year.start;
                returnValue[type].endMonth = filter.month.end;
                returnValue[type].endYear = filter.year.end;
                returnValue[type].name = 'GCASH';
                returnValue[type].cardType = 'GCASH';
                returnValue[type].accountType = cardtypeName;
                returnValue[type].cardBrand = cardBrandData;
              } else {
                returnValue[type] = {
                  count: resultPayload.transCount,
                  amount: resultPayload.transAmount,
                  startMonth: filter.month.start,
                  startYear: filter.year.start,
                  endMonth: filter.month.end,
                  endYear: filter.year.end,
                  name: 'GCASH',
                  cardType: 'GCASH',
                  accountType: cardtypeName,
                  cardBrand: cardBrandData,
                };
              }
            } else if (reportType === 'payloadXenditEwallet') {
              const resultPayload = payloadSnapshot[reportType][key];
              type = key; // key contains ewallet paymentMethods (shopeepay, grabpay, paymaya)

              if (type in returnValue) {
                returnValue[type].count += resultPayload.transCount;
                returnValue[type].amount += resultPayload.transAmount;
                returnValue[type].startMonth = filter.month.start;
                returnValue[type].startYear = filter.year.start;
                returnValue[type].endMonth = filter.month.end;
                returnValue[type].endYear = filter.year.end;
                returnValue[type].name = 'XENDIT';
                returnValue[type].cardType = 'XENDIT';
                returnValue[type].accountType = null;
                returnValue[type].cardBrand = null;
                returnValue[type].paymentType = resultPayload.paymentType;
                returnValue[type].paymentMethod = Reports.getPaymentMethodDisplayName(resultPayload.paymentMethod);
                returnValue[type].paymentGateway = 'xendit';
              } else {
                returnValue[type] = {
                  count: resultPayload.transCount,
                  amount: resultPayload.transAmount,
                  startMonth: filter.month.start,
                  startYear: filter.year.start,
                  endMonth: filter.month.end,
                  endYear: filter.year.end,
                  name: 'XENDIT',
                  cardType: 'XENDIT',
                  accountType: null,
                  cardBrand: null,
                  paymentType: resultPayload.paymentType,
                  paymentMethod: Reports.getPaymentMethodDisplayName(resultPayload.paymentMethod),
                  paymentGateway: 'xendit',
                };
              }
            } else if (reportType === 'payloadXenditDirectDebit') {
              const resultPayload = payloadSnapshot[reportType][key];
              type = key; // key contains direct debit paymentMethods (bpi, ubp, rcbc)

              if (type in returnValue) {
                returnValue[type].count += resultPayload.transCount;
                returnValue[type].amount += resultPayload.transAmount;
                returnValue[type].startMonth = filter.month.start;
                returnValue[type].startYear = filter.year.start;
                returnValue[type].endMonth = filter.month.end;
                returnValue[type].endYear = filter.year.end;
                returnValue[type].name = 'XENDIT';
                returnValue[type].cardType = 'XENDIT';
                returnValue[type].accountType = null;
                returnValue[type].cardBrand = null;
                returnValue[type].paymentType = resultPayload.paymentType;
                returnValue[type].paymentMethod = Reports.getPaymentMethodDisplayName(resultPayload.paymentMethod);
                returnValue[type].paymentGateway = 'xendit';
              } else {
                returnValue[type] = {
                  count: resultPayload.transCount,
                  amount: resultPayload.transAmount,
                  startMonth: filter.month.start,
                  startYear: filter.year.start,
                  endMonth: filter.month.end,
                  endYear: filter.year.end,
                  name: 'XENDIT',
                  cardType: 'XENDIT',
                  accountType: null,
                  cardBrand: null,
                  paymentType: resultPayload.paymentType,
                  paymentMethod: Reports.getPaymentMethodDisplayName(resultPayload.paymentMethod),
                  paymentGateway: 'xendit',
                };
              }
            }
          });
        }
      });

      Object.keys(returnValue).forEach((key) => {
        result.push({
          name: returnValue[key].cardType,
          accountType: returnValue[key].accountType,
          startMonth: filter.month.start,
          startYear: filter.year.start,
          endMonth: filter.month.end,
          endYear: filter.year.end,
          count: returnValue[key].count,
          amount: returnValue[key].amount,
          cardType: returnValue[key].cardType,
          cardBrand: returnValue[key].cardBrand,
          paymentType: returnValue[key].paymentType,
          paymentMethod: returnValue[key].paymentMethod,
          paymentGateway: returnValue[key]?.paymentGateway || null,
        });
      });

      return {
        filteredData: result,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  // map payment method display name
  // currently only xendit transactions uses "paymentMethod" on reports
  static getPaymentMethodDisplayName(value) {
    const displayName = {
      // ewallet
      shopeepay: 'Shopee Pay',
      grabpay: 'Grabpay',
      paymaya: 'Maya',
      // direct debit banks
      bpi: 'BPI',
      ubp: 'Unionbank',
      rcbc: 'RCBC',
      // credit card
      cc_dc: 'Online Credit Card',
    };

    if (value) {
      const mappedValue = Object.keys(displayName).find((name) => value === name);
      if (!mappedValue) {
        return value;
      }
      return displayName[mappedValue];
    }

    return value;
  }

  static async generateCSVFile(fileName, filePath, header, data) {
    const csvWriter = createCsvWriter({
      path: `${filePath}/${fileName}`,
      header,
    });
    const result = await csvWriter
      .writeRecords(data)
      .then(() => 'Succesfully Generate File')
      .catch((err) => {
        throw err;
      });
    return {
      result,
      status: true,
      fileName,
    };
  }

  async gCollectionSummaryEmail(urlLink, reportName) {
    const currentDate = new Date();
    const startDate = new Date();
    startDate.setTime(currentDate.getTime());
    startDate.setMonth(currentDate.getMonth() - 1);
    startDate.setDate(1);
    const month = startDate.getMonth() + 1;
    const year = startDate.getFullYear();

    const lastDay = new Date(year, month + 1, 0);
    const emailPatternId = this.config.notifPatternId.emails[this.repoName].collection;
    const period = `${this.dateHelper.monthNames()[month - 1]} 1 to ${lastDay.getDate()}, ${year}.`;

    const recipients = this.config.recipient.collection;

    recipients.map((recipient) => {
      // Sending of Email
      this.email({
        to: recipient,
        patternId: emailPatternId,
        parameters: [
          {
            Name: 'REPORT_NAME',
            Value: reportName,
          },
          {
            Name: 'PERIOD',
            Value: period,
          },
          {
            Name: 'URL_LINK',
            Value: `<![CDATA[${urlLink}]]>`,
          },
        ],
      });
      return true;
    });
  }

  async generateGCollectionReportFile() {
    try {
      const currentDate = new Date();
      const startMonth = new Date();
      startMonth.setMonth(startMonth.getMonth() - 1);
      startMonth.setHours(8, 0, 0, 0);
      startMonth.setDate(0);
      const endMonth = new Date();
      endMonth.setMonth(endMonth.getMonth() - 1);
      endMonth.setHours(31, 59, 59, 999);
      endMonth.setDate(30);
      const transactionSnapshot = await this.snapshotRepository.getSnapshotByDateRange(
        'transactions',
        startMonth.toISOString(),
        endMonth.toISOString()
      );
      const payloadMid = [];
      const payloadCompany = [];
      await transactionSnapshot.forEach((snapshot) => {
        const payload = JSON.parse(snapshot.payload);
        payloadMid.push({
          month: snapshot.month,
          payload: payload.payloadCMid,
        });
        payloadCompany.push({
          month: snapshot.month,
          payload: payload.payloadCCompany,
        });
      });
      const month = endMonth.getMonth() + 1;
      const monthNumber = `0${month}`;
      const year = endMonth.getFullYear();
      const monthlyReportDate = `${year}${monthNumber.slice(-2)}`;
      const filePath = './storage';
      // Company Report Generation
      const companyFileName = `collection-company-${monthlyReportDate}.csv`;
      const companyHeader = [
        { id: 'company', title: 'Company' },
        { id: 'depositoryBankAccount', title: 'Bank Depository Account No.' },
        { id: 'month', title: 'Month' },
        { id: 'transCount', title: 'Trans Count' },
        { id: 'transAmount', title: 'Trans Amount' },
        { id: 'bankCharge', title: 'Bank Charge' },
        { id: 'costCenter', title: 'Cost Center' },
      ];
      const companyYTD = {
        transCount: 0,
        transAmount: 0,
        bankCharge: 0,
      };
      const companyResult = [];
      await payloadCompany.forEach(async (company) => {
        const { payload } = company;
        await Object.keys(payload).forEach((key) => {
          companyResult.push({
            company: payload[key].name,
            depositoryBankAccount: payload[key].depositoryBankAccount,
            month: company.month,
            transCount: payload[key].transCount,
            transAmount: parseFloat(payload[key].transAmount).toFixed(2),
            bankCharge: parseFloat(payload[key].bankCharge).toFixed(2),
            costCenter: payload[key].costCenter,
          });
          companyYTD.transCount += payload[key].transCount;
          companyYTD.transAmount += payload[key].transAmount;
          companyYTD.bankCharge += payload[key].bankCharge;
        });
      });
      companyResult.push({
        company: 'Total',
        depositoryBankAccount: '',
        month: '',
        transCount: companyYTD.transCount,
        transAmount: parseFloat(companyYTD.transAmount).toFixed(2),
        bankCharge: parseFloat(companyYTD.bankCharge).toFixed(2),
        costCenter: '',
      });

      const cmpFileGenResult = await Reports.generateCSVFile(companyFileName, filePath, companyHeader, companyResult);
      this.logger.info(`${cmpFileGenResult.result} - ${cmpFileGenResult.fileName}`);

      // MID Report Generation
      const mIdFilename = `collection-mid-${monthlyReportDate}.csv`;
      const midHeader = [
        { id: 'mid', title: 'MID' },
        { id: 'company', title: 'Company' },
        { id: 'depositoryBankAccount', title: 'Bank Depository Account No.' },
        { id: 'month', title: 'Month' },
        { id: 'transCount', title: 'Trans Count' },
        { id: 'transAmount', title: 'Trans Amount' },
        { id: 'bankCharge', title: 'Bank Charge' },
        { id: 'costCenter', title: 'Cost Center' },
      ];
      const midTTD = {
        transCount: 0,
        transAmount: 0,
        bankCharge: 0,
      };
      const midResult = [];
      await payloadMid.forEach(async (mid) => {
        const { payload } = mid;
        await Object.keys(payload).forEach((key) => {
          midResult.push({
            mid: payload[key].name,
            company: payload[key].merchantCompany,
            depositoryBankAccount: payload[key].depositoryBankAccount,
            month: mid.month,
            transCount: payload[key].transCount,
            transAmount: parseFloat(payload[key].transAmount).toFixed(2),
            bankCharge: parseFloat(payload[key].bankCharge).toFixed(2),
            costCenter: payload[key].costCenter,
          });
          midTTD.transCount += payload[key].transCount;
          midTTD.transAmount += payload[key].transAmount;
          midTTD.bankCharge += payload[key].bankCharge;
        });
      });

      midResult.push({
        mid: 'Total',
        company: '',
        depositoryBankAccount: '',
        month: '',
        transCount: midTTD.transCount,
        transAmount: parseFloat(midTTD.transAmount).toFixed(2),
        bankCharge: parseFloat(midTTD.bankCharge).toFixed(2),
        costCenter: '',
      });

      const midFileGenResult = await Reports.generateCSVFile(mIdFilename, filePath, midHeader, midResult);
      this.logger.info(`${midFileGenResult.result} - ${midFileGenResult.fileName}`);

      // S3 File Upload
      const midUploadResponse = await this.uploadFile.S3UploadFile(mIdFilename, './storage/', 'gateway-collection');
      if (typeof midUploadResponse.status !== 'undefined' && midUploadResponse.status === true) {
        this.logger.info(`File Successfully Uploaded to S3 Bucket - ${mIdFilename}`);
      }
      const companyUploadResponse = await this.uploadFile.S3UploadFile(
        companyFileName,
        './storage/',
        'gateway-collection'
      );
      if (typeof companyUploadResponse.status !== 'undefined' && companyUploadResponse.status === true) {
        this.logger.info(`File Successfully Uploaded to S3 Bucket - ${companyFileName}`);
      }

      await this.generatedReportRepository.batchPut([
        {
          id: this.uuid.create(),
          year: currentDate.getFullYear(),
          fileName: mIdFilename,
          filePath,
          fileS3Path: 'gateway-collection',
          type: 'collectionmid',
          createdAt: new Date().toISOString(),
        },
        {
          id: this.uuid.create(),
          year: currentDate.getFullYear(),
          fileName: companyFileName,
          filePath,
          fileS3Path: 'gateway-collection',
          type: 'collectioncompany',
          createdAt: new Date().toISOString(),
        },
      ]);

      this.gCollectionSummaryEmail(midUploadResponse.url, 'Gateway Collection Summary MID');
      this.gCollectionSummaryEmail(companyUploadResponse.url, 'Gateway Collection Summary Company');

      this.uploadToSFTPServer({
        fileName: mIdFilename,
      });
      this.uploadToSFTPServer({
        fileName: companyFileName,
      });
    } catch (error) {
      this.logger.error(error);
    }
  }

  async appendDataToFile(text, filePath) {
    if (text.length !== 0) {
      fs.appendFile(filePath, `${text.join('\r\n')}\r\n`, (err) => {
        if (err) this.logger.error("Couldn't append the data:", err);
        this.logger.info('The data was appended to file!');
      });
    } else {
      this.logger.info('The data was empty and did not append to file!');
    }
  }

  async deleteExistingFile(filePath) {
    // Delete Existing Report in storage
    if (fs.existsSync(filePath)) {
      fs.unlink(filePath, (err) => {
        if (err) {
          this.logger.error(`Error in deleting the file: ${filePath}`);
        }
      });
    }
  }

  static async dataToCSVRows(transactions, header) {
    return transactions.map((transaction) => {
      const report = [];
      Object.keys(header).forEach((key) => {
        report.push(transaction[key]);
      });
      return report.join(',');
    });
  }

  async generateGCreditCardReportFile() {
    try {
      const previousDate = new Date();
      previousDate.setDate(0);
      const month = previousDate.getMonth() + 1;
      const monthNumber = `0${previousDate.getMonth() + 1}`;
      const year = previousDate.getFullYear();
      const monthlyReportDate = `${month}-${year}`;
      const fileName = `gateway-credit-card-${year}${monthNumber.slice(-2)}.csv`;
      const filePath = `./storage/${fileName}`;
      const header = {
        recordDate: 'Record Date Time',
        channelName: 'Channel Name',
        depositoryBank: 'Depository Bank',
        depositoryBankAccountNo: 'Depository Bank Account No.',
        merchantCompany: 'Merchant Company',
        mid: 'MID',
        reference: 'Reference No.',
        transId: 'Transaction ID',
        authCode: 'Auth Code',
        creditCardNumber: 'Credit Card No.',
        creditCardHolderName: 'CC Holder Name',
        accountNumber: 'Account No.',
        creditCardBank: 'CC Bank',
        creditCardCountry: 'CC Country',
        creditCardType: 'CC Type',
        paymentMethod: 'Payment Method',
        amountCurrency: 'Currency',
        grossAmount: 'Gross Amount',
        bankDiscount: 'Bank Discount',
        withholdingtax: 'Withholding Tax',
        netAmount: 'Net Amount',
        status: 'Payment Status',
        threeDFlag: '3D Secure Flag',
        costCenter: 'Cost Center',
      };
      await Reports.deleteFile(filePath);
      await this.appendDataToFile([Object.values(header).join(',')], filePath);
      const creditCardReport = async (startKey) => {
        const transactions = await this.repository.gatewayCCMontlyReport(monthlyReportDate, startKey);
        const transactionTransformed = await this.reportTransformation(transactions, 'transactionLogs');
        const csvRows = await Reports.dataToCSVRows(transactionTransformed, header);
        await this.appendDataToFile(csvRows, filePath);
        const { lastKey } = transactions;

        if (typeof lastKey === 'object' && lastKey !== null) {
          return creditCardReport(lastKey);
        }
        return '';
      };

      await creditCardReport('');
      this.logger.info(
        `Done Generating Gateway Credit Card | File: ${fileName} | Path: ${filePath} | Timestamp: ${new Date()}`
      );
      const s3BucketPath = `gateway-credit-card/${fileName}`;
      const readStream = await fs.createReadStream(filePath);
      let uploadResult;
      try {
        let clientConfig = {
          region: process.env.AWS_REGION,
          httpOptions: { timeout: 10 * 120 * 1000 },
          forcePathStyle: true,
        };

        if (process.env.STAGE === 'local') {
          clientConfig = {
            ...clientConfig,
            endpoint: process.env.AWS_ENDPOINT,
          };
        }

        const s3Config = {
          client: new S3Client(clientConfig),
          params: {
            Bucket: process.env.S3_BUCKET_NAME,
            Key: s3BucketPath,
            Body: readStream,
          },
          queueSize: 10,
          partSize: 1024 * 1024 * 5,
          leavePartsOnError: false,
        };
        const streamUpload = new Upload(s3Config);
        uploadResult = await streamUpload.done();
      } catch (e) {
        throw new Error(`Failed to upload file to S3 - ${e}`);
      }

      if (typeof uploadResult.ETag !== 'undefined') {
        this.logger.info(`Successful to Upload in S3 Bucket - ${fileName}`);
        const urlparams = {
          Bucket: process.env.S3_BUCKET_NAME,
          Key: `${s3BucketPath}/${fileName}`,
        };
        let s3Config = {
          region: process.env.AWS_REGION,
          httpOptions: { timeout: 10 * 120 * 1000 },
          forcePathStyle: true,
        };
        if (process.env.STAGE === 'local') {
          s3Config = {
            ...s3Config,
            endpoint: process.env.AWS_ENDPOINT,
          };
        }
        const client = new S3Client(s3Config);
        const command = new PutObjectCommand(urlparams);
        const url = await getSignedUrl(client, command, { expiresIn: 14400 });
        await this.generatedReportRepository.add({
          id: this.uuid.create(),
          year: year.toString(),
          fileName,
          filePath,
          fileS3Path: 'gateway-credit-card',
          type: 'creditcard',
          createdAt: new Date().toISOString(),
        });

        const lastDay = new Date(year, month, 0);
        const emailPatternId = this.config.notifPatternId.emails[this.repoName].creditCard;
        const period = `${this.dateHelper.monthNames()[month - 1]} 1 to ${lastDay.getDate()}, ${year}.`;
        const recipients = this.config.recipient.creditCard;
        await recipients.forEach((recipient) => {
          // Sending of Email
          this.email({
            to: recipient,
            patternId: emailPatternId,
            parameters: [
              {
                Name: 'REPORT_NAME',
                Value: 'Gateway Credit Card Transaction Report',
              },
              {
                Name: 'PERIOD',
                Value: period,
              },
              {
                Name: 'URL_LINK',
                Value: `<![CDATA[${url}]]>`,
              },
            ],
          });
        });

        await this.uploadToSFTPServer({ fileName });
      } else {
        this.logger.error(`Failed to Upload in S3 Bucket - ${fileName}`);
      }
    } catch (error) {
      this.logger.error(error);
    }
  }

  async uploadToSFTPServer({ fileName }) {
    try {
      const fileToUploadPath = path.join(path.resolve(), 'storage', fileName);

      // Skip file dumping if it does not exist in storage directory
      if (!fs.existsSync(fileToUploadPath)) {
        this.logger.info(`File ${fileToUploadPath} does not exist`);
        return;
      }

      const sftpClient = new SftpClient();
      const sftpConnectionOption = {
        host: this.config.sftp.host,
        port: this.config.sftp.port,
        username: this.config.sftp.username,
        privateKey: fs.existsSync(this.config.sftp.private_key) ? fs.readFileSync(this.config.sftp.private_key) : '',
      };

      // Initialize connection
      await sftpClient.connect(sftpConnectionOption);

      const sftpRes = await sftpClient.put(fileToUploadPath, `${this.config.sftp.upload_path}${fileName}`);
      this.logger.info(`SFTP: ${sftpRes}`);

      // Close connection
      await sftpClient.end();
    } catch (err) {
      this.logger.error(err);
    }
  }

  async generateBillingReportFile() {
    try {
      const now = new Date();
      const yesterday = new Date(now.setDate(now.getDate() - 1));
      yesterday.setHours(8, 0, 0, 0);
      const today = new Date();
      today.setHours(8, 0, 0, 0);
      const fileName = `Globe-Bills-${this.dateHelper.formatDateMMDDYYYY(today)}.csv`;
      const filePath = `./storage/${fileName}`;
      const header = {
        recordDate: 'Payment Date',
        reference: 'Reference No.',
        postPaymentReferenceId: 'Manual OR',
        channelName: 'Merchant Name',
        transId: 'TransId',
        authCode: 'AuthCode',
        creditCardNumber: 'CreditCard No',
        creditCardBank: 'CreditCard Bank',
        creditCardCountry: 'CreditCard Country',
        creditCardType: 'Credit Card Type',
        paymentMethod: 'PaymentMethod',
        amountCurrency: 'Currency',
        amountValue: 'Amount',
        prodDesc: 'ProdDesc',
        ipAddress: 'IP Address',
        accountNumber: 'Account No',
        status: 'Status',
        threeDFlag: 'ThreeDFlag',
        refundAmount: 'Refund Amount',
        refundStatus: 'Refund Status',
      };

      await Reports.deleteFile(filePath);
      await this.appendDataToFile([Object.values(header).join(',')], filePath);
      const month = yesterday.getMonth() + 1;
      const day = yesterday.getDate();
      const year = yesterday.getFullYear();
      const dailyReportDate = `${month}-${day}-${year}`;

      const billingTransactions = async (startKey) => {
        const result = await this.repository.billingDailyReport(dailyReportDate, startKey);
        const transactionTransformed = await this.reportTransformation(result, 'transactionLogs');
        const csvRows = await Reports.dataToCSVRows(transactionTransformed, header);
        await this.appendDataToFile(csvRows, filePath);
        const { lastKey } = result;

        if (typeof lastKey === 'object' && lastKey !== null) {
          return billingTransactions(lastKey);
        }
        return '';
      };

      await billingTransactions('');

      this.logger.info(
        `Done Generating Billing Report | File: ${fileName} | Path: ${filePath} | Timestamp: ${new Date()}`
      );

      await this.uploadToSFTPServer({ fileName });

      // After SFTP Upload - Send an email via esb client
      const recipients = this.config.recipient?.billingNotif ?? [];
      recipients.forEach((recipient) => {
        this.email({
          to: recipient,
          patternId: 37281,
          parameters: [
            {
              Name: 'STATUS',
              Value: 'Success',
            },
            {
              Name: 'REPORT_FILE',
              Value: fileName,
            },
          ],
        });
      });
    } catch (err) {
      this.logger.error(err);
    }
  }

  async verifyDownload(data, httpInfo, currentUser) {
    try {
      const category = this.category(this.repoName, data.type);
      await this.auditLogs(
        {
          ...httpInfo,
        },
        currentUser,
        category
      );
      return {
        authorized: true,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async generateMonthlyWirelessPayTypeReport() {
    try {
      const previousDate = new Date();
      previousDate.setDate(0);
      const month = previousDate.getMonth() + 1;
      const year = previousDate.getFullYear();
      const monthlyReportDate = `${month}-${year}`;
      const mList = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];
      const dateNow = new Date();
      const fileName = `Revenue Wireless-PayType-${this.dateHelper.formatDateMMDDYYYY(dateNow)}.csv`;
      const filePath = `./storage/${fileName}`;
      const header = {
        transId: 'Payment Reference',
        createdAt: 'Payment Date',
        paymentTypeCode: 'Payment Type Code',
        qty: 'Qty',
        amountValue: 'Amount',
      };
      await Reports.deleteFile(filePath);
      let qtytotal = 0;
      let amounttotal = 0;
      await this.appendDataToFile([Object.values(header).join(',')], filePath);

      const wirelessMontlyReport = async (startKey) => {
        const transactions = await this.repository.raWirelessMonthlyReport(monthlyReportDate, startKey);
        qtytotal += transactions.count;
        const transactionTransformed = await this.reportTransformation(transactions, 'transactionLogs');
        transactionTransformed.forEach((key) => {
          amounttotal += parseFloat(key.amountValue);
        });
        const csvRows = await Reports.dataToCSVRows(transactionTransformed, header);
        await this.appendDataToFile(csvRows, filePath);
        const { lastKey } = transactions;

        if (typeof lastKey === 'object' && lastKey !== null) {
          return wirelessMontlyReport(lastKey);
        }
        return '';
      };

      await wirelessMontlyReport('');
      amounttotal.toFixed(2);
      const footer = {
        first: null,
        second: null,
        third: 'TOTAL',
        qtytotal,
        amounttotal,
      };
      await this.appendDataToFile([Object.values(footer).join(',')], filePath);
      this.logger.info(
        `Done Generating Revenue Wireless PayType Report | File: ${fileName} | Path: ${filePath} | Timestamp: ${new Date()}`
      );
      const uploadResponse = await this.uploadFile.S3UploadFile(fileName, './storage', 'revenue-wireless');
      if (typeof uploadResponse.status !== 'undefined' && uploadResponse.status === true) {
        this.logger.info(`File Successfully Uploaded to S3 Bucket - ${fileName}`);
      }

      await this.generatedReportRepository.add({
        id: this.uuid.create(),
        year: year.toString(),
        fileName,
        filePath,
        month: mList[month],
        type: 'revenuewirelesspaytype',
        fileS3Path: 'revenue-wireless',
        createdAt: new Date().toISOString(),
      });

      this.uploadToSFTPServer({ fileName });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async generateMonthlyWirelessPayModeReport() {
    try {
      const previousDate = new Date();
      previousDate.setDate(0);
      const month = previousDate.getMonth() + 1;
      const year = previousDate.getFullYear();
      const monthlyReportDate = `${month}-${year}`;
      const mList = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];
      const dateNow = new Date();
      const fileName = `Revenue Wireless-PayMode-${this.dateHelper.formatDateMMDDYYYY(dateNow)}.csv`;
      const filePath = `./storage/${fileName}`;
      const header = {
        transId: 'Payment Reference',
        createdAt: 'Payment Date',
        payModeCode: 'Payment Mode Code',
        payModeDesc: 'Payment Mode Description',
        creditCardBank: 'Credit Card Company',
        qty: 'Qty',
        amountValue: 'Amount',
      };
      await Reports.deleteFile(filePath);
      let qtytotal = 0;
      let amounttotal = 0;
      await this.appendDataToFile([Object.values(header).join(',')], filePath);

      const wirelesPayModedReport = async (startKey) => {
        const transactions = await this.repository.raWirelessMonthlyReport(monthlyReportDate, startKey);
        qtytotal += transactions.count;
        const transactionTransformed = await this.reportTransformation(transactions, 'transactionLogs');
        transactionTransformed.forEach((key) => {
          amounttotal += parseFloat(key.amountValue);
        });

        const csvRows = await Reports.dataToCSVRows(transactionTransformed, header);
        await this.appendDataToFile(csvRows, filePath);
        const { lastKey } = transactions;

        if (typeof lastKey === 'object' && lastKey !== null) {
          return wirelesPayModedReport(lastKey);
        }
        return '';
      };

      await wirelesPayModedReport('');
      amounttotal.toFixed(2);
      const footer = {
        first: null,
        second: null,
        third: null,
        fourth: null,
        fifth: 'TOTAL',
        qtytotal,
        amounttotal,
      };
      await this.appendDataToFile([Object.values(footer).join(',')], filePath);
      this.logger.info(
        `Done Generating Revenue Wireless PayMode Report | File: ${fileName} | Path: ${filePath} | Timestamp: ${new Date()}`
      );
      const uploadResponse = await this.uploadFile.S3UploadFile(fileName, './storage', 'revenue-wireless');
      if (typeof uploadResponse.status !== 'undefined' && uploadResponse.status === true) {
        this.logger.info(`File Successfully Uploaded to S3 Bucket - ${fileName}`);
      }
      await this.generatedReportRepository.add({
        id: this.uuid.create(),
        year: year.toString(),
        fileName,
        filePath,
        month: mList[month],
        type: 'revenuewirelesspaymode',
        fileS3Path: 'revenue-wireless',
        createdAt: new Date().toISOString(),
      });

      this.uploadToSFTPServer({ fileName });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async monthlyReports(data) {
    try {
      const filteredData = await this.generatedReportRepository.searchFilter(data);
      return {
        filteredData,
        count: filteredData.count,
        lastKey: JSON.stringify(filteredData.lastKey),
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async downloadS3Reports(data, httpInfo, currentUser) {
    try {
      const generatedReport = await this.generatedReportRepository.getData(data.id);

      if (generatedReport[0]?.count) {
        throw new Error('Report not found!');
      }

      const { fileName, fileS3Path } = generatedReport[0];
      const category = this.category(this.repoName, data.type);

      await this.auditLogs(
        {
          ...httpInfo,
        },
        currentUser,
        category
      );

      return await this.uploadFile.S3DownloadFile(fileName, fileS3Path, process.env.S3_BUCKET_NAME);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async downloadS3LukeBatchFiles(data, httpInfo, currentUser) {
    try {
      const batchFiles = await this.batchFileRepository.getData(data.filename);

      if (batchFiles[0]?.count) {
        throw new Error('File not found!');
      }

      const { filename, location } = batchFiles[0];
      const category = this.category(this.repoName, 'lukeBatch');

      await this.auditLogs(
        {
          ...httpInfo,
        },
        currentUser,
        category
      );

      return await this.uploadFile.S3DownloadFile(filename, location, process.env.S3_BUCKET_LUKE);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async gotsReport(data) {
    try {
      const payload = data;
      payload.filter.channelId = process.env.GOTSID;
      return await this.searchDataForReport(payload);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async ecpayReport(data) {
    try {
      const payload = data;
      payload.filter.merchantTerminalId = 'ecpay';
      return await this.searchDataForReport(payload);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async channelReport(args) {
    try {
      const { filter } = args;

      const startMonth = this.dateHelper.monthNumberConverted(filter.month.start, 'start', filter.year.start);
      const endMonth = this.dateHelper.monthNumberConverted(filter.month.end, 'end', filter.year.end);

      const transactionSnapshot = await this.snapshotRepository.getSnapshotByDateRange(
        'transactions',
        startMonth,
        endMonth
      );
      const result = [];
      const mergeData = {};
      // Extract Data from Snapshot
      const payloadChannelReport = transactionSnapshot.map((snapshot) => {
        const payloadSnapshot = JSON.parse(snapshot.payload);
        return payloadSnapshot.payloadChannelReport;
      });
      // Merging Data
      await payloadChannelReport.forEach((snapshot) => {
        if (snapshot) {
          Object.keys(snapshot).forEach(async (key) => {
            if (typeof mergeData[key] === 'undefined') {
              mergeData[key] = {
                channelId: snapshot[key].channelId,
                channelName: snapshot[key].channelName,
                gateway: snapshot[key].gateway,
                fundingSource: snapshot[key].fundingSource,
                transAmount: snapshot[key].transAmount,
                transCount: snapshot[key].transCount,
                billType: snapshot[key].billType,
                businessUnit: snapshot[key].businessUnit,
                paymentType: snapshot[key].paymentType,
              };
            } else {
              // Add Transaction Count and Amount Value
              mergeData[key].transAmount += parseFloat(snapshot[key].transAmount);
              mergeData[key].transCount += snapshot[key].transCount;
            }
          });
        }
      });

      Object.values(mergeData).forEach(async (value) => {
        if (filter.channelIds && filter.fundingSource && filter.billType) {
          if (
            filter.channelIds.includes(value.channelId) &&
            filter.fundingSource === value.fundingSource &&
            filter.billType === value.billType
          ) {
            result.push(value);
          }
        } else if (filter.fundingSource && filter.billType) {
          if (filter.fundingSource === value.fundingSource && filter.billType === value.billType) {
            result.push(value);
          }
        } else if (filter.channelIds && filter.billType) {
          if (filter.channelIds.includes(value.channelId) && filter.billType === value.billType) {
            result.push(value);
          }
        } else if (filter.channelIds && filter.fundingSource) {
          if (filter.channelIds.includes(value.channelId) && filter.fundingSource === value.fundingSource) {
            result.push(value);
          }
        } else if (filter.fundingSource) {
          if (filter.fundingSource === value.fundingSource) {
            result.push(value);
          }
        } else if (filter.channelIds) {
          if (filter.channelIds.includes(value.channelId)) {
            result.push(value);
          }
        } else if (filter.billType) {
          if (filter.billType === value.billType) {
            result.push(value);
          }
        } else {
          result.push(value);
        }
      });
      return {
        filteredData: result,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async generateGlobeOneReportFile() {
    try {
      const globeOneId = process.env.GLOBE_ONE_CHANNEL_ID;
      const today = new Date();
      today.toLocaleString('en-US', { timeZone: 'Asia/Manila' });
      const yesterday = new Date(today.setDate(today.getDate() - 1));
      const year = yesterday.getFullYear();
      const month = yesterday.getMonth() + 1;
      const day = yesterday.getDate();
      const fileName = `GlobeOneReport-${this.dateHelper.formatDateMMDDYYYY(yesterday)}.csv`;
      const filePath = './storage/';
      const header = {
        recordDate: 'Transaction Date/Time',
        mobileNumber: 'Mobile No.',
        amountValue: 'Amount',
        paymentMethod: 'Payment Type',
        status: 'Payment Status',
        reference: 'Payment Id',
        refusalReasonRaw: 'Refusal/Error Reason',
      };
      // Delete Existing Report in storage
      await this.deleteExistingFile(`${filePath}${fileName}`);
      await this.appendDataToFile([Object.values(header).join(',')], `${filePath}${fileName}`);
      const dailyReportDate = `${month}-${day}-${year}`;
      const globeOneTransactions = async (startKey) => {
        const result = await this.repository.globeOneDailyReport(dailyReportDate, startKey, globeOneId);
        const transactionTransformed = await this.reportTransformation(result, 'transactionLogs');
        const csvRows = await Reports.dataToCSVRows(transactionTransformed, header);
        await this.appendDataToFile(csvRows, `${filePath}${fileName}`);
        const { lastKey } = result;

        if (lastKey) {
          return globeOneTransactions(lastKey);
        }
        return '';
      };
      await globeOneTransactions('');
      this.logger.info({
        message: 'Done Generating GlobeOne Report',
        file: fileName,
        path: filePath,
      });
      const uploadResponse = await this.uploadFile.S3UploadFile(fileName, filePath, 'globe-one');
      if (uploadResponse.status === true) {
        this.logger.info(uploadResponse);

        // Email Daily Report
        const emailPatternId = this.config.notifPatternId.emails[this.repoName].globeOne;
        const recipients = this.config.recipient.globeOne;
        const emailDate = `${this.dateHelper.monthNames()[month - 1]} ${day}, ${year}.`;

        await recipients.forEach(async (recipient) => {
          // Sending of Email
          await Reports.sendDailyEmail(recipient, emailPatternId, emailDate, uploadResponse.url);
        });
      } else {
        this.logger.error(uploadResponse);
      }
    } catch (err) {
      this.logger.error(err);
    }
  }

  async globeOneReport(data) {
    try {
      const payload = data;
      payload.filter.channelId = process.env.GLOBE_ONE_CHANNEL_ID;
      return await this.searchDataForReport(payload);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async generateECPayReportFile() {
    try {
      const today = new Date();
      today.toLocaleString('en-US', { timeZone: 'Asia/Manila' });
      const yesterday = new Date(today.setDate(today.getDate() - 1));
      const year = yesterday.getFullYear();
      const month = yesterday.getMonth() + 1;
      const day = yesterday.getDate();
      const fileName = `ECPayReport-${this.dateHelper.formatDateMMDDYYYY(yesterday)}.csv`;
      const filePath = './storage/';
      const header = {
        reference: 'Reference No.',
        billerName: 'Biller Name',
        subMerchantId: 'Submerchant ID',
        amountValue: 'Amount',
        status: 'Status',
      };
      // Delete Existing Report in storage
      await this.deleteExistingFile(`${filePath}${fileName}`);
      await this.appendDataToFile([Object.values(header).join(',')], `${filePath}${fileName}`);
      const dailyReportDate = `${month}-${day}-${year}`;
      const ecpayTransactions = async (startKey) => {
        const result = await this.repository.ecpayDailyReport(dailyReportDate, startKey);
        const transactionTransformed = await this.reportTransformation(result, 'transactionLogs');
        const csvRows = await Reports.dataToCSVRows(transactionTransformed, header);
        await this.appendDataToFile(csvRows, `${filePath}${fileName}`);
        const { lastKey } = result;

        if (lastKey) {
          return ecpayTransactions(lastKey);
        }
        return '';
      };
      await ecpayTransactions('');
      this.logger.info({
        message: 'Done Generating ECPay Report',
        file: fileName,
        path: filePath,
      });
      const uploadResponse = await this.uploadFile.S3UploadFile(fileName, filePath, 'ecpay');
      if (uploadResponse.status === true) {
        this.logger.info(uploadResponse);

        // Email Daily Report
        const emailPatternId = this.config.notifPatternId.emails[this.repoName].ecpay;
        const recipients = this.config.recipient.ecpay;
        const emailDate = `${this.dateHelper.monthNames()[month - 1]} ${day}, ${year}.`;

        await recipients.forEach(async (recipient) => {
          // Sending of Email
          await Reports.sendDailyEmail(recipient, emailPatternId, emailDate, uploadResponse.url);
        });
      } else {
        this.logger.error(uploadResponse);
      }
    } catch (err) {
      this.logger.error(err);
    }
  }

  async payByLink(data) {
    try {
      const payload = data;
      payload.filter.channelId = process.env.PAY_BY_LINK_CHANNEL_ID;
      return await this.searchDataForReport(payload);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async loadORReport(data) {
    try {
      const payload = data;
      payload.filter.isOrTransaction = true;
      return await this.searchDataForReport(data);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async installmentReport(data) {
    try {
      const payload = data;
      payload.filter.isInstallment = true;
      return await this.searchDataForReport(data);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async adaDeclinedDetailedReport(data) {
    try {
      this.logger.info({
        message: 'ADA Declined Detailed Report Filter',
        payload: JSON.stringify(data),
      });

      const transactions = await this.searchDataRepository(
        this.recurringTransRepository,
        data,
        'entityName',
        'payment'
      );

      this.logger.info({
        message: 'ADA Declined Detailed Transactions',
        payload: JSON.stringify(transactions),
      });
      const filteredData = await this.reportTransformation(transactions.filteredData, 'recurringLogs');
      this.logger.info({
        message: 'ADA Declined Detailed Filtered Transaction',
        payload: JSON.stringify(filteredData),
      });
      return {
        lastKey: JSON.stringify(transactions.lastKey),
        filteredData,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async adaSummaryReport(data) {
    try {
      this.logger.info({
        message: 'ADA Summary Report Filter',
        payload: JSON.stringify(data),
      });
      const { filter } = data;
      const startMonth = this.dateHelper.monthNumberConverted(filter.month.start, 'start', filter.year.start);
      const endMonth = this.dateHelper.monthNumberConverted(filter.month.end, 'end', filter.year.end);
      const recurringSnapshot = await this.snapshotRepository.getSnapshotByDateRange('recurring', startMonth, endMonth);
      const recurringSnapshotPayload = {};
      const result = [];
      const getPerBankOnSnapshot = async (newPayload, snapshotDate) => {
        const { month, year } = snapshotDate;
        await Object.entries(newPayload).forEach(async (payload) => {
          const [bank, value] = payload;
          if (filter.bank) {
            if (filter.bank === bank) {
              const index = `${month}|${year}|${bank}`;
              if (typeof recurringSnapshotPayload[index] !== 'undefined') {
                recurringSnapshotPayload[index].Approved.transCount += value.Approved.transCount;
                recurringSnapshotPayload[index].Approved.transAmount += value.Approved.transAmount;
                recurringSnapshotPayload[index].Declined.transCount += value.Declined.transCount;
                recurringSnapshotPayload[index].Declined.transAmount += value.Declined.transAmount;
              } else {
                recurringSnapshotPayload[index] = value;
              }
            }
          } else {
            const index = `${month}|${year}|${bank}`;
            if (typeof recurringSnapshotPayload[index] !== 'undefined') {
              recurringSnapshotPayload[index].Approved.transCount += value.Approved.transCount;
              recurringSnapshotPayload[index].Approved.transAmount += value.Approved.transAmount;
              recurringSnapshotPayload[index].Declined.transCount += value.Declined.transCount;
              recurringSnapshotPayload[index].Declined.transAmount += value.Declined.transAmount;
            } else {
              recurringSnapshotPayload[index] = value;
            }
          }
        });
      };

      const getPerMonthOnSnapshot = async (transactions) => {
        await transactions.forEach(async (transaction) => {
          const snapshotDate = {
            month: transaction.month,
            year: transaction.year,
          };
          const snapshotPayload = JSON.parse(transaction.payload);
          await getPerBankOnSnapshot(snapshotPayload, snapshotDate);
        });
      };

      await getPerMonthOnSnapshot(recurringSnapshot);

      await Object.entries(recurringSnapshotPayload).forEach(async (snapshotPayload) => {
        const [key, value] = snapshotPayload;
        const splitKey = key.split('|');
        result.push({
          month: splitKey[0],
          year: splitKey[1],
          bank: splitKey[2],
          numberOfBilledAccount: value.Approved.transCount + value.Declined.transCount,
          totalAmountAR: value.Approved.transAmount + value.Declined.transAmount,
          approvedTransCount: value.Approved.transCount,
          approvedTransAmount: value.Approved.transAmount,
          declinedTransCount: value.Declined.transCount,
          declinedTransAmount: value.Declined.transAmount,
          declinedRate:
            (value.Declined.transCount / (value.Approved.transCount + value.Declined.transCount)).toFixed(2) * 100,
        });
      });
      return { filteredData: result };
    } catch (error) {
      this.logger.error({
        message: 'ADA Summary Report Error',
        payload: error,
      });
      throw error;
    }
  }

  async endGameTransactionReport(data) {
    try {
      this.logger.info({
        message: 'End Game Transaction Report Filter',
        payload: JSON.stringify(data),
      });

      const transactions = await this.searchDataRepository(
        this.recurringTransRepository,
        data,
        'entityName',
        'endgame'
      );

      this.logger.info({
        message: 'End Game Detailed Transactions',
        payload: JSON.stringify(transactions),
      });
      const filteredData = await this.reportTransformation(transactions.filteredData, 'recurringLogs');
      this.logger.info({
        message: 'End Game Filtered Transaction',
        payload: JSON.stringify(filteredData),
      });
      return {
        lastKey: JSON.stringify(transactions.lastKey),
        filteredData,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async uploadTransactions(args, httpInfo, currentUser) {
    try {
      const { filename, mimetype, createReadStream } = await args.file.file;
      const re = /.+(\.csv)$/;
      const result = [];
      const duplicates = [];
      const searchData = [];
      // Invalid File Format
      if (!filename || !re.test(filename)) {
        this.logger.error(
          `Bulk Upload - Invalid File: ${filename} | Filtered By ${currentUser} | HTTP Info: ${JSON.stringify(httpInfo)} | MimeType: ${JSON.stringify(mimetype)}`
        );
        throw new Error('Invalid File');
      }
      this.logger.info(
        `File: ${filename} | Filtered By ${currentUser} | HTTP Info: ${JSON.stringify(httpInfo)} | MimeType: ${JSON.stringify(mimetype)}`
      );
      const readStream = await createReadStream();
      let csvData;
      try {
        csvData = await csv({
          headers: ['reference', 'accountNumber', 'datefrom', 'dateto'],
          checkColumn: true,
          ignoreEmpty: true,
        }).fromStream(readStream);
      } catch (error) {
        this.logger.error(`${error.name}`, JSON.stringify(error));
        throw new Error(error.err);
      }

      // If No Data Found
      if (csvData.length < 1) {
        this.logger.error(`Search Bulk Data - No Data Found: ${JSON.stringify(csvData)}`);
        return result;
      }
      // If row data exceed 1000
      if (csvData.length > 1000) {
        this.logger.error('CSV Row Data Exceed to 1000 rows');
        throw new Error('CSV Row Data Exceed to 1000 rows');
      }

      this.logger.info(`Search Bulk Data - CSV Data: ${JSON.stringify(csvData)}`);
      for (let i = 0; i < csvData.length; i += 1) {
        const { accountNumber, reference, datefrom, dateto } = csvData[i];
        const validation = bulkSearchUsesCases(csvData[i]);
        if (validation.status === 200) {
          if (!duplicates.includes(`${accountNumber}${reference}${datefrom}${dateto}`)) {
            duplicates.push(`${accountNumber}${reference}${datefrom}${dateto}`);
            if ((!datefrom && dateto) || (datefrom && !dateto)) {
              this.logger.error(`Incomplete Date Data: ${JSON.stringify(csvData[i])}`);
            } else {
              searchData.push(csvData[i]);
            }
          }
        } else {
          this.logger.error(`Invalid Input: ${JSON.stringify(validation)}`);
        }
      }

      this.logger.info(`Search Bulk Data - Removed Duplicate Search Data: ${JSON.stringify(searchData)}`);
      const response = await Promise.all(
        searchData.map(async (data) => {
          const { accountNumber, reference, datefrom, dateto } = data;
          let filterData, start, end;
          if (datefrom) {
            const datefromsplit = datefrom.split('/');
            start = `${datefromsplit[2]}-${datefromsplit[0]}-${datefromsplit[1]}T00:00:00:000Z`;
          }
          if (dateto) {
            const datetosplit = dateto.split('/');
            end = `${datetosplit[2]}-${datetosplit[0]}-${datetosplit[1]}T15:59:59:000Z`;
          }
          if (datefrom && dateto && reference && accountNumber) {
            filterData = {
              accountNumber,
              reference,
              createdAt: {
                start,
                end,
              },
            };
          } else if (datefrom && dateto && reference && !accountNumber) {
            filterData = {
              reference,
              createdAt: {
                start,
                end,
              },
            };
          } else if (datefrom && dateto && !reference && accountNumber) {
            filterData = {
              accountNumber,
              createdAt: {
                start,
                end,
              },
            };
          } else if (reference && accountNumber) {
            filterData = {
              reference,
              accountNumber,
            };
          } else if (reference) {
            filterData = {
              reference,
            };
          } else if (accountNumber) {
            filterData = {
              accountNumber,
            };
          }
          let returnedFilteredData;
          const getTransactions = async (startKeys) => {
            const transactions = await this.searchDataForReport({
              filter: filterData,
              pagination: {
                startKeys,
              },
            });
            const { filteredData, lastKey } = transactions;
            this.logger.info(`Filter: ${JSON.stringify(filterData)}`);
            result.push(...filteredData);
            returnedFilteredData = { ...returnedFilteredData, ...filteredData };
            if (lastKey) {
              await getTransactions(lastKey);
            }
          };
          if (filterData) {
            await getTransactions('');
            return returnedFilteredData;
          }
          return {};
        })
      );
      this.logger.info(`Search Bulk Data - Response: ${JSON.stringify(response)}`);
      this.logger.info(`Search Bulk Data - Result: ${JSON.stringify(result)}`);
      return {
        filteredData: result,
      };
    } catch (error) {
      this.logger.error('Bulk Search Error: ', JSON.stringify(error));
      throw error;
    }
  }

  async payByLinkReport(data) {
    try {
      let payByLinkData;
      const referenceId = data.filter?.reference;

      if (referenceId) {
        payByLinkData = await this.payByLinkRepository.getByPaymentId(referenceId, data.filter);
      } else {
        payByLinkData = await this.payByLinkRepository.getAll(data);
      }

      return {
        lastKey: JSON.stringify(payByLinkData.lastKey),
        filteredData: payByLinkData,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
module.exports = Reports;
