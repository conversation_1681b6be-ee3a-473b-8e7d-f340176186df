const Service = require('../Service');

class Audit extends Service {
  constructor(container) {
    const { auditRepository, userRepository, roleRepository, channelRepository } = container;
    super(auditRepository, container);
    this.userRepository = userRepository;
    this.roleRepository = roleRepository;
    this.channelRepository = channelRepository;
  }

  async show(where) {
    try {
      const data = await this.auditRepository.getById(Object.values(where)[0]);
      if (typeof data !== 'undefined') {
        const user = await this.userRepository.getById(data.userId);
        if (typeof user === 'undefined' || user.name !== data.userName || user.email !== data.userEmail) {
          throw this.errors.userGetError();
        }
        if (typeof user !== 'undefined') {
          const role = await this.roleRepository.getById(user.roleId);
          Object.assign(user, { role });
          let channelName = null;
          if (typeof user.channel !== 'undefined') {
            const channel = await this.channelRepository.getChannelName(user.channel);
            if (typeof channel !== 'undefined') {
              channelName = channel.name;
            }
          }
          Object.assign(user, { channel: channelName });
          Object.assign(data, { user });
        }
        Object.keys(data).forEach((key) => {
          if (key === 'newValue') {
            data[key] = JSON.stringify(data[key]) || null;
          }
          if (key === 'oldValue') {
            data[key] = JSON.stringify(data[key]) || null;
          }
        });
      }
      return data;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async search(data) {
    try {
      const results = await this.searchData(data);
      results.filteredData = results.filteredData.map((fData) => ({
        ...fData,
        newValue: JSON.stringify(fData.newValue) || null,
        oldValue: JSON.stringify(fData.oldValue) || null,
      }));
      if (typeof results.lastKey === 'undefined') {
        results.lastKey = null;
      } else {
        const { id, sortKey, createdAt } = JSON.parse(results.lastKey);
        results.lastKey = {
          id,
          sortKey,
          createdAt,
        };
      }
      return results;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async isNotViewCount() {
    try {
      const notifNotViewCount = await this.auditRepository.notifNotViewCount();
      return {
        isNotViewed: notifNotViewCount.count,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async notifications(data) {
    try {
      const filteredData = await this.auditRepository.notifications(Object.values(data)[0]);
      if (filteredData.count <= 0) {
        return {
          filteredData: [],
          count: 0,
          cursors: [''],
        };
      }
      return {
        filteredData,
        count: filteredData.count,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async updateStatus(data) {
    try {
      Object.keys(data.id).forEach((key) => {
        this.auditRepository.update(data.id[key], {
          isViewed: true,
        });
      });
      return { status: true };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}

module.exports = Audit;
