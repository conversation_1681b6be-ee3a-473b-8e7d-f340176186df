const csv = require('csvtojson');
const Service = require('../Service');
const bankUseCases = require('../../domain/bank/useCases');

class Bank extends Service {
  constructor(container) {
    const { bankRepository } = container;
    super(bankRepository, container);
  }

  async createBank(data, httpInfo, currentUser) {
    try {
      const checkBank = await this.repository.getByName(data.name);
      if (checkBank.count > 0) {
        throw this.errors.bankNameError();
      }
      const checkCode = await this.repository.getByCode(data.code);
      if (checkCode.count > 0) {
        throw this.errors.bankCodeError();
      }
      // Validate Data
      const bankPayload = bankUseCases.create({
        ...data,
      });
      // Insert Data
      return this.create(bankPayload, httpInfo, currentUser);
    } catch (error) {
      this.logger.error('Create Bank', error.message);
      throw error;
    }
  }

  async updateBank(data, id, httpInfo, currentUser) {
    try {
      const oldValue = await this.repository.getByName(id.name);

      if (!Object.keys(data).length) {
        return oldValue[0];
      }

      if (data?.code) {
        const checkCode = await this.repository.getUniqueBankCode({
          name: id.name,
          code: data.code,
        });

        if (checkCode.count > 0) {
          throw this.errors.bankCodeError();
        }
      }

      const bankPayload = await bankUseCases.update(data);

      delete bankPayload.name;
      return this.update(bankPayload, id, httpInfo, currentUser, oldValue[0]);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async deleteBank(data, id, httpInfo, currentUser) {
    try {
      const bank = await this.repository.getByName(id.name);
      if (typeof bank === 'undefined' || bank.count < 1) {
        throw this.errors.notFound();
      }
      return this.delete(data, id, httpInfo, currentUser, bank[0]);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async show(where) {
    try {
      const data = await this.repository.getByName(Object.values(where)[0].name);
      return data[0];
    } catch (error) {
      this.logger.error('Show Bank', error.message);
      throw error;
    }
  }

  async search(data) {
    try {
      const result = await this.searchDataWithCursors(data);
      return result;
    } catch (error) {
      this.logger.error('Search Bank', error.message);
      throw error;
    }
  }

  async upload(args, httpInfo, currentUser) {
    try {
      const { filename, mimetype, createReadStream } = await args.file.file;
      const re = /.+(\.csv)$/;
      if (typeof filename === 'undefined' || !re.test(filename)) {
        throw new Error('Invalid File');
      }
      const readStream = await createReadStream();
      const csvData = await csv({
        headers: ['name', 'code', 'gateway'],
        checkColumn: true,
      }).fromStream(readStream);
      if (csvData.length === 0) {
        throw new Error('No Data Found');
      }

      const existingBank = await this.repository.getAllName();
      const banks = csvData.map((bank) => {
        const checkCode = existingBank.filter((data) => data.code === bank.code);
        const checkBank = existingBank.filter((data) => data.name === bank.name);
        const { gateway } = bank;

        if (checkCode.length > 0 && checkBank.length > 0) {
          throw this.errors.bankError();
        }
        // Validate Data
        return bankUseCases.create({
          gateway: gateway.toLowerCase(),
          code: bank.code,
          name: bank.name,
          isExist: checkBank.count,
        });
      });

      await banks.forEach(async (bank) => {
        // Insert of Data
        await this.create(bank, httpInfo, currentUser);
      });

      return {
        filename,
        mimetype,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async searchDataWithCursors(data) {
    let allIds, filteredData;
    const cursors = [''];
    if (Object.keys(data.filter).length <= 0) {
      filteredData = await this.repository.listAll(data);
      allIds = await this.repository.getAllId();
    } else {
      const searchData = await this.repository.searchFilter(data);
      allIds = await this.repository.searchFilterNoPaginate(data);
      filteredData = [];
      const filteredCount = searchData.count;
      for (let i = 0; i < data.pagination.limit; i += 1) {
        if (
          typeof searchData[i] !== 'undefined' &&
          (data.pagination.limit >= filteredCount || data.pagination.limit <= filteredCount)
        ) {
          filteredData.push(searchData[i]);
        }
      }
    }
    const filterCount = filteredData.count;
    if (filterCount <= 0) {
      return { filteredData: [], count: 0, cursors: [''] };
    }
    for (let i = 1; i < Math.ceil(allIds.count / data.pagination.limit); i += 1) {
      cursors.push(allIds[i * data.pagination.limit - 1].name);
    }
    return {
      filteredData,
      count: allIds.count,
      cursors,
    };
  }
}

module.exports = Bank;
