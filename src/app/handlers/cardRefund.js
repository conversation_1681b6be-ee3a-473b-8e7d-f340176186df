const Service = require('../Service');
const refundSearchInput = require('../../domain/transactions/useCases/cardRefundSearchInput');
const refundApprovalUsecases = require('../../domain/transactions/useCases/cardRefundRequest');
const refundDetailedUsecases = require('../../domain/transactions/useCases/refundTransTransformation');

class CardRefund extends Service {
  constructor(container) {
    const { transactionRepository, snapshotRepository, refundRepository, authClient, config, configRepository } =
      container;
    super(refundRepository, container);
    this.transactionRepository = transactionRepository;
    this.snapshotRepository = snapshotRepository;
    this.authClient = authClient;
    this.config = config;
    this.configRepository = configRepository;
  }

  static async refundTransactionTransformation(transactions) {
    if (transactions.count > 0) {
      return transactions.map((transaction) => refundDetailedUsecases(transaction));
    }
    return [];
  }

  // Request Card Refund Module
  async refundRequestModule(data, user) {
    try {
      let payload = data;
      const { channelId } = payload.filter;
      const { cardAssignedChannels, billType } = user;
      if (!cardAssignedChannels || !billType) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      const parsedAssignedChannel = JSON.parse(cardAssignedChannels);
      if (parsedAssignedChannel.length < 1) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      const userAssignedChannels = await parsedAssignedChannel.map((channel) => channel.channelId);

      if (!channelId) {
        payload.filter.userAssignedChannels = userAssignedChannels;
      } else if (!userAssignedChannels.includes(channelId)) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      if (user.billType !== 'Both') {
        payload.filter.billType = user.billType;
      }
      // Validate Filter Input
      payload = await refundSearchInput(payload);
      this.logger.info({
        message: 'Card Refund Request Search Payload',
        payload: JSON.stringify(payload),
      });
      const result = await this.transactionRepository.searchFilter(payload);
      const filteredData = await this.reportTransformation(result, 'transactionLogs');
      return {
        filteredData,
        lastKey: JSON.stringify(result.lastKey),
      };
    } catch (error) {
      this.logger.error('Error Search Card Refund Data: ', error.message);
      throw error;
    }
  }

  // Filling a Request for Refund
  async requestForRefund(data, httpInfo, currentUser) {
    try {
      const category = this.category(this.repoName, 'request');
      const transactions = await this.transactionRepository.getByReference(data.reference);
      if (!transactions) {
        throw new Error('No Transactions Found');
      }
      const filteredData = await this.reportTransformation(transactions, 'transactionLogs');
      const forApproval = 'For Approval';

      const { refundApprovalStatus } = filteredData;
      switch (refundApprovalStatus) {
        case 'For Approval':
          throw new Error('Request for refund already filed');
        case 'Approved':
          throw new Error('Request for refund already approved');
        default:
          filteredData[0].refundApprovalStatus = forApproval;
      }
      if (data.refundAmount > parseFloat(filteredData[0].finalAmount || filteredData[0].amountValue)) {
        throw new Error('Refund Amount is greater than Payment amount');
      }
      const payload = await refundApprovalUsecases({
        ...filteredData[0],
        ...data,
      });
      const { id, transactionId, refundAmount, refundReason } = payload;
      const newpayload = payload;
      // Added in Request in Refund Approval
      await this.repository.add(payload);
      // Update Transaction Logs
      await this.transactionRepository.update({
        id,
        transactionId,
        refundApprovalStatus: forApproval,
        refundStatus: forApproval,
        refundAmount,
        refundReason,
      });
      await this.auditLogs(
        {
          ...httpInfo,
          newValue: newpayload,
        },
        currentUser,
        category
      );
      return {
        approve: true,
        refundApprovalStatus: forApproval,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  // Approvers Card Refund Module
  async refundApprovalModule(data, user) {
    try {
      let payload = data;
      if (!user || !user.cardAssignedChannels || !user.billType) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      const { cardAssignedChannels, billType } = user;
      const assignedChannels = JSON.parse(cardAssignedChannels);
      if (assignedChannels.length < 1) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      const userAssignedChannels = await assignedChannels.map((channel) => channel.channelId);
      if (!payload.filter.channelId) {
        payload.filter.userAssignedChannels = userAssignedChannels;
      } else if (!userAssignedChannels.includes(data.filter.channelId)) {
        delete payload.filter.channelId;
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      if (billType !== 'Both') {
        payload.filter.billType = billType;
      }
      // Validate Filter Input
      if (payload.filter.status) {
        if (payload.filter.status === 'ADYEN_AUTHORIZED') {
          payload.filter.status = 'ADYEN_AUTHORISED';
        }
      }
      payload = await refundSearchInput(payload);
      payload.filter.refundApprovalStatus = 'For Approval';
      const filteredData = await this.repository.searchFilter(payload);
      const lastKey = JSON.stringify(filteredData.lastKey);
      const result = await CardRefund.refundTransactionTransformation(filteredData);
      return {
        filteredData: result,
        lastKey,
      };
    } catch (error) {
      this.logger.error('Error Search Card Refund Data: ', error.message);
      throw error;
    }
  }

  // Module/Page
  async refundApprovalModuleHistory(data) {
    try {
      let payload = data;
      // Validate Filter Input
      payload = await refundSearchInput(payload);
      payload.filter.refundApprovalStatus = 'Rejected';
      const filteredData = await this.repository.searchFilter(payload);
      const lastKey = JSON.stringify(filteredData.lastKey);
      const result = await CardRefund.refundTransactionTransformation(filteredData);
      return {
        filteredData: result,
        lastKey,
      };
    } catch (error) {
      this.logger.error('Error Search Card Refund Data: ', error.message);
      throw error;
    }
  }

  async refundapproval(data, httpInfo, currentUser) {
    try {
      const refundForApprovalData = await this.repository.getByReferenceForApproval(data.reference);
      if (refundForApprovalData.count === 0) {
        throw new Error('No Data Found!');
      }
      const rejected = 'Rejected';
      let category = this.category(this.repoName, 'approved');

      // Reject
      if (data.action === 'reject') {
        category = this.category(this.repoName, 'rejected');
        await this.transactionRepository.update({
          id: refundForApprovalData[0].id,
          transactionId: refundForApprovalData[0].transactionId,
          refundApprovalStatus: rejected,
          refundRejectedTimestamp: new Date().toISOString(),
          refundAmount: refundForApprovalData[0].refundAmount,
          refundReason: refundForApprovalData[0].refundReason,
          refundStatus: rejected,
        });
        const refundTransaction = await this.repository.getByReferenceForApproval(data.reference);
        await this.repository.update({
          id: refundTransaction[0].id,
          requestTimeStamp: refundTransaction[0].requestTimeStamp,
          refundApprovalStatus: rejected,
          approverRemarks: data.approverRemarks,
          refundRejectedTimestamp: new Date().toISOString(),
        });
        await this.auditLogs(
          {
            ...httpInfo,
          },
          currentUser,
          category
        );
        return {
          success: true,
          code: 200,
        };
      }

      // Approved
      const channel = await this.channelRepository.getById(refundForApprovalData[0].channelId);
      const { clientId, clientSecret } = channel;
      const token = await this.authClient.accessToken({ clientId, clientSecret }, this.config);
      const cardRefundRequest = {
        paymentId: data.reference,
        refundAmount: refundForApprovalData[0].refundAmount,
        refundReason: refundForApprovalData[0].refundReason,
        requestTimeStamp: refundForApprovalData[0].requestTimeStamp,
        emailAddress: currentUser.email,
      };
      this.logger.info(`Card Refund Request: ${JSON.stringify(cardRefundRequest)}`);
      const cardRefundResponse = await this.authClient.manualRefund(cardRefundRequest, this.config, token.accessToken);
      this.logger.info(`Card Refund Response: ${JSON.stringify(cardRefundResponse)}`);
      const { pspReference } = cardRefundResponse.data;
      if (cardRefundResponse.status === 200 && pspReference) {
        return {
          success: true,
          code: cardRefundResponse.status,
        };
      }
      if (cardRefundResponse.message === 'Internal server error') {
        throw new Error('Internal server error');
      }
      throw new Error('Card Refund Error');
    } catch (error) {
      this.logger.error(JSON.stringify(error));
      throw error;
    }
  }

  async cardRefundDetailedReport(data) {
    try {
      let payload = data;
      // Validate Filter Input
      payload = await refundSearchInput(payload);
      payload.filter.refundApprovalStatus = 'Approved';
      const filteredData = await this.repository.searchFilter(payload);
      const lastKey = JSON.stringify(filteredData.lastKey);
      const result = await CardRefund.refundTransactionTransformation(filteredData);
      return {
        filteredData: result,
        lastKey,
      };
    } catch (error) {
      this.logger.error('Error Search Card Refund Detailed Report: ', error.message);
      throw error;
    }
  }

  async cardRefundSummaryReport(data) {
    try {
      const payload = data.filter;
      const { refundRange } = payload;
      const refundStats = {
        bill: {},
        nonbill: {},
      };
      const refundApprovedPayloadLoop = async (refundPayload) => {
        if (payload.channelId) {
          const { channelId } = payload;
          if (refundPayload[channelId]) {
            if (refundPayload[channelId].adyen) {
              if (refundPayload[channelId].adyen.bill) {
                if (refundStats.bill[channelId]) {
                  refundStats.bill[channelId].totalApprovedRefundAmount +=
                    refundPayload[channelId].adyen.bill.totalApprovedRefundAmount;
                  refundStats.bill[channelId].totalApprovedRefundCount +=
                    refundPayload[channelId].adyen.bill.totalApprovedRefundCount;
                } else if (
                  refundPayload[channelId].adyen.bill.totalApprovedRefundAmount !== 0 ||
                  refundPayload[channelId].adyen.bill.totalApprovedRefundCount !== 0
                ) {
                  refundStats.bill[channelId] = {
                    ...refundPayload[channelId].adyen.bill,
                    channelName: refundPayload[channelId].name,
                    totalForApprovalAmount: 0,
                    totalForApprovalCount: 0,
                    totalAutoRefundAmount: 0,
                    totalAutoRefundCount: 0,
                  };
                }
              }
              if (refundPayload[channelId].adyen.nonbill) {
                if (refundStats.nonbill[channelId]) {
                  refundStats.nonbill[channelId].totalApprovedRefundAmount +=
                    refundPayload[channelId].adyen.nonbill.totalApprovedRefundAmount;
                  refundStats.nonbill[channelId].totalApprovedRefundCount +=
                    refundPayload[channelId].adyen.nonbill.totalApprovedRefundCount;
                } else if (
                  refundPayload[channelId].adyen.nonbill.totalApprovedRefundAmount !== 0 ||
                  refundPayload[channelId].adyen.nonbill.totalApprovedRefundCount !== 0
                ) {
                  refundStats.nonbill[channelId] = {
                    ...refundPayload[channelId].adyen.nonbill,
                    channelName: refundPayload[channelId].name,
                    totalForApprovalAmount: 0,
                    totalForApprovalCount: 0,
                    totalAutoRefundAmount: 0,
                    totalAutoRefundCount: 0,
                  };
                }
              }
            }
          }
        } else {
          const keys = Object.keys(refundPayload);
          await keys.forEach((key) => {
            if (refundPayload[key].adyen) {
              if (refundPayload[key].adyen.bill) {
                if (refundStats.bill[key]) {
                  refundStats.bill[key].totalApprovedRefundAmount +=
                    refundPayload[key].adyen.bill.totalApprovedRefundAmount;
                  refundStats.bill[key].totalApprovedRefundCount +=
                    refundPayload[key].adyen.bill.totalApprovedRefundCount;
                } else if (
                  refundPayload[key].adyen.bill &&
                  (refundPayload[key].adyen.bill.totalApprovedRefundAmount !== 0 ||
                    refundPayload[key].adyen.bill.totalApprovedRefundCount !== 0)
                ) {
                  refundStats.bill[key] = {
                    ...refundPayload[key].adyen.bill,
                    channelName: refundPayload[key].name,
                    totalForApprovalAmount: 0,
                    totalForApprovalCount: 0,
                    totalAutoRefundAmount: 0,
                    totalAutoRefundCount: 0,
                  };
                }
              }
              if (refundPayload[key].adyen.nonbill) {
                if (refundStats.nonbill[key]) {
                  refundStats.nonbill[key].totalApprovedRefundAmount +=
                    refundPayload[key].adyen.nonbill.totalApprovedRefundAmount;
                  refundStats.nonbill[key].totalApprovedRefundCount +=
                    refundPayload[key].adyen.nonbill.totalApprovedRefundCount;
                } else if (
                  refundPayload[key].adyen.nonbill &&
                  (refundPayload[key].adyen.nonbill.totalApprovedRefundAmount !== 0 ||
                    refundPayload[key].adyen.nonbill.totalApprovedRefundCount !== 0)
                ) {
                  refundStats.nonbill[key] = {
                    ...refundPayload[key].adyen.nonbill,
                    channelName: refundPayload[key].name,
                    totalForApprovalAmount: 0,
                    totalForApprovalCount: 0,
                    totalAutoRefundAmount: 0,
                    totalAutoRefundCount: 0,
                  };
                }
              }
            }
          });
        }
      };
      const refundForApprovalPayloadLoop = async (transaction) => {
        const { channelId, channelName, billType, refundAmount } = transaction;
        if (billType === 'Bill') {
          if (refundStats.bill[channelId]) {
            refundStats.bill[channelId].totalForApprovalAmount += parseFloat(refundAmount);
            refundStats.bill[channelId].totalForApprovalCount += 1;
          } else {
            refundStats.bill[channelId] = {
              channelName,
              totalForApprovalAmount: parseFloat(refundAmount),
              totalForApprovalCount: 1,
              totalApprovedRefundAmount: 0,
              totalApprovedRefundCount: 0,
              totalAutoRefundAmount: 0,
              totalAutoRefundCount: 0,
            };
          }
        } else if (typeof refundStats.nonbill[channelId] !== 'undefined') {
          refundStats.nonbill[channelId].totalForApprovalAmount += parseFloat(refundAmount);
          refundStats.nonbill[channelId].totalForApprovalCount += 1;
        } else {
          refundStats.nonbill[channelId] = {
            channelName,
            totalForApprovalAmount: parseFloat(refundAmount),
            totalForApprovalCount: 1,
            totalApprovedRefundAmount: 0,
            totalApprovedRefundCount: 0,
            totalAutoRefundAmount: 0,
            totalAutoRefundCount: 0,
          };
        }
      };

      const refundAutoPayloadLoop = async (refundPayload) => {
        if (payload.channelId) {
          const { channelId } = payload;
          if (refundPayload[channelId]) {
            if (refundPayload[channelId].adyen) {
              if (refundPayload[channelId].adyen.bill) {
                if (refundStats.bill[channelId]) {
                  refundStats.bill[channelId].totalAutoRefundAmount +=
                    refundPayload[channelId].adyen.bill.totalApprovedRefundAmount;
                  refundStats.bill[channelId].totalAutoRefundCount +=
                    refundPayload[channelId].adyen.bill.totalApprovedRefundCount;
                } else if (
                  refundPayload[channelId].adyen.bill.totalApprovedRefundAmount !== 0 ||
                  refundPayload[channelId].adyen.bill.totalApprovedRefundCount !== 0
                ) {
                  const { totalApprovedRefundAmount, totalApprovedRefundCount } = refundPayload[channelId].adyen.bill;
                  refundStats.bill[channelId] = {
                    totalAutoRefundAmount: totalApprovedRefundAmount,
                    totalAutoRefundCount: totalApprovedRefundCount,
                    channelName: refundPayload[channelId].name,
                    totalApprovedRefundAmount: 0,
                    totalApprovedRefundCount: 0,
                    totalForApprovalAmount: 0,
                    totalForApprovalCount: 0,
                  };
                }
              }
              if (refundPayload[channelId].adyen.nonbill) {
                if (refundStats.nonbill[channelId]) {
                  refundStats.nonbill[channelId].totalAutoRefundAmount +=
                    refundPayload[channelId].adyen.nonbill.totalApprovedRefundAmount;
                  refundStats.nonbill[channelId].totalAutoRefundCount +=
                    refundPayload[channelId].adyen.nonbill.totalApprovedRefundCount;
                } else if (
                  refundPayload[channelId].adyen.nonbill.totalApprovedRefundAmount !== 0 ||
                  refundPayload[channelId].adyen.nonbill.totalApprovedRefundCount !== 0
                ) {
                  /* eslint max-len: ["error", { "code": 110 }] */
                  const AtRfndAmnt = refundPayload[channelId].adyen.nonbill.totalApprovedRefundAmount;
                  const AtRfundCnt = refundPayload[channelId].adyen.nonbill.totalApprovedRefundCount;
                  refundStats.nonbill[channelId] = {
                    totalAutoRefundAmount: AtRfndAmnt,
                    totalAutoRefundCount: AtRfundCnt,
                    channelName: refundPayload[channelId].name,
                    totalApprovedRefundAmount: 0,
                    totalApprovedRefundCount: 0,
                    totalForApprovalAmount: 0,
                    totalForApprovalCount: 0,
                  };
                }
              }
            }
          }
        } else {
          const keys = Object.keys(refundPayload);
          await keys.forEach((key) => {
            if (refundPayload[key].adyen) {
              if (refundPayload[key].adyen.bill) {
                if (refundStats.bill[key]) {
                  refundStats.bill[key].totalAutoRefundAmount +=
                    refundPayload[key].adyen.bill.totalApprovedRefundAmount;
                  refundStats.bill[key].totalAutoRefundCount += refundPayload[key].adyen.bill.totalApprovedRefundCount;
                } else if (
                  refundPayload[key].adyen.bill &&
                  (refundPayload[key].adyen.bill.totalApprovedRefundAmount !== 0 ||
                    refundPayload[key].adyen.bill.totalApprovedRefundCount !== 0)
                ) {
                  const AtRefundAmnt = refundPayload[key].adyen.bill.totalApprovedRefundAmount;
                  const AtRefundCount = refundPayload[key].adyen.bill.totalApprovedRefundCount;
                  refundStats.bill[key] = {
                    totalAutoRefundAmount: AtRefundAmnt,
                    totalAutoRefundCount: AtRefundCount,
                    channelName: refundPayload[key].name,
                    totalForApprovalAmount: 0,
                    totalForApprovalCount: 0,
                  };
                }
              }
              if (refundPayload[key].adyen.nonbill) {
                if (refundStats.nonbill[key]) {
                  refundStats.nonbill[key].totalAutoRefundAmount +=
                    refundPayload[key].adyen.nonbill.totalApprovedRefundAmount;
                  refundStats.nonbill[key].totalAutoRefundCount +=
                    refundPayload[key].adyen.nonbill.totalApprovedRefundCount;
                } else if (
                  refundPayload[key].adyen.nonbill &&
                  (refundPayload[key].adyen.nonbill.totalApprovedRefundAmount !== 0 ||
                    refundPayload[key].adyen.nonbill.totalApprovedRefundCount !== 0)
                ) {
                  const AtRefundAmnt = refundPayload[key].adyen.nonbill.totalApprovedRefundAmount;
                  const AtRefundCount = refundPayload[key].adyen.nonbill.totalApprovedRefundCount;
                  refundStats.nonbill[key] = {
                    totalAutoRefundAmount: AtRefundAmnt,
                    totalAutoRefundCount: AtRefundCount,
                    channelName: refundPayload[key].name,
                    totalForApprovalAmount: 0,
                    totalForApprovalCount: 0,
                    totalApprovedRefundAmount: 0,
                    totalApprovedRefundCount: 0,
                  };
                }
              }
            }
          });
        }
      };
      const refundApprovedStats = async (snapshots) => {
        await snapshots.forEach(async (snapshot) => {
          await refundApprovedPayloadLoop(JSON.parse(snapshot.payload));
        });
      };
      const refundForApprovalStats = async (transactions) => {
        await transactions.forEach(async (transaction) => {
          await refundForApprovalPayloadLoop(transaction);
        });
      };
      const refundAutoStats = async (snapshots) => {
        await snapshots.forEach(async (snapshot) => {
          await refundAutoPayloadLoop(JSON.parse(snapshot.payload));
        });
      };

      const refundFATransaction = (filters, startKeys) =>
        this.repository.getFARefundTransaction(filters, startKeys, 'adyen');

      const getRefundForApprovalLoop = async (startKeys) => {
        const transactions = await refundFATransaction(payload, startKeys);
        if (transactions.count > 0) {
          await refundForApprovalStats(transactions);
        }
        if (typeof transactions.lastKey !== 'undefined') {
          await getRefundForApprovalLoop(transactions.lastKey);
        }
      };
      if (refundRange) {
        const refundSnapshot = await this.snapshotRepository.getSnapshotByDateRange(
          'refund',
          refundRange.start,
          refundRange.end
        );
        await refundApprovedStats(refundSnapshot);
        const refundAutoSnapshot = await this.snapshotRepository.getSnapshotByDateRange(
          'autoRefund',
          refundRange.start,
          refundRange.end
        );
        await refundAutoStats(refundAutoSnapshot);
        await getRefundForApprovalLoop('');
      } else {
        const todayDate = new Date();
        const year = todayDate.getFullYear();
        const refundSnapshot = await this.snapshotRepository.getSnapshotByYear('refund', year);
        await refundApprovedStats(refundSnapshot);
        const refundAutoSnapshot = await this.snapshotRepository.getSnapshotByYear('autoRefund', year);
        await refundAutoStats(refundAutoSnapshot);
        await getRefundForApprovalLoop('');
      }
      return {
        Bill: Object.values(refundStats.bill),
        NonBill: Object.values(refundStats.nonbill),
      };
    } catch (error) {
      this.logger.error('Error Search Credit Card Summary Report: ', error.message);
      throw error;
    }
  }
}

module.exports = CardRefund;
