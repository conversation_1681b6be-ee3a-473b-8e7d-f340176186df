const Service = require('../Service');
const refundSearchInput = require('../../domain/transactions/useCases/ewalletRefundSearchInput');
const refundApprovalUsecases = require('../../domain/transactions/useCases/ewalletRefundRequest');
const refundDetailedUsecases = require('../../domain/transactions/useCases/refundTransTransformation');
const { kafkaMessageMapper, determineRefundStatus, calculateRefundBalance } = require('../../infra/utils/formatter');
const {
  CREATE_REFUND_SESSION,
  SUCCESS_CREATE_REFUND_SESSION,
  REFUND_ACTION_STATUSES,
  PARTIALLY_REFUNDED,
  FULLY_REFUNDED,
} = require('../../infra/utils/constants');
const { getPhDateTime } = require('../../infra/utils/date');
const { injectChannelName, withRefundData, withSettlementData } = require('../../infra/utils/aggregates');

class XenditRefund extends Service {
  constructor(container) {
    const {
      transactionRepository,
      // snapshotRepository,
      refundRepository,
      authClient,
      config,
      configRepository,
      eventLogsRepository,
      settlementRepository,
      kafka,
    } = container;
    super(refundRepository, container);
    this.transactionRepository = transactionRepository;
    this.eventLogRepository = eventLogsRepository;
    // this.snapshotRepository = snapshotRepository;
    this.settlementRepository = settlementRepository;

    this.authClient = authClient;
    this.config = config;
    this.configRepository = configRepository;
    this.kafka = kafka;
  }

  static async refundTransactionTransformation(transactions) {
    if (transactions.count > 0) {
      return transactions.map((transaction) => refundDetailedUsecases(transaction));
    }
    return [];
  }

  // Request Card Refund Module
  async refundRequest(data, user) {
    try {
      let payload = data;
      const { channelId } = payload.filter;
      const { ewalletAssignedChannels, billType } = user;
      if (!ewalletAssignedChannels || !billType) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      const parsedAssignedChannel = JSON.parse(ewalletAssignedChannels);
      if (parsedAssignedChannel.length < 1) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      const userAssignedChannels = await parsedAssignedChannel.map((channel) => channel.channelId);

      if (!channelId) {
        payload.filter.userAssignedChannels = userAssignedChannels;
      } else if (!userAssignedChannels.includes(channelId)) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      if (user.billType !== 'Both') {
        payload.filter.billType = user.billType;
      }
      // Validate Filter Input
      payload = await refundSearchInput(payload);
      this.logger.info({
        message: 'EWallet Refund Request Search Payload',
        payload: JSON.stringify(payload),
      });
      payload.filter.gatewayProcessor = payload.filter.paymentGateway;
      delete payload.filter.paymentGateway;

      const result = await this.transactionRepository.searchFilter(payload);

      await injectChannelName(
        {
          filteredData: result,
        },
        async (channelIds) => this.channelRepository.getBatch(channelIds)
      );
      let formattedData = await withSettlementData(result, async (paymentId) =>
        this.settlementRepository.getByPaymentId(paymentId)
      );
      formattedData = await withRefundData(
        formattedData,
        async (data) => this.repository.getByPaymentAndTransactionId(data),
        data.filter
      );

      return {
        filteredData: formattedData,
        lastKey: JSON.stringify(result.lastKey),
      };
    } catch (error) {
      this.logger.error('Error Search EWallet Refund Data: ', error.message);
      throw error;
    }
  }

  // Filling a Request for Refund
  async createRefundRequest(data, httpInfo, currentUser) {
    try {
      const category = this.category(this.repoName, 'request');
      const { paymentId, transactionId } = data;
      const transactions = await this.transactionRepository.getByPaymentId(paymentId);
      if (!transactions) {
        throw new Error('No Transactions Found');
      }

      const {
        settlementBreakdown,
        gatewayProcessor,
        channelId: transChannelId,
        createDateTime,
        totalAmount,
      } = transactions[0];
      const forApproval = 'For Approval';

      let { refundApprovalStatus } = this.repository.getByPaymentAndTransactionId({
        paymentId,
        transactionId,
      });

      switch (refundApprovalStatus) {
        case 'For Approval':
          throw new Error('Request for refund already filed');
        case 'Approved':
          throw new Error('Request for refund already approved');
        default:
          refundApprovalStatus = forApproval;
      }
      const transactionType = settlementBreakdown?.[0]?.transactionType;
      if (!settlementBreakdown?.length || !['G', 'N'].includes(transactionType)) {
        throw new Error("settlementBreakdown must be non-empty with first transactionType 'G' or 'N'");
      }
      let settlement;
      let amountForComparison = totalAmount;
      let refundApprovalPayload = transactions[0];
      this.logger.info(`Processing Transaction Type : ${transactionType}`);

      const [transRefundAmount, settlementRefundAmount] = await this.repository.getPaymentIdTotalRefundAmount(
        paymentId,
        transactionId
      );
      let remainingBalance = calculateRefundBalance(totalAmount, transRefundAmount);

      if (parseFloat(data.refundAmount) > remainingBalance) {
        throw new Error('Refund amount exceeds that remaining balance for the transaction');
      }
      this.logger.info(`PaymentID: ${paymentId} Processing Transaction Type : ${transactionType}`);
      if (transactionType === 'G') {
        settlement = await this.settlementRepository.getByPaymentAndTransactionId({
          paymentId,
          transactionId,
        });
        remainingBalance = calculateRefundBalance(settlement.amount || settlement.amountValue, settlementRefundAmount);

        if (parseFloat(data.refundAmount) > remainingBalance) {
          throw new Error('Refund amount exceeds that remaining balance for the settlement');
        }
        amountForComparison = settlement.amount || settlement.amountValue;
        refundApprovalPayload = settlement;
      }
      if (data.refundAmount > parseFloat(amountForComparison)) {
        throw new Error('Refund Amount is greater than Payment amount');
      }
      if (data.refundAmount === 0) {
        throw new Error('Refund Amount should no be zero');
      }
      const payload = await refundApprovalUsecases({
        ...refundApprovalPayload,
        ...data,
      });
      const { refundAmount, refundReason } = payload;

      const newpayload = payload;

      const refundId = this.uuid.create();
      const channelResult = await this.channelRepository.getChannelName(transChannelId);
      if (!channelResult) {
        throw new Error('Channel data not found');
      }
      const [{ name: channelName, id: channelId }] = channelResult;

      const { amount, status, accountId } = refundApprovalPayload;
      const amountValue = amount ?? totalAmount;
      const refundStatus = determineRefundStatus(amountValue, refundAmount, remainingBalance);
      // Added in Request in Refund Approval
      await this.repository.add({
        ...payload,
        channelId,
        channelName,
        refundId,
        createDateTime: getPhDateTime(),
        status,
        paymentGateway: gatewayProcessor,
        refundStatus,
        amountValue: `${amountValue}`,
        amount: refundAmount,
        refundReason,
        refundApprovalStatus,
        accountNumber: accountId,
        transactionId,
      });
      //
      await this.transactionRepository.update(
        { paymentId, createDateTime },
        {
          refundId,
        }
      );

      await this.auditLogs(
        {
          ...httpInfo,
          newValue: newpayload,
        },
        currentUser,
        category
      );
      return {
        approve: true,
        refundApprovalStatus: REFUND_ACTION_STATUSES.FOR_APPROVAL,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  // Approvers Card Refund Module
  async refundApproval(data, user) {
    try {
      let payload = data;
      if (!user || !user.ewalletAssignedChannels || !user.billType) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      const { ewalletAssignedChannels, billType } = user;
      const assignedChannels = JSON.parse(ewalletAssignedChannels);
      if (assignedChannels.length < 1) {
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      const userAssignedChannels = await assignedChannels.map((channel) => channel.channelId);
      if (!payload.filter.channelId) {
        payload.filter.userAssignedChannels = userAssignedChannels;
      } else if (!userAssignedChannels.includes(data.filter.channelId)) {
        delete payload.filter.channelId;
        return {
          filteredData: [],
          lastKey: null,
        };
      }
      if (billType !== 'Both') {
        payload.filter.billType = billType;
      }
      payload = await refundSearchInput(payload);
      payload.filter.paymentGateway = JSON.parse(this.config.paymentGateway.REFUND_APPROVAL_EXPECTED_PAYMENT_GATEWAY);
      payload.filter.refundApprovalStatus = 'For Approval';
      const filteredData = await this.repository.searchFilter(payload);
      const lastKey = JSON.stringify(filteredData.lastKey);
      const result = await XenditRefund.refundTransactionTransformation(filteredData);
      return {
        filteredData: result,
        lastKey,
      };
    } catch (error) {
      this.logger.error('Error Search Card Refund Data: ', error.message);
      throw error;
    }
  }

  // Module/Page
  async refundApprovalModuleHistory(data) {
    try {
      let payload = data;
      // Validate Filter Input
      payload = await refundSearchInput(payload);
      payload.filter.refundApprovalStatus = 'Rejected';
      const filteredData = await this.repository.searchFilter(payload);
      const lastKey = JSON.stringify(filteredData.lastKey);
      const result = await XenditRefund.refundTransactionTransformation(filteredData);
      return {
        filteredData: result,
        lastKey,
      };
    } catch (error) {
      this.logger.error('Error Search EWallet Refund Data: ', error.message);
      throw error;
    }
  }

  // Approve or Reject
  async updateRefundRequest(data, httpInfo, currentUser) {
    const { action, paymentId, transactionId } = data;
    const eventId = this.uuid.create();
    let transaction;
    try {
      const refundForApprovalData = await this.repository.getByPaymentIdForApproval(paymentId, transactionId);
      if (refundForApprovalData.count === 0) {
        throw new Error('No Data Found!');
      }
      const refundKeys = {
        paymentId: refundForApprovalData[0].paymentId,
        createDateTime: refundForApprovalData[0].createDateTime,
      };
      this.logger.info({
        message: 'Refund Keys',
        refundKeys,
      });
      let category = this.category(this.repoName, 'approved');
      const transSettlementPromises = [this.transactionRepository.getByPaymentId(paymentId)];
      if (transactionId) {
        transSettlementPromises.push(
          this.settlementRepository.getByPaymentAndTransactionId({ paymentId, transactionId })
        );
      }
      const [transactionData, settlementData] = await Promise.allSettled(transSettlementPromises);

      transaction = transactionData?.status === 'rejected' ? null : transactionData.value;
      const settlement = !settlementData || settlementData.status === 'rejected' ? null : settlementData.value;
      if (!transaction) {
        throw new Error('Transaction not found');
      }
      const transactionKeys = {
        paymentId: transaction[0].paymentId,
        createDateTime: transaction[0].createDateTime,
      };
      const settlementKeys = {
        paymentId: settlement?.paymentId,
        transactionId: settlement?.transactionId,
      };
      this.logger.info({
        message: 'Settlement Keys',
        settlementKeys,
      });
      if (action === 'reject') {
        category = this.category(this.repoName, 'rejected');
        await this.repository.update(refundKeys, {
          requestTimeStamp: refundForApprovalData[0].requestTimeStamp,
          refundApprovalStatus: REFUND_ACTION_STATUSES.REJECTED,
          approverRemarks: data.approverRemarks ?? '',
          refundRejectedTimestamp: new Date().toISOString(),
        });
        await this.auditLogs(
          {
            ...httpInfo,
          },
          currentUser,
          category
        );
        return {
          success: true,
          code: 200,
        };
      }
      const kafaMessage = kafkaMessageMapper(
        this.config.kafka.topics.refund,
        this.config.kafka.events.cc_refund,
        refundForApprovalData[0].paymentId,
        {
          amount: parseFloat(refundForApprovalData[0].refundAmount),
          reason: refundForApprovalData[0].refundReason,
        },
        this.config.kafka.schemas.refund
      );
      this.logger.info({
        message: 'Kafka Request payload',
        payload: kafaMessage,
      });
      const kafkaResponse = await this.kafka.produce(kafaMessage);
      this.logger.info({
        message: 'Kafka Response',
        payload: kafkaResponse,
      });
      if (kafkaResponse.code !== 'SUCCESS') {
        throw new Error('Failed Kafka Response');
      }

      // update refund data
      await this.repository.update(refundKeys, {
        refundApprovalStatus: REFUND_ACTION_STATUSES.APPROVED,
        isAutoRefund: 'false',
      });

      const [transTotalRefundAmount, settlementTotalRefundAmount] = await this.repository.getPaymentIdTotalRefundAmount(
        paymentId,
        transactionId ?? 'none' // added null coalesce value to prevent matching with undefined results
      );

      this.logger.info(
        `Transaction Total Refund amount: ${transTotalRefundAmount}, Settlement Total Refund Amount ${settlementTotalRefundAmount}`
      );
      const status =
        parseFloat(transTotalRefundAmount) < parseFloat(transaction[0].totalAmount)
          ? PARTIALLY_REFUNDED
          : FULLY_REFUNDED;
      // update translogs
      await this.transactionRepository.update(transactionKeys, {
        status,
      });
      if (transactionId) {
        const settlementStatus =
          parseFloat(settlementTotalRefundAmount) < parseFloat(settlement.amount) ? PARTIALLY_REFUNDED : FULLY_REFUNDED;
        await this.settlementRepository.update(settlementKeys, {
          status: settlementStatus,
        });
      }
      // create event logs
      const eventParams = {
        paymentId,
        eventId,
        eventName: CREATE_REFUND_SESSION,
        eventStatus: SUCCESS_CREATE_REFUND_SESSION,
        eventDetails: {
          body: data,
        },
      };
      await this.eventLogRepository.registerEventLog(eventParams);
      return {
        success: true,
        code: 200,
      };
    } catch (error) {
      this.logger.error(JSON.stringify(error));
      await this.eventLogRepository.registerFailedEvent({
        paymentId,
        eventId,
        eventName: CREATE_REFUND_SESSION,
        errorMessage: error?.message,
        paymentMethod: transaction?.paymentMethod,
        channelId: transaction?.channelId,
      });
      throw error;
    }
  }

  // refund reports
  async xenditRefundDetailedReport(data) {
    try {
      let payload = data;
      // Validate Filter Input
      payload = await refundSearchInput(payload);
      payload.filter.refundApprovalStatus = 'Approved';
      const filteredData = await this.repository.searchFilter(payload);
      const lastKey = JSON.stringify(filteredData.lastKey);
      const result = await XenditRefund.refundTransactionTransformation(filteredData);
      return {
        filteredData: result,
        lastKey,
      };
    } catch (error) {
      this.logger.error('Error Search Gcash Refund Detailed Report: ', error.message);
      throw error;
    }
  }

  async xenditRefundSummaryReport(data) {
    try {
      const payload = data.filter;
      const { refundRange } = payload;
      const refundStats = {
        bill: {},
        nonbill: {},
      };

      const refundForApprovalPayloadLoop = async (transaction) => {
        const { channelId, channelName, billType, refundAmount } = transaction;
        if (billType === 'Bill') {
          if (refundStats.bill[channelId]) {
            refundStats.bill[channelId].totalForApprovalAmount += parseFloat(refundAmount);
            refundStats.bill[channelId].totalForApprovalCount += 1;
          } else {
            refundStats.bill[channelId] = {
              channelName,
              totalForApprovalAmount: parseFloat(refundAmount),
              totalForApprovalCount: 1,
              totalApprovedRefundAmount: 0,
              totalApprovedRefundCount: 0,
              totalAutoRefundAmount: 0,
              totalAutoRefundCount: 0,
            };
          }
        } else if (typeof refundStats.nonbill[channelId] !== 'undefined') {
          refundStats.nonbill[channelId].totalForApprovalAmount += parseFloat(refundAmount);
          refundStats.nonbill[channelId].totalForApprovalCount += 1;
        } else {
          refundStats.nonbill[channelId] = {
            channelName,
            totalForApprovalAmount: parseFloat(refundAmount),
            totalForApprovalCount: 1,
            totalApprovedRefundAmount: 0,
            totalApprovedRefundCount: 0,
            totalAutoRefundAmount: 0,
            totalAutoRefundCount: 0,
          };
        }
      };

      const refundForApprovalStats = async (transactions) => {
        await transactions.forEach(async (transaction) => {
          await refundForApprovalPayloadLoop(transaction);
        });
      };

      const refundFATransaction = (filters, startKeys) =>
        this.repository.getFARefundTransaction(filters, startKeys, 'xendit');

      const getRefundForApprovalLoop = async (startKeys) => {
        const transactions = await refundFATransaction(payload, startKeys);
        if (transactions.count > 0) {
          await refundForApprovalStats(transactions);
        }
        if (typeof transactions.lastKey !== 'undefined') {
          await getRefundForApprovalLoop(transactions.lastKey);
        }
      };
      if (refundRange) {
        await getRefundForApprovalLoop([]);
      } else {
        await getRefundForApprovalLoop('');
      }
      return {
        Bill: Object.values(refundStats.bill),
        NonBill: Object.values(refundStats.nonbill),
      };
    } catch (error) {
      this.logger.error('Error Search Xendit Refund Summary Report: ', error.message);
      throw error;
    }
  }
}
module.exports = XenditRefund;
