const Service = require('../Service');

class Dashboard extends Service {
  constructor(container) {
    const {
      snapshotRepository,
      transactionRepository,
      channelRepository,
      gatewayRepository,
      configRepository,
      DateHelper,
      Mapper,
      Performance,
      paymentGateway,
      config,
    } = container;
    super(snapshotRepository, container);
    this.transactionRepository = transactionRepository;
    this.snapshotRepository = snapshotRepository;
    this.channelRepository = channelRepository;
    this.paymentGateway = paymentGateway;
    this.dateHelper = DateHelper;
    this.mapper = Mapper;
    this.config = config;
    this.performance = Performance;
    this.gatewayRepository = gatewayRepository;
    this.configRepository = configRepository;
  }

  /**
   * Return online false
   * Skip Adyen payment gateway
   * as it is introducing a error blocker from node-fetch
   */
  async paymentGatewayUptime() {
    try {
      const adyenConfig = {
        countryCode: 'PH',
        amountCurrency: 'USD',
        amountValue: 1,
        merchantAccount: this.config.paymentGateway.PAYMENT_GATEWAY_MERCHANT_ACCOUNT,
        returnUrl: 'http://localhost:3000',
        reference: this.uuid.create(),
      };
      this.logger.info(adyenConfig);
      const result = await this.paymentGateway.paymentSession(adyenConfig);

      if (typeof result !== 'undefined' && result.paymentSession !== 'string') {
        return {
          online: true,
        };
      }
      return {
        online: false,
      };
    } catch (error) {
      this.logger.error(error);
      return {
        online: false,
      };
    }
  }

  async transactions(args) {
    const { startMonth, endMonth } = args.where;
    const months = this.dateHelper.monthsCovered(startMonth, endMonth);
    return this.repository.transactionsByMonth(months, 'transaction');
  }

  async performances() {
    try {
      const data = await this.performance.cpuUsage();
      return {
        cpu: data,
        memory: this.performance.usedMemory(),
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async overallTransactions(args) {
    try {
      const { startMonth, endMonth } = args.where;
      const snapshots = await this.repository.getSnapshotByDateRange('overall', startMonth, endMonth);
      let overall = 0;
      let success = 0;
      let failed = 0;
      let refused = 0;
      let adyen = 0;
      let gcash = 0;
      snapshots.forEach((snapshot) => {
        const payload = JSON.parse(snapshot.payload);
        overall += payload.overall;
        success += payload.success;
        failed += payload.failed;
        refused += payload.refused;
        if (typeof payload.adyen !== 'undefined') {
          adyen += payload.adyen;
        }
        if (typeof payload.gcash !== 'undefined') {
          gcash += payload.gcash;
        }
      });

      return {
        overall,
        success,
        failed,
        refused,
        adyen,
        gcash,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async overallupdatedat() {
    try {
      let lastUpdatedAt;
      const overallSnapshots = await this.repository.getLastUpdatedAt();
      overallSnapshots.forEach((snapshot) => {
        if (typeof lastUpdatedAt === 'undefined') {
          lastUpdatedAt = snapshot.updatedAt;
        }
        if (typeof lastUpdatedAt !== 'undefined' && new Date(lastUpdatedAt) <= new Date(snapshot.updatedAt)) {
          lastUpdatedAt = snapshot.updatedAt;
        }
      });
      return {
        lastUpdatedAt,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async search(data) {
    const cursors = [
      {
        channelId: '',
        paymentMethod: '',
      },
    ];
    let filteredData, allIds;
    if (Object.keys(data.filter).length <= 0) {
      filteredData = await this.gatewayRepository.listAll(data);
      allIds = await this.gatewayRepository.getAllData();
    } else {
      const searchData = await this.gatewayRepository.searchFilter(data);
      allIds = await this.gatewayRepository.searchFilterNoPaginate(data);
      filteredData = [];
      const filteredCount = searchData.count;
      for (let i = 0; i < data.pagination.limit; i += 1) {
        if (
          typeof searchData[i] !== 'undefined' &&
          (data.pagination.limit >= filteredCount || data.pagination.limit <= filteredCount)
        ) {
          filteredData.push(searchData[i]);
        }
      }
    }
    const filterCount = filteredData.count;
    if (filterCount <= 0) {
      return { filteredData: [], count: 0, cursors: [''] };
    }
    for (let i = 1; i < Math.ceil(allIds.count / data.pagination.limit); i += 1) {
      cursors.push({
        channelId: allIds[i * data.pagination.limit - 1].channelId,
        paymentMethod: allIds[i * data.pagination.limit - 1].paymentMethod,
      });
    }

    // Get Channel Data
    if (filteredData.length > 0) {
      const channelIds = filteredData.map((fdata) => fdata.channelId);
      const channels = await this.channelRepository.whereIn(channelIds);
      if (channels.length > 0) {
        filteredData = await this.mapper(filteredData, channels, 'channel', 'channelId');
      }
    }
    return {
      filteredData,
      count: allIds.count,
      cursors,
    };
  }
}

module.exports = Dashboard;
