const Service = require('../Service');

class Batch extends Service {
  constructor(container) {
    const { batchFileRepository } = container;
    super(batchFileRepository, container);
  }

  async search(data) {
    try {
      const filteredData = await this.repository.searchFilter(data);
      return {
        filteredData,
        count: filteredData.count,
        lastKey: JSON.stringify(filteredData.lastKey),
      };
    } catch (error) {
      this.logger.error('Search Batch File', error.message);
      throw error;
    }
  }
}

module.exports = Batch;
