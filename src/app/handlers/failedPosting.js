const Service = require('../Service');
const { injectChannelName } = require('../../infra/utils/formatter');

class FailedPosting extends Service {
  constructor(container) {
    const { settlementRepository } = container;
    super(settlementRepository, container);
  }

  async failedReports(data) {
    try {
      const payload = data;
      payload.filter.status = 'POSTING_FAILED';
      const res = await this.searchDataForReport(payload);
      const injectedResult = await injectChannelName(res, async (channelIds) =>
        this.channelRepository.getBatch(channelIds)
      );
      return injectedResult;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
module.exports = FailedPosting;
