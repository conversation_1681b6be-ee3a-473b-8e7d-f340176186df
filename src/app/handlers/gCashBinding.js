const Service = require('../Service');

class GCashBinding extends Service {
  constructor(container) {
    const { logger, config, authClient, channelRepository, gCashBindRepository } = container;
    super(channelRepository, container);
    this.logger = logger;
    this.config = config;
    this.authClient = authClient;
    this.gCashBindRepository = gCashBindRepository;
  }

  async gCashBindingReport(data) {
    try {
      let gCashBindData;

      if (data.filter?.uuid) {
        gCashBindData = await this.gCashBindRepository.getByUuid(data.filter.uuid, data.filter);
      } else if (data.filter?.bindingRequestID) {
        gCashBindData = await this.gCashBindRepository.getByBindingRequestID(data.filter.bindingRequestID, data.filter);
      } else {
        gCashBindData = await this.gCashBindRepository.getAll(data);
      }

      const transformedData = this.gCashBindReportTransformation(gCashBindData);

      return {
        lastKey: JSON.stringify(gCashBindData.lastKey),
        filteredData: transformedData,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async unBindGcashRecord({ bindingRequestID }, httpInfo, currentUser) {
    try {
      const gCashBindData = await this.gCashBindRepository.getByBindingRequestID(bindingRequestID);

      if (!gCashBindData || gCashBindData?.count < 1) {
        throw this.errors.bindingNotFound();
      }

      if (gCashBindData[0].status !== 'ACTIVE') {
        const errorMessage =
          gCashBindData[0].status === 'PROCESSING'
            ? 'Account is under PROCESSING'
            : `Account already ${gCashBindData[0].status}`; // INACTIVE | UNBIND
        throw new Error(errorMessage);
      }

      const channel = await this.channelRepository.getById(gCashBindData[0].channelId);

      if (!channel) {
        throw this.errors.channelNotFound();
      }

      const token = await this.authClient.accessToken(
        {
          clientId: channel.clientId,
          clientSecret: channel.clientSecret,
        },
        this.config
      );

      if (!token?.accessToken) {
        throw this.errors.accessTokenRequest();
      }

      const unBindGCashRequest = {
        uuid: gCashBindData[0].uuid,
        bindingRequestID: gCashBindData[0].bindingRequestID,
      };

      const gCashUnbind = await this.authClient.gCashUnbind(unBindGCashRequest, this.config, token.accessToken);

      if (!gCashUnbind || gCashUnbind?.status !== 200) {
        const unBindErrorMessage =
          gCashUnbind?.data?.message || gCashUnbind?.error?.details || 'Payment Service API - Internal server error';
        this.logger.error(JSON.stringify({ bindingRequestID, message: gCashUnbind }));
        throw new Error(unBindErrorMessage);
      }

      const category = this.category(this.repoName, 'unbind');
      await this.auditLogs(
        {
          ...httpInfo,
          oldValue: { bindingRequestID, status: gCashBindData[0].status },
          newValue: { bindingRequestID, status: gCashUnbind.data?.status ?? '' },
        },
        currentUser,
        category
      );

      return {
        status: gCashUnbind.data?.status,
        uuid: gCashBindData[0].uuid,
        bindingRequestID: gCashBindData[0].bindingRequestID,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
module.exports = GCashBinding;
