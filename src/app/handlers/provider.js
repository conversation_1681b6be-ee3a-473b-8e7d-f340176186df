const Service = require('../Service');

class Provider extends Service {
  constructor(container) {
    const { providerRepository } = container;
    super(providerRepository, container);
  }

  async search(data) {
    const results = await this.searchDataWithCursors(data);
    const filteredData = [];
    let limit = 0;
    Object.keys(results.filteredData).forEach((key) => {
      if (!Number.isNaN(Number(key))) {
        if (limit < data.pagination.limit) {
          if (typeof results.filteredData[key].isEnabled === 'undefined') {
            results.filteredData[key].isEnabled = false;
          }
          filteredData.push(results.filteredData[key]);
        }
        limit += 1;
      }
    });
    return {
      filteredData,
      count: results.count,
      cursors: results.cursors,
    };
  }

  async updateProvider(data, id, httpInfo, currentUser) {
    const oldValue = await this.repository.getById(id);
    // Update Data
    return this.update(
      {
        ...data,
      },
      id,
      httpInfo,
      currentUser,
      oldValue
    );
  }
}

module.exports = Provider;
