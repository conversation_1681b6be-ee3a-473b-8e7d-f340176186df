const Channel = require('./channel');
const Role = require('./role');
const User = require('./user');
const Audit = require('./audit');
const Reports = require('./reports');
const Config = require('./config');
// const Dashboard = require('./dashboard');
// const Provider = require('./provider');
// const Archive = require('./archive');
// const Mid = require('./mid');
// const Bank = require('./bank');
const Batch = require('./batch');
// const Voucher = require('./voucher');
// const GCashRefund = require('./gcashRefund');
// const InstallmentMId = require('./installmentMId');
// const CardRefund = require('./cardRefund');
// const BillLinerConfig = require('./billLiner');
const XenditRefund = require('./xenditRefund');
const PostPaymentConfig = require('./postPaymentConfig');
// const GCashBinding = require('./gCashBinding');
const ConvenienceFee = require('./convenienceFee');
const ConvenienceFeeBrand = require('./convenienceFeeBrand');
const FailedPosting = require('./failedPosting');
const PayByLink = require('./payByLink');
class Handlers {
  constructor(container) {
    return {
      Channel: new Channel(container),
      Role: new Role(container),
      User: new User(container),
      Audit: new Audit(container),
      Report: new Reports(container),
      Config: new Config(container),
      // GCashBinding: new GCashBinding(container),
      // Dashboard: new Dashboard(container),
      // Provider: new Provider(container),
      // Archive: new Archive(container),
      // Mid: new Mid(container),
      // Bank: new Bank(container),
      Batch: new Batch(container),
      // Voucher: new Voucher(container),
      // GCashRefund: new GCashRefund(container),
      // InstallmentMId: new InstallmentMId(container),
      // CardRefund: new CardRefund(container),
      // BillLinerConfig: new BillLinerConfig(container),
      XenditRefund: new XenditRefund(container),
      PostPaymentConfig: new PostPaymentConfig(container),
      ConvenienceFee: new ConvenienceFee(container),
      ConvenienceFeeBrand: new ConvenienceFeeBrand(container),
      FailedPosting: new FailedPosting(container),
      PayByLink: new PayByLink(container),
    };
  }
}

module.exports = Handlers;
