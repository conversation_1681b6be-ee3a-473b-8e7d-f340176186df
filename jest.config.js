module.exports = {
  // The test environment that will be used for testing
  testEnvironment: 'node',

  // The glob patterns <PERSON><PERSON> uses to detect test files
  testMatch: ['**/test/**/*.spec.js'],

  // An array of regexp pattern strings that are matched against all test paths
  // matched tests are skipped
  testPathIgnorePatterns: ['/node_modules/'],

  // Indicates whether each individual test should be reported during the run
  verbose: true,

  // Automatically clear mock calls and instances between every test
  clearMocks: true,

  // Indicates whether the coverage information should be collected while executing the test
  collectCoverage: false,

  // The directory where Jest should output its coverage files
  coverageDirectory: 'coverage',

  // An array of regexp pattern strings used to skip coverage collection
  coveragePathIgnorePatterns: ['/node_modules/'],

  // A list of reporter names that <PERSON><PERSON> uses when writing coverage reports
  coverageReporters: ['json', 'text', 'lcov', 'clover'],

  // The root directory that Je<PERSON> should scan for tests and modules within
  rootDir: '.',

  // A list of paths to directories that <PERSON><PERSON> should use to search for files in
  roots: ['<rootDir>'],

  // Setup files after environment is set up
  setupFilesAfterEnv: ['<rootDir>/test/jest.setup.js'],

  // The maximum amount of workers used to run your tests
  maxWorkers: '50%',

  // An array of directory names to be searched recursively up from the requiring module's location
  moduleDirectories: ['node_modules', '<rootDir>'],

  // A map from regular expressions to module names that allow to stub out resources
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
};
