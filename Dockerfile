ARG ARTIFACTORY_URL
ARG NODE_VERSION=22.14.0
# =========================================================================
# ============================ base IMAGE ==============================
# =========================================================================
FROM $ARTIFACTORY_URL/isg-gpayo-docker-local/node:v$NODE_VERSION AS base

RUN apk --no-cache add \
    bash \
    g++ \
    make \
    python3 \
    zlib-dev \
    libc-dev \
    bsd-compat-headers \
    py-setuptools \
    openssl-dev \
    musl-dev

# Stage 1: Build
FROM base AS deps
ARG JFROG_USERNAME
ARG JFROG_PASSWORD

RUN cp /dev/null /etc/apk/repositories
RUN echo "https://$JFROG_USERNAME:$<EMAIL>/artifactory/hmd-alpinelinux/v3.19/main" >>/etc/apk/repositories
RUN echo "https://$JFROG_USERNAME:$<EMAIL>/artifactory/hmd-alpinelinux/v3.19:/community" >>/etc/apk/repositories

WORKDIR /home/<USER>/app

COPY package*.json ./

RUN npm install -g husky@9.1.5  \
    && npm ci --omit=dev --verbose

# # Stage 2: Runtime
FROM base AS runner

RUN cp /dev/null /etc/apk/repositories
RUN echo "https://$JFROG_USERNAME:$<EMAIL>/artifactory/hmd-alpinelinux/v3.19/main" >>/etc/apk/repositories
RUN echo "https://$JFROG_USERNAME:$<EMAIL>/artifactory/hmd-alpinelinux/v3.19:/community" >>/etc/apk/repositories

# Install runtime dependencies
RUN apk --no-cache add \
  lz4-dev \
  cyrus-sasl \
  ca-certificates

WORKDIR /home/<USER>/app

# Copy application files from the base stage
COPY --from=deps /home/<USER>/app/node_modules ./node_modules
COPY package*.json ./
COPY index.js ./
COPY config ./config
COPY src ./src

# Uncomment this if local testing/development
# COPY .env.example .env

USER node
EXPOSE 3000

ENTRYPOINT ["node", "index.js"]
