const path = require('path');
const replace = require('replace-in-file');
const fs = require('fs').promises; // Use fs.promises to work with promises
const Listr = require('listr');
const { writeFileSync } = require('fs');

const srcPath = path.join(__dirname, '..', 'src');
const testPath = path.join(__dirname, '..', 'test');
const srcAndTestPath = `{${testPath}/unit,${srcPath}}`;
const routerPath = path.join(srcPath, 'interfaces', 'http', 'router.js');
const containerPath = path.join(srcPath, 'container.js');

const tasks = new Listr([
  {
    title: 'Remove UsersController routes',
    async task() {
      try {
        const results = await replace({
          files: routerPath,
          from: /\s*apiRouter.*UsersController'\)\);/,
          to: '',
        });

        return results;
      } catch (error) {
        return JSON.stringify(error);
      }
    },
  },
  {
    title: 'Remove example files from DI container',
    async task() {
      try {
        const results = await replace({
          files: containerPath,
          from: [
            /\s*const \{(\n.*)+.*app\/user'\);/,
            /\s*const.*UsersRepository'\);/,
            /\s*const.*UserSerializer'\);/,
            /, User: UserModel/,
            /\s*usersRepository.*\}\]/,
            /,\s*UserModel/,
            /\s+createUser(.|\n)+.*DeleteUser\n/,
            /\s+userSerializer: UserSerializer\n/,
          ],
          to: '',
        });

        return results;
      } catch (error) {
        return JSON.stringify(error);
      }
    },
  },
  {
    title: 'Delete example files and tests',
    async task() {
      try {
        const results = await Promise.all([
          fs.rm(path.join(srcAndTestPath, 'app', 'user'), { recursive: true }),
          fs.rm(path.join(srcAndTestPath, 'domain', 'user'), { recursive: true }),
          fs.rm(path.join(srcAndTestPath, 'infra', 'user'), { recursive: true }),
          fs.rm(path.join(srcAndTestPath, 'interfaces', 'http', 'user'), { recursive: true }),
          fs.rm(path.join(srcPath, 'infra', 'database', 'migrate'), { recursive: true }),
          fs.rm(path.join(srcPath, 'infra', 'database', 'seeds'), { recursive: true }),
          fs.rm(path.join(srcPath, 'infra', 'database', 'models', 'User.js')),
          fs.rm(path.join(testPath, 'features', 'api', 'users'), { recursive: true }),
          fs.rm(path.join(testPath, 'support', 'factories'), { recursive: true }),
        ]);

        return results;
      } catch (error) {
        console.error(JSON.stringify(error));
        return JSON.stringify(error);
      }
    },
  },
  {
    title: 'Remove example data from swagger.json',
    task() {
      try {
        const results = writeFileSync(
          path.join(srcPath, 'interfaces', 'http', 'swagger', 'swagger.json'),
          JSON.stringify(
            {
              openapi: '3.0.0',
              info: {
                title: 'Node API boilerplate',
                version: 'v1',
              },
              servers: [
                {
                  description: 'Local server',
                  url: '/api',
                },
              ],
            },
            null,
            '  '
          )
        );

        return results;
      } catch (error) {
        return JSON.stringify(error);
      }
    },
  },
  {
    title: 'Remove cleanup script from package.json',
    async task() {
      try {
        const results = await replace({
          files: path.join(__dirname, '..', 'package.json'),
          from: /,\s*"cleanup.*cleanup\.js"/,
          to: '',
        });

        return results;
      } catch (error) {
        return JSON.stringify(error);
      }
    },
  },
]);

tasks.run();
