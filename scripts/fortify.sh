#!/bin/bash

echo "Downloading Fortify uploader..."
ls FodUpload.jar || wget https://github.com/fod-dev/fod-uploader-java/releases/download/v5.0.0/FodUpload.jar

echo "Zipping files..."
zip -r code.zip . -x *.git* # you can include an -i flag to specify which file extensions to include; this will speed up the scan

echo "Uploading..."
java -jar FodUpload.jar \
-z code.zip \
-ep 2 \
-ac ${FORTIFY_AC} \
-rid ${RELEASE_ID} \
-portalurl https://ams.fortify.com \
-apiurl https://api.ams.fortify.com \
|| echo "An error occurred. Please report the specific error to the DevOps team."