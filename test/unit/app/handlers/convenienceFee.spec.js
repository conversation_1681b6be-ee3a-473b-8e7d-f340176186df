const chai = require('chai');
const sinon = require('sinon');
const { expect } = chai;

// Import the CF class and mocks
const ConvenienceFee = require('../../../../src/app/handlers/convenienceFee');
const uuid = require('../../../../src/infra/utils/Uuid');
const category = require('../../../../src/infra/utils/Category');
const error = require('../../../../src/domain/errors');
const formatter = require('../../../../src/infra/utils/formatter');

describe('App :: Handlers :: Convenience Fee', function () {
  let convenienceFeeService;
  let convenienceFeeRepositoryMock;
  let userRepositoryMock;
  let roleRepositoryMock;
  let channelRepositoryMock;
  let auditRepositoryMock;
  let loggerMock;

  beforeEach(() => {
    // Mock dependencies
    convenienceFeeRepositoryMock = {
      getById: sinon.stub(),
      listAll: sinon.stub(),
      getAllId: sinon.stub(),
      searchFilter: sinon.stub(),
      searchFilterNoPaginate: sinon.stub(),
      getCompositeKey: sinon.stub(),
    };

    userRepositoryMock = {
      getById: sinon.stub(),
    };

    roleRepositoryMock = {
      getById: sinon.stub(),
    };

    channelRepositoryMock = {
      getByChannelId: sinon.stub(),
    };

    auditRepositoryMock = {
      add: sinon.stub(),
    };

    loggerMock = {
      error: sinon.stub(),
    };

    // Instantiate CF with mock dependencies
    convenienceFeeService = new ConvenienceFee({
      convenienceFeeRepository: convenienceFeeRepositoryMock,
      userRepository: userRepositoryMock,
      roleRepository: roleRepositoryMock,
      auditRepository: auditRepositoryMock,
      channelRepository: channelRepositoryMock,
      uuid: uuid,
      logger: loggerMock,
      Category: category,
      errors: error,
      formatter: formatter,
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('show()', function () {
    it('should return the convenience fee data when found', async function () {
      const id = '123';
      const mockCFData = [{ id, name: 'Test CF' }];

      convenienceFeeRepositoryMock.getById.resolves(mockCFData);

      const result = await convenienceFeeService.show({ id: { id } });

      expect(convenienceFeeRepositoryMock.getById.calledOnceWith(id)).to.be.true;
      expect(result).to.deep.equal(mockCFData[0]);
    });

    it('should log an error and throw if repository.getById fails', async function () {
      const id = '123';
      const mockError = new Error('Database error');

      convenienceFeeRepositoryMock.getById.rejects(mockError);

      try {
        await convenienceFeeService.show({ id: { id } });
        throw new Error('Expected show() to throw');
      } catch (error) {
        expect(error).to.equal(mockError);
        expect(loggerMock.error.calledOnce).to.be.true;
      }
    });
  });

  describe('search()', function () {
    it('should return the convenience fee data when found with empty filter', async function () {
      const mockCFData = [
        {
          name: 'TEST',
          channelId: 'TEST',
          brand: 'TestBrands',
          id: 'd2f78dfa-adff-4311-a239-d6a8ea630639',
        },
      ];
      const mockAllIdData = [{ id: 'd2f78dfa-adff-4311-a239-d6a8ea630639' }];

      Object.defineProperty(mockCFData, 'count', { value: 1 });
      Object.defineProperty(mockAllIdData, 'count', { value: 1 });

      convenienceFeeRepositoryMock.listAll.resolves(mockCFData);
      convenienceFeeRepositoryMock.getAllId.resolves(mockAllIdData);

      const result = await convenienceFeeService.search({
        filter: {},
        pagination: { startKey: '', limit: 10 },
      });

      expect(convenienceFeeRepositoryMock.listAll.calledOnce).to.be.true;
      expect(convenienceFeeRepositoryMock.getAllId.calledOnce).to.be.true;
      expect(result).to.deep.equal({
        filteredData: mockCFData,
        count: 1,
        cursors: [''],
      });
    });

    it('should return filtered data when filter is provided', async function () {
      const mockCFData = [
        {
          name: 'TEST',
          channelId: 'TEST',
          brand: 'TestBrands',
          id: 'd2f78dfa-adff-4311-a239-d6a8ea630639',
        },
      ];
      const mockAllIdData = [{ id: 'd2f78dfa-adff-4311-a239-d6a8ea630639' }];

      Object.defineProperty(mockCFData, 'count', { value: 1 });
      Object.defineProperty(mockAllIdData, 'count', { value: 1 });

      convenienceFeeRepositoryMock.searchFilter.resolves(mockCFData);
      convenienceFeeRepositoryMock.searchFilterNoPaginate.resolves(mockAllIdData);

      const result = await convenienceFeeService.search({
        filter: { brand: 'TestBrands' },
        pagination: { startKey: '', limit: 10 },
      });

      expect(convenienceFeeRepositoryMock.searchFilter.calledOnce).to.be.true;
      expect(convenienceFeeRepositoryMock.searchFilterNoPaginate.calledOnce).to.be.true;
      expect(result.filteredData).to.have.lengthOf(1);
    });

    it('should log an error and throw if repository.listAll fails', async function () {
      const mockError = new Error('Database error');

      convenienceFeeRepositoryMock.listAll.rejects(mockError);

      try {
        await convenienceFeeService.search({
          filter: {},
          pagination: { startKey: '', limit: 10 },
        });
        throw new Error('Expected search() to throw');
      } catch (error) {
        expect(error).to.equal(mockError);
      }
    });
  });

  describe('createConvenienceFee()', function () {
    it('should successfully create a convenience fee when valid data is provided', async function () {
      const data = {
        channelId: 'TEST',
        brand: 'TestBrands',
        gatewayProcessor: 'gcash',
        paymentMethod: 'dragonpay_gcash',
        transactionType: 'Bill',
        convenienceFeeType: 'Off',
      };

      const id = 'd2f78dfa-adff-4311-a239-d6a8ea630639';
      const mockChannelData = [{ name: 'TEST', id }];
      Object.defineProperty(mockChannelData, 'count', { value: 1 });

      const mockCFData = [];
      Object.defineProperty(mockCFData, 'count', { value: 0 });

      channelRepositoryMock.getByChannelId.resolves(mockChannelData);
      convenienceFeeRepositoryMock.getCompositeKey.resolves(mockCFData);

      const expectedCreatedEntry = { ...data, id: sinon.match.string };
      convenienceFeeService.create = sinon.stub().resolves(expectedCreatedEntry);

      const result = await convenienceFeeService.createConvenienceFee(
        data,
        { browser: 'chrome' },
        '<EMAIL>'
      );

      expect(channelRepositoryMock.getByChannelId.calledOnceWith('TEST')).to.be.true;
      expect(convenienceFeeRepositoryMock.getCompositeKey.calledOnce).to.be.true;

      // Updated assertions to match new return format
      expect(result).to.have.property('created', 1);
      expect(result).to.have.property('skipped', 0);
      expect(result).to.have.property('entries');
      expect(result.entries).to.have.lengthOf(1);
      expect(result.entries[0]).to.include({
        brand: 'TestBrands',
        channelId: 'TEST',
        convenienceFeeType: 'Off',
        gatewayProcessor: 'gcash',
        paymentMethod: 'dragonpay_gcash',
        transactionType: 'Bill',
      });
    });

    it('should throw error when channel is not found', async function () {
      const data = {
        channelId: 'NONEXISTENT',
        brand: 'TestBrands',
        gatewayProcessor: 'gcash',
        paymentMethod: 'dragonpay_gcash',
        transactionType: 'Bill',
        convenienceFeeType: 'Off',
      };

      const mockChannelData = [];
      Object.defineProperty(mockChannelData, 'count', { value: 0 });

      channelRepositoryMock.getByChannelId.resolves(mockChannelData);

      convenienceFeeService.errors = {
        channelIdNotFound: () => new Error('CHANNEL_ID_NOT_FOUND'),
      };

      try {
        await convenienceFeeService.createConvenienceFee(data, { browser: 'chrome' }, '<EMAIL>');
        throw new Error('Expected createConvenienceFee() to throw');
      } catch (error) {
        expect(error.message).to.equal('CHANNEL_ID_NOT_FOUND');
        expect(loggerMock.error.calledOnce).to.be.true;
      }
    });

    it('should throw error when convenience fee already exists', async function () {
      const data = {
        channelId: 'TEST',
        brand: 'TestBrands',
        gatewayProcessor: 'gcash',
        paymentMethod: 'dragonpay_gcash',
        transactionType: 'Bill',
        convenienceFeeType: 'Off',
      };

      const id = 'd2f78dfa-adff-4311-a239-d6a8ea630639';
      const mockChannelData = [{ name: 'TEST', id }];
      Object.defineProperty(mockChannelData, 'count', { value: 1 });

      const mockCFData = [{ id: 'existing-cf-id' }];
      Object.defineProperty(mockCFData, 'count', { value: 1 });

      channelRepositoryMock.getByChannelId.resolves(mockChannelData);
      convenienceFeeRepositoryMock.getCompositeKey.resolves(mockCFData);

      convenienceFeeService.errors = {
        convenienceFeeUniqueness: () => new Error('CONVENIENCE_FEE_ALREADY_EXISTS'),
      };

      try {
        await convenienceFeeService.createConvenienceFee(data, { browser: 'chrome' }, '<EMAIL>');
        throw new Error('Expected createConvenienceFee() to throw');
      } catch (error) {
        expect(error.message).to.equal('CONVENIENCE_FEE_ALREADY_EXISTS');
        expect(loggerMock.error.calledOnce).to.be.true;
      }
    });
  });

  describe('createConvenienceFee() - Bulk Creation', function () {
    it('should successfully create multiple convenience fees when valid data is provided', async function () {
      const data = {
        channelId: 'TEST',
        brands: ['Smart', 'Sun'],
        gatewayProcessor: 'xendit',
        paymentMethods: ['CC_DC', 'PAYMAYA'],
        transactionType: 'Bill',
        convenienceFeeType: 'Flat',
        amount: 10,
      };

      const mockChannelData = [{ name: 'TEST CHANNEL', id: 'channel-id' }];
      Object.defineProperty(mockChannelData, 'count', { value: 1 });

      channelRepositoryMock.getByChannelId.resolves(mockChannelData);

      // Mock no existing conflicts for all combinations
      const mockNoConflictData = [];
      Object.defineProperty(mockNoConflictData, 'count', { value: 0 });
      convenienceFeeRepositoryMock.getCompositeKey.resolves(mockNoConflictData);

      // Mock successful creation for each combination
      const mockCreatedEntry = { id: 'created-id', ...data };
      convenienceFeeService.create = sinon.stub().resolves(mockCreatedEntry);

      const result = await convenienceFeeService.createConvenienceFee(
        data,
        { browser: 'chrome' },
        '<EMAIL>'
      );

      expect(channelRepositoryMock.getByChannelId.calledOnceWith('TEST')).to.be.true;
      expect(convenienceFeeRepositoryMock.getCompositeKey.callCount).to.equal(4); // 2 brands × 2 methods
      expect(convenienceFeeService.create.callCount).to.equal(4);
      expect(result.created).to.equal(4);
      expect(result.skipped).to.equal(0);
      expect(result.entries).to.have.lengthOf(4);
    });

    it('should handle partial conflicts and create only non-conflicting combinations', async function () {
      const data = {
        channelId: 'TEST',
        brands: ['BrandA', 'BrandB'],
        gatewayProcessor: 'xendit',
        paymentMethods: ['CC_DC', 'PAYMAYA'],
        transactionType: 'Bill',
        convenienceFeeType: 'Flat',
        amount: 10,
      };

      const mockChannelData = [{ name: 'TEST CHANNEL', id: 'channel-id' }];
      Object.defineProperty(mockChannelData, 'count', { value: 1 });

      channelRepositoryMock.getByChannelId.resolves(mockChannelData);

      // Mock conflicts for specific combinations
      convenienceFeeRepositoryMock.getCompositeKey.callsFake((compositeKey) => {
        const { brand, paymentMethod } = compositeKey;
        if (brand === 'BrandA' && paymentMethod === 'CC_DC') {
          const conflictData = [{ id: 'existing-id' }];
          Object.defineProperty(conflictData, 'count', { value: 1 });
          return Promise.resolve(conflictData);
        }
        const noConflictData = [];
        Object.defineProperty(noConflictData, 'count', { value: 0 });
        return Promise.resolve(noConflictData);
      });

      const mockCreatedEntry = { id: 'created-id', ...data };
      convenienceFeeService.create = sinon.stub().resolves(mockCreatedEntry);

      const result = await convenienceFeeService.createConvenienceFee(
        data,
        { browser: 'chrome' },
        '<EMAIL>'
      );

      expect(result.created).to.equal(3); // 4 combinations - 1 conflict
      expect(result.skipped).to.equal(1);
      expect(result.entries).to.have.lengthOf(3);
    });

    it('should throw error when all combinations have conflicts', async function () {
      const data = {
        channelId: 'TEST',
        brands: ['BrandA'],
        gatewayProcessor: 'xendit',
        paymentMethods: ['CC_DC'],
        transactionType: 'Bill',
        convenienceFeeType: 'Flat',
        amount: 10,
      };

      const mockChannelData = [{ name: 'TEST CHANNEL', id: 'channel-id' }];
      Object.defineProperty(mockChannelData, 'count', { value: 1 });

      channelRepositoryMock.getByChannelId.resolves(mockChannelData);

      // Mock conflict for the only combination
      const mockConflictData = [{ id: 'existing-id' }];
      Object.defineProperty(mockConflictData, 'count', { value: 1 });
      convenienceFeeRepositoryMock.getCompositeKey.resolves(mockConflictData);

      try {
        await convenienceFeeService.createConvenienceFee(data, { browser: 'chrome' }, '<EMAIL>');
        throw new Error('Expected createConvenienceFee() to throw');
      } catch (error) {
        expect(error.message).to.include('All Convenience Fee combinations already exist');
        expect(loggerMock.error.calledOnce).to.be.true;
      }
    });

    it('should throw error when channel is not found for bulk creation', async function () {
      const data = {
        channelId: 'NONEXISTENT',
        brands: ['BrandA'],
        gatewayProcessor: 'xendit',
        paymentMethods: ['CC_DC'],
        transactionType: 'Bill',
        convenienceFeeType: 'Flat',
      };

      const mockChannelData = [];
      Object.defineProperty(mockChannelData, 'count', { value: 0 });

      channelRepositoryMock.getByChannelId.resolves(mockChannelData);

      convenienceFeeService.errors = {
        channelIdNotFound: () => new Error('CHANNEL_ID_NOT_FOUND'),
      };

      try {
        await convenienceFeeService.createConvenienceFee(data, { browser: 'chrome' }, '<EMAIL>');
        throw new Error('Expected createConvenienceFee() to throw');
      } catch (error) {
        expect(error.message).to.equal('CHANNEL_ID_NOT_FOUND');
        expect(loggerMock.error.calledOnce).to.be.true;
      }
    });

    it('should route to single creation when brands and paymentMethods are not arrays', async function () {
      const data = {
        channelId: 'TEST',
        brand: 'SingleBrand', // not an array
        gatewayProcessor: 'xendit',
        paymentMethod: 'CC_DC', // not an array
        transactionType: 'Bill',
        convenienceFeeType: 'Off',
      };

      const mockChannelData = [{ name: 'TEST', id: 'channel-id' }];
      Object.defineProperty(mockChannelData, 'count', { value: 1 });

      const mockCFData = [];
      Object.defineProperty(mockCFData, 'count', { value: 0 });

      channelRepositoryMock.getByChannelId.resolves(mockChannelData);
      convenienceFeeRepositoryMock.getCompositeKey.resolves(mockCFData);

      const expectedResult = { ...data, id: 'created-id' };
      convenienceFeeService.create = sinon.stub().resolves(expectedResult);

      const result = await convenienceFeeService.createConvenienceFee(
        data,
        { browser: 'chrome' },
        '<EMAIL>'
      );

      expect(result.created).to.equal(1);
      expect(result.skipped).to.equal(0);
      expect(result.entries).to.have.lengthOf(1);
    });
  });

  describe('updateConvenienceFee()', function () {
    it('should successfully update a convenience fee when valid data is provided', async function () {
      const data = {
        channelId: 'TEST',
        brand: 'TestBrands',
        gatewayProcessor: 'gcash',
        paymentMethod: 'dragonpay_gcash',
        transactionType: 'Bill',
        convenienceFeeType: 'Flat',
      };

      const id = 'd2f78dfa-adff-4311-a239-d6a8ea630639';
      const mockExistingCF = [{ id, channelId: 'TEST' }];
      Object.defineProperty(mockExistingCF, 'count', { value: 1 });

      const mockChannelData = [{ name: 'TEST', id }];
      Object.defineProperty(mockChannelData, 'count', { value: 1 });

      const mockCFData = [{ id, channelId: 'TEST' }];
      Object.defineProperty(mockCFData, 'count', { value: 1 });

      convenienceFeeRepositoryMock.getById.resolves(mockExistingCF);
      channelRepositoryMock.getByChannelId.resolves(mockChannelData);
      convenienceFeeRepositoryMock.getCompositeKey.resolves(mockCFData);

      convenienceFeeService.update = sinon.stub().resolves(data);

      const result = await convenienceFeeService.updateConvenienceFee(
        data,
        { id },
        { browser: 'chrome' },
        '<EMAIL>'
      );

      expect(convenienceFeeRepositoryMock.getById.calledOnceWith(id)).to.be.true;
      expect(channelRepositoryMock.getByChannelId.calledOnceWith('TEST')).to.be.true;
      expect(result).to.deep.equal(data);
    });

    it('should throw error when convenience fee not found', async function () {
      const data = { channelId: 'TEST', brand: 'TestBrands' };
      const id = 'nonexistent-id';

      const mockCFData = [];
      Object.defineProperty(mockCFData, 'count', { value: 0 });

      convenienceFeeRepositoryMock.getById.resolves(mockCFData);

      convenienceFeeService.errors = {
        notFound: () => new Error('NOT_FOUND'),
      };

      try {
        await convenienceFeeService.updateConvenienceFee(data, { id }, { browser: 'chrome' }, '<EMAIL>');
        throw new Error('Expected updateConvenienceFee() to throw');
      } catch (error) {
        expect(error.message).to.equal('NOT_FOUND');
        expect(loggerMock.error.calledOnce).to.be.true;
      }
    });
  });

  describe('deleteConvenienceFee()', function () {
    it('should successfully delete a convenience fee when valid data is provided', async function () {
      const data = { reasonToDelete: 'No longer in use' };
      const id = 'd2f78dfa-adff-4311-a239-d6a8ea630639';

      const mockCFData = [{ id, name: 'TEST' }];
      Object.defineProperty(mockCFData, 'count', { value: 1 });

      convenienceFeeRepositoryMock.getById.resolves(mockCFData);

      convenienceFeeService.delete = sinon.stub().resolves({ id });

      const result = await convenienceFeeService.deleteConvenienceFee(
        data,
        { id },
        { browser: 'chrome' },
        '<EMAIL>'
      );

      expect(convenienceFeeRepositoryMock.getById.calledOnceWith(id)).to.be.true;
      expect(result).to.deep.equal({ id });
    });

    it('should throw error when convenience fee not found', async function () {
      const data = { reasonToDelete: 'No longer in use' };
      const id = 'nonexistent-id';

      const mockCFData = [];
      Object.defineProperty(mockCFData, 'count', { value: 0 });

      convenienceFeeRepositoryMock.getById.resolves(mockCFData);

      convenienceFeeService.errors = {
        notFound: () => new Error('NOT_FOUND'),
      };

      try {
        await convenienceFeeService.deleteConvenienceFee(data, { id }, { browser: 'chrome' }, '<EMAIL>');
        throw new Error('Expected deleteConvenienceFee() to throw');
      } catch (error) {
        expect(error.message).to.equal('NOT_FOUND');
        expect(loggerMock.error.calledOnce).to.be.true;
      }
    });
  });
});
