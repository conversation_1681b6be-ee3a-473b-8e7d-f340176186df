// Import Jest's mocking functionality
const Batch = require('../../../../src/app/handlers/batch');

describe('App :: Handlers :: Batch', () => {
  let batchService;
  let batchFileRepository;
  let loggerMock;
  beforeEach(() => {
    batchFileRepository = {
      searchFilter: jest.fn(),
    };
    loggerMock = {
      error: jest.fn(),
    };

    batchService = new Batch({
      batchFileRepository,
      logger: loggerMock,
    });

    jest.spyOn(batchFileRepository, 'searchFilter').mockImplementation();
  });

  describe('batchFileReport', () => {
    it('should return the result from searchData when successful', async () => {
      // Mock data
      const mockData = {
        filter: {},
        pagination: { limit: 10, startKey: '' },
      };

      const mockResolvedValueForSearchFilter = [
        {
          filename: 'test1.csv',
          createdAt: '2025-04-25T09:51:11.218Z',
        },
      ];
      mockResolvedValueForSearchFilter.count = 1;
      mockResolvedValueForSearchFilter.lastKey = { id: 'last-key' };

      const expectedResult = {
        filteredData: mockResolvedValueForSearchFilter,
        count: 1,
        lastKey: JSON.stringify({ id: 'last-key' }),
      };

      // Mock searchData to return expected result
      batchFileRepository.searchFilter.mockResolvedValue(mockResolvedValueForSearchFilter);

      // Call the search method
      const result = await batchService.search(mockData);
      // Assertions
      expect(batchFileRepository.searchFilter).toHaveBeenCalledWith(mockData);
      expect(batchFileRepository.searchFilter).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when batch report fails', async () => {
      // Mock data
      const mockData = {
        filter: {},
        pagination: { limit: 10, startKey: '' },
      };

      // Mock searchData to throw error
      batchFileRepository.searchFilter.mockImplementation(() => {
        throw new Error('Database error');
      });

      // Call the search method and expect it to throw
      try {
        await batchService.search(mockData);
        // If we reach here, the test should fail
        fail('Expected search to throw an error');
      } catch (err) {
        // Assertions
        expect(batchFileRepository.searchFilter).toHaveBeenCalledWith(mockData);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });
});
