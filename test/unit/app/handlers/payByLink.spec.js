const PayByLink = require('../../../../src/app/handlers/payByLink');
const payByLinkUseCases = require('../../../../src/domain/payByLink/useCases');
const { getPhDateTime } = require('../../../../src/infra/utils/date');
const { generateSessionId } = require('../../../../src/infra/utils/sessionIdGenerator');

jest.mock('microtime');
jest.mock('../../../../src/domain/payByLink/useCases');
jest.mock('../../../../src/infra/utils/date');
jest.mock('../../../../src/infra/utils/sessionIdGenerator');

describe('PayByLink', () => {
  let payByLink;
  let mockContainer;

  // Shared data/mocks
  const inputData = {
    channelId: 'channel123',
    amountValue: 10000,
    description: 'Test payment',
    emailAddress: '<EMAIL>',
    mobileNumber: '09123456789',
    notificationPreference: ['email', 'whatsapp', 'viber'],
  };

  const mockChannel = {
    id: 'channel123',
    channelCode: 'TEST',
  };

  beforeEach(() => {
    mockContainer = {
      logger: { info: jest.fn() },
      uuid: { create: jest.fn().mockReturnValue('test-uuid') },
      channelRepository: { getVerifiedPayByLinkChannelById: jest.fn() },
      payByLinkRepository: { createPayByLinkReportEntry: jest.fn() },
      transactionRepository: { createPayByLinkTransaction: jest.fn() },
      xenditService: { createInvoice: jest.fn() },
    };
    payByLink = new PayByLink(mockContainer);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('App :: Handlers :: Pay By Link', () => {
    it('should successfully create pay by link', async () => {
      // Mocks specific to end-to-end pay by link generation
      require('microtime').now = jest.fn().mockReturnValue(1234567890);
      getPhDateTime.mockReturnValue('2025-07-16T10:30:00.000Z');
      generateSessionId.mockReturnValue('CwBQBIBeSYqJTnlSh99jjn4beu');

      process.env.PAYBYLINK_PAYMENT_METHODS = 'CREDIT_CARD,DD_BPI,GRABPAY';
      process.env.PAYBYLINK_INVOICE_DURATION = '86400';

      payByLinkUseCases.create.mockReturnValue(inputData);
      mockContainer.channelRepository.getVerifiedPayByLinkChannelById.mockResolvedValue(mockChannel);
      mockContainer.xenditService.createInvoice.mockResolvedValue({
        external_id: 'TEST1234567890',
        invoice_url: 'https://checkout-staging.xendit.co/web/687765d3f72950f6569edab5',
        status: 'PENDING',
        merchant_name: 'Test Merchant',
        id: 'xendit_invoice_123',
        amount: 10000,
        description: 'Test payment',
      });

      const result = await payByLink.createPayByLink(inputData);

      // Assertions
      expect(payByLinkUseCases.create).toHaveBeenCalledWith(inputData);
      expect(mockContainer.channelRepository.getVerifiedPayByLinkChannelById).toHaveBeenCalledWith('channel123');
      expect(mockContainer.xenditService.createInvoice).toHaveBeenCalledWith({
        external_id: 'TEST1234567890',
        amount: 10000,
        description: 'Test payment',
        invoice_duration: '86400',
        customer: {
          email: '<EMAIL>',
          mobile_number: '09123456789',
        },
        customer_notification_preference: {
          invoice_created: ['email', 'whatsapp', 'viber'],
        },
        payment_methods: ['CREDIT_CARD', 'DD_BPI', 'GRABPAY'],
      });
      expect(mockContainer.payByLinkRepository.createPayByLinkReportEntry).toHaveBeenCalledWith({
        paymentId: 'TEST1234567890',
        channelId: 'channel123',
        paymentGateway: 'xendit',
        paymentLink: 'https://checkout-staging.xendit.co/web/687765d3f72950f6569edab5',
        status: 'PENDING',
        merchantAccount: 'Test Merchant',
        merchantReference: 'xendit_invoice_123',
        amount: 10000,
        description: 'Test payment',
        linkType: '',
      });
      expect(mockContainer.transactionRepository.createPayByLinkTransaction).toHaveBeenCalledWith({
        paymentId: 'TEST1234567890',
        customerId: 'PBL-test-uuid',
        sessionId: 'CwBQBIBeSYqJTnlSh99jjn4beu',
        createDateTime: '2025-07-16T10:30:00.000Z',
        channelId: 'channel123',
        status: 'PENDING',
        gatewayProcessor: 'xendit',
        paymentMethod: 'paybylink',
        totalAmount: 10000,
      });
      expect(result).toEqual({
        externalId: 'TEST1234567890',
        paymentGateway: 'xendit',
        paymentLink: 'https://checkout-staging.xendit.co/web/687765d3f72950f6569edab5',
        status: 'PENDING',
        merchantAccount: 'Test Merchant',
        merchantReference: 'xendit_invoice_123',
        amount: 10000,
        description: 'Test payment',
        linkType: '',
      });
    });

    it('should fail when channel is not found', async () => {
      mockContainer.channelRepository.getVerifiedPayByLinkChannelById.mockResolvedValue(null);
      await expect(payByLink.createPayByLink(inputData)).rejects.toThrow('Channel not found.');
    });

    it('should fail when channel code is missing', async () => {
      mockContainer.channelRepository.getVerifiedPayByLinkChannelById.mockResolvedValue({
        ...mockChannel,
        channelCode: null,
      });
      await expect(payByLink.createPayByLink(inputData)).rejects.toThrow('Channel code not found.');
    });
  });
});
