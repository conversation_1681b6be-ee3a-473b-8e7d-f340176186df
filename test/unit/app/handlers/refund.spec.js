const XenditRefund = require('../../../../src/app/handlers/xenditRefund');
const { getPhDateTime } = require('../../../../src/infra/utils/date');
const category = require('../../../../src/infra/utils/Category');

describe('App :: Handlers :: Refund', () => {
  let refundRepository;
  let transactionRepository;
  let snapshotRepository;
  let eventLogsRepository;
  let settlementRepository;
  let userRepository;
  let roleRepository;
  let auditRepository;
  let config;
  let configRepository;
  let logger;
  let refundService;
  let uuid;
  let kafka;
  beforeEach(() => {
    refundRepository = {
      searchFilter: jest.fn(),
      getFARefundTransaction: jest.fn(),
      getByPaymentIdForApproval: jest.fn(),
      update: jest.fn(),
      getPaymentIdTotalRefundAmount: jest.fn(),
    };

    transactionRepository = {
      getByPaymentId: jest.fn(),
      update: jest.fn(),
      searchFilter: jest.fn(),
    };

    settlementRepository = {
      getByPaymentAndTransactionId: jest.fn(),
      getByPaymentId: jest.fn(),
      update: jest.fn(),
      searchFilter: jest.fn(),
    };

    logger = {
      error: jest.fn(),
      info: jest.fn(),
    };

    snapshotRepository = {
      getSnapshotByYear: jest.fn(),
    };

    uuid = {
      create: jest.fn(),
    };

    kafka = {
      produce: jest.fn(),
    };

    config = {
      paymentGateway: {
        REFUND_APPROVAL_EXPECTED_PAYMENT_GATEWAY: JSON.stringify([
          'xendit',
          'card',
          'card_straight',
          'card_installment',
        ]),
      },
      kafka: {
        topics: {
          cc_refund: 'cc_refund',
        },
        events: {
          cc_refund: 'cc_refund',
        },
        schemas: {
          refund: 'refund',
        },
      },
    };

    eventLogsRepository = {
      registerEventLog: jest.fn(),
      registerFailedEvent: jest.fn(),
    };

    userRepository = {
      getById: jest.fn(),
    };

    roleRepository = {
      getById: jest.fn(),
    };

    auditRepository = {
      add: jest.fn(),
    };

    refundService = new XenditRefund({
      refundRepository,
      transactionRepository,
      snapshotRepository,
      eventLogsRepository,
      configRepository,
      settlementRepository,
      config,
      logger,
      uuid,
      Category: category,
      kafka: kafka,
      userRepository,
      roleRepository,
      auditRepository,
    });
  });

  describe('refundApproval', () => {
    it('should return the result from xendit approval when successful', async () => {
      // Mock data
      const mockData = {
        filter: {
          paymentGateway: 'xendit',
        },
        pagination: { limit: 10, startKey: '' },
      };
      const mockUser = {
        ewalletAssignedChannels: JSON.stringify([{ channelId: global.faker.string.uuid() }]),
        billType: 'NonBill',
      };

      const mockTransactionItem = {
        paymentId: 'SAMPLE-paymentId',
        refundId: global.faker.string.uuid(),
        refundRejectedTimestamp: '2025-03-29T00:51:56.222Z',
        refundApprovedTimestamp: '2025-03-29T00:51:56.222Z',
        accountNumber: '**********',
        amountValue: '500',
        channelName: 'DNO-PrepaidFiber',
        postedTimestamp: '2025-03-28T13:04:04.674Z',
        paymentId: 'SAMPLE-paymentId',
        refundAmount: '50000',
        refundApprovalStatus: 'For Approval',
        refundReason: 'Auto Refund',
        refundRejectedTimestamp: null,
        status: 'PAYMENT_AUTHORIZED',
        timestamp: '2025-03-29T00:51:56.222Z',
        billType: 'NonBill',
        convenienceFee: 'CF',
        paymentMethod: 'bpi',
        finalAmount: 1,
        acquirementId: global.faker.string.uuid(),
        hasConvenienceFee: true,
        mobileNumber: '********',
        refundStatus: 'STATUS',
        refundType: 'REFUND_TYPE',
        isAutoRefund: true,
        requestTimeStamp: '2025-03-29T00:51:56.222Z',
      };

      const mockDynamooseResult = [{ ...mockTransactionItem }];
      mockDynamooseResult.count = mockDynamooseResult.length;
      mockDynamooseResult.lastKey = { id: 'last-key' };

      const gcashTransId = mockTransactionItem.acquirementId;
      const refundDate = mockTransactionItem.refundRejectedTimestamp || mockTransactionItem.refundApprovedTimestamp;
      delete mockTransactionItem.acquirementId;
      delete mockTransactionItem.refundApprovedTimestamp;
      delete mockTransactionItem.id;
      delete mockTransactionItem.isAutoRefund;

      const expectedResult = {
        filteredData: [
          {
            ...mockTransactionItem,
            paymentMethod: 'BPI',
            refundDate,
            gcashTransId,
            refundType: 'Auto',
          },
        ],
        lastKey: JSON.stringify({ id: 'last-key' }),
      };

      refundRepository.searchFilter.mockResolvedValue(mockDynamooseResult);

      const result = await refundService.refundApproval(mockData, mockUser);

      expect(refundRepository.searchFilter).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when xenditApproval fails', async () => {
      // Mock data
      const mockData = {
        filter: {
          paymentGateway: 'card',
        },
        pagination: { limit: 10, startKey: '' },
      };
      const mockUser = {
        ewalletAssignedChannels: JSON.stringify([{ channelId: global.faker.string.uuid() }]),
        billType: 'NonBill',
      };

      refundRepository.searchFilter.mockImplementation(() => {
        throw new Error('Error Search Card Refund Data');
      });

      try {
        await refundService.refundApproval(mockData, mockUser);
        // If we reach here, the test should fail
        fail('Expected refundApproval to throw an error');
      } catch (err) {
        // Assertions
        expect(refundRepository.searchFilter).toHaveBeenCalled();
        expect(logger.error).toHaveBeenCalled();
        expect(err.message).toBe('Error Search Card Refund Data');
      }
    });

    it('should return success when approval push through', async () => {
      const mockData = {
        action: 'approval',
        paymentId: 'mock-payment-id',
        approverRemarks: '',
      };

      const mockDBData = {
        paymentId: 'mock-payment-id',
        createDateTime: getPhDateTime(),
        requestTimeStamp: getPhDateTime(),
        refundReason: 'reason',
        refundAmount: '23214',
      };
      const mockDynamooseResult = [{ ...mockDBData }];
      mockDynamooseResult.count = mockDynamooseResult.length;
      mockDynamooseResult.lastKey = { id: 'last-key' };

      refundRepository.getByPaymentIdForApproval.mockResolvedValue(mockDynamooseResult);
      refundRepository.getPaymentIdTotalRefundAmount.mockResolvedValue([]);
      transactionRepository.getByPaymentId.mockResolvedValue(mockDynamooseResult);

      kafka.produce.mockResolvedValue({ code: 'SUCCESS' });

      const res = await refundService.updateRefundRequest(mockData, {}, {});

      expect(res).toEqual({
        success: true,
        code: 200,
      });
    });

    it('should return success when rejection push through', async () => {
      const mockData = {
        action: 'reject',
        paymentId: 'mock-payment-id',
        approverRemarks: '',
      };

      const mockDBData = {
        paymentId: 'mock-payment-id',
        createDateTime: getPhDateTime(),
        requestTimeStamp: getPhDateTime(),
        refundReason: 'reason',
        refundAmount: '23214',
      };
      const mockDynamooseResult = [{ ...mockDBData }];
      mockDynamooseResult.count = mockDynamooseResult.length;
      mockDynamooseResult.lastKey = { id: 'last-key' };

      refundRepository.getByPaymentIdForApproval.mockResolvedValue(mockDynamooseResult);
      refundRepository.getPaymentIdTotalRefundAmount.mockResolvedValue([]);
      transactionRepository.getByPaymentId.mockResolvedValue(mockDynamooseResult);

      userRepository.getById.mockResolvedValue({ roleId: 1 });
      roleRepository.getById.mockResolvedValue([
        {
          id: global.faker.string.uuid(),
          name: global.faker.person.fullName({ sex: 'male' }),
          code: global.faker.string.uuid(),
          permissions: [],
        },
      ]);

      const res = await refundService.updateRefundRequest(mockData, {}, { id: 1 });

      expect(res).toEqual({
        success: true,
        code: 200,
      });
    });

    it('should log and rethrow error when approval action fails', async () => {
      const mockData = {
        action: 'approval',
        paymentId: 'mock-payment-id',
        approverRemarks: '',
      };

      refundRepository.getByPaymentIdForApproval.mockResolvedValue({ count: 0 });
      try {
        await refundService.updateRefundRequest(mockData, {}, {});
        // If we reach here, the test should fail
        fail('Expected refundapproval to throw an error');
      } catch (err) {
        // Assertions
        expect(eventLogsRepository.registerFailedEvent).toHaveBeenCalled();
        expect(logger.error).toHaveBeenCalled();
        expect(err.message).toBe('No Data Found!');
      }
    });
  });

  describe('xenditRefundSummaryReport', () => {
    it('should return the result from xenditRefundSummaryReport when successful', async () => {
      // Mock data
      const mockData = {
        filter: {},
        pagination: { limit: 10, startKey: '' },
      };
      const mockUser = {
        ewalletAssignedChannels: JSON.stringify([{ channelId: global.faker.string.uuid() }]),
        billType: 'NonBill',
      };

      const mockTransactionItem = {
        id: 'SAMPLE-paymentId',
        refundId: global.faker.string.uuid(),
        refundRejectedTimestamp: '2025-03-29T00:51:56.222Z',
        refundApprovedTimestamp: '2025-03-29T00:51:56.222Z',
        accountNumber: '**********',
        amountValue: '500',
        channelName: 'DNO-PrepaidFiber',
        postedTimestamp: '2025-03-28T13:04:04.674Z',
        paymentId: 'SAMPLE-paymentId',
        refundAmount: '50000',
        refundApprovalStatus: 'For Approval',
        refundReason: 'Auto Refund',
        refundRejectedTimestamp: null,
        status: 'PAYMENT_AUTHORIZED',
        timestamp: '2025-03-29T00:51:56.222Z',
        billType: 'NonBill',
        convenienceFee: 'CF',
        paymentMethod: 'bpi',
        finalAmount: 1,
        acquirementId: global.faker.string.uuid(),
        hasConvenienceFee: true,
        mobileNumber: '********',
        refundStatus: 'STATUS',
        refundType: 'REFUND_TYPE',
        isAutoRefund: true,
        requestTimeStamp: '2025-03-29T00:51:56.222Z',
      };

      const mockDynamooseResult = [{ ...mockTransactionItem }];
      mockDynamooseResult.count = mockDynamooseResult.length;

      const expectedResult = {
        Bill: [],
        NonBill: [
          {
            channelName: mockTransactionItem.channelName,
            totalApprovedRefundAmount: 0,
            totalApprovedRefundCount: 0,
            totalAutoRefundAmount: 0,
            totalAutoRefundCount: 0,
            totalForApprovalAmount: parseInt(mockTransactionItem.refundAmount),
            totalForApprovalCount: 1,
          },
        ],
      };

      snapshotRepository.getSnapshotByYear.mockResolvedValue([]);
      refundRepository.getFARefundTransaction.mockResolvedValue(mockDynamooseResult);

      const result = await refundService.xenditRefundSummaryReport(mockData, mockUser);

      expect(refundRepository.getFARefundTransaction).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when xenditRefundSummaryReport fails', async () => {
      // Mock data
      const mockData = {
        filter: {},
        pagination: { limit: 10, startKey: '' },
      };
      const mockUser = {
        ewalletAssignedChannels: JSON.stringify([{ channelId: global.faker.string.uuid() }]),
        billType: 'NonBill',
      };

      refundRepository.getFARefundTransaction.mockImplementation(() => {
        throw new Error('xenditRefundSummaryReport Error');
      });

      try {
        await refundService.xenditRefundSummaryReport(mockData, mockUser);
        // If we reach here, the test should fail
        fail('Expected xenditRefundSummaryReport to throw an error');
      } catch (err) {
        // Assertions
        expect(refundRepository.getFARefundTransaction).toHaveBeenCalled();
        expect(logger.error).toHaveBeenCalled();
        expect(err.message).toBe('xenditRefundSummaryReport Error');
      }
    });
  });

  describe('xenditRefundDetailedReport', () => {
    it('should return the result from xendit refund detailed report when successful', async () => {
      // Mock data
      const mockData = {
        filter: {
          paymentGateway: 'xendit',
        },
        pagination: { limit: 10, startKey: '' },
      };
      const mockUser = {
        ewalletAssignedChannels: JSON.stringify([{ channelId: global.faker.string.uuid() }]),
        billType: 'NonBill',
      };

      const mockTransactionItem = {
        id: 'SAMPLE-paymentId',
        refundId: global.faker.string.uuid(),
        refundRejectedTimestamp: '2025-03-29T00:51:56.222Z',
        refundApprovedTimestamp: '2025-03-29T00:51:56.222Z',
        accountNumber: '**********',
        amountValue: '500',
        channelName: 'DNO-PrepaidFiber',
        postedTimestamp: '2025-03-28T13:04:04.674Z',
        paymentId: 'SAMPLE-paymentId',
        refundAmount: '50000',
        refundApprovalStatus: 'For Approval',
        refundReason: 'Auto Refund',
        refundRejectedTimestamp: null,
        status: 'PAYMENT_AUTHORIZED',
        timestamp: '2025-03-29T00:51:56.222Z',
        billType: 'NonBill',
        convenienceFee: 'CF',
        paymentMethod: 'bpi',
        finalAmount: 1,
        acquirementId: global.faker.string.uuid(),
        hasConvenienceFee: true,
        mobileNumber: '********',
        refundStatus: 'STATUS',
        refundType: 'REFUND_TYPE',
        isAutoRefund: true,
        requestTimeStamp: '2025-03-29T00:51:56.222Z',
      };

      const mockDynamooseResult = [{ ...mockTransactionItem }];
      mockDynamooseResult.count = mockDynamooseResult.length;
      mockDynamooseResult.lastKey = { id: 'last-key' };

      const gcashTransId = mockTransactionItem.acquirementId;
      const refundDate = mockTransactionItem.refundRejectedTimestamp || mockTransactionItem.refundApprovedTimestamp;
      delete mockTransactionItem.acquirementId;
      delete mockTransactionItem.refundApprovedTimestamp;
      delete mockTransactionItem.id;
      delete mockTransactionItem.isAutoRefund;

      const expectedResult = {
        filteredData: [
          {
            ...mockTransactionItem,
            paymentMethod: 'BPI',
            refundDate,
            gcashTransId,
            refundType: 'Auto',
          },
        ],
        lastKey: JSON.stringify({ id: 'last-key' }),
      };

      refundRepository.searchFilter.mockResolvedValue(mockDynamooseResult);

      const result = await refundService.xenditRefundDetailedReport(mockData, mockUser);

      expect(refundRepository.searchFilter).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when xenditApproval fails', async () => {
      // Mock data
      const mockData = {
        filter: {
          paymentGateway: 'xendit',
        },
        pagination: { limit: 10, startKey: '' },
      };
      const mockUser = {
        ewalletAssignedChannels: JSON.stringify([{ channelId: global.faker.string.uuid() }]),
        billType: 'NonBill',
      };

      refundRepository.searchFilter.mockImplementation(() => {
        throw new Error('Error Search Card Refund Data');
      });

      try {
        await refundService.xenditRefundDetailedReport(mockData, mockUser);
        // If we reach here, the test should fail
        fail('Expected refundApproval to throw an error');
      } catch (err) {
        // Assertions
        expect(refundRepository.searchFilter).toHaveBeenCalled();
        expect(logger.error).toHaveBeenCalled();
        expect(err.message).toBe('Error Search Card Refund Data');
      }
    });
  });
});
