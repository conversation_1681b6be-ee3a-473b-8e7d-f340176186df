const chai = require('chai');
const sinon = require('sinon');
const { expect } = chai;

// Import the CF class and mocks
const Config = require('../../../../src/app/handlers/config');
const category = require('../../../../src/infra/utils/Category');
const error = require('../../../../src/domain/errors');
const formatter = require('../../../../src/infra/utils/formatter');

describe('App :: Handlers :: Convenience Fee', function () {
  let configService;
  let channelRepositoryMock;
  let auditRepositoryMock;
  let loggerMock;

  beforeEach(() => {
    // Mock dependencies
    configRepositoryMock = {
      getById: sinon.stub(),
      listAll: sinon.stub(),
      getAllId: sinon.stub(),
      searchFilter: sinon.stub(),
      searchFilterNoPaginate: sinon.stub(),
      getCompositeKey: sinon.stub(),
      getAllData: sinon.stub(),
      getByName: sinon.stub(),
    };

    channelRepositoryMock = {
      getByChannelId: sinon.stub(),
    };

    auditRepositoryMock = {
      add: sinon.stub(),
    };

    loggerMock = {
      error: sinon.stub(),
    };

    // Instantiate CF with mock dependencies
    configService = new Config({
      configRepository: configRepositoryMock,
      channelRepository: channelRepositoryMock,
      auditRepository: auditRepositoryMock,
      logger: loggerMock,
      Category: category,
      errors: error,
      formatter: formatter,
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('show()', function () {
    it('should call configRepository.listAll when show is called without args', async () => {
      const mockConfigs = [
        {
          name: 'hipPostPayment',
          createdAt: '2024-06-18T01:59:55.604Z',
          type: 'default',
          value: 'PostPayment',
          updatedAt: '2020-09-23T04:51:21.831Z',
        },
        {
          name: 'otcCodeLimit',
          createdAt: '2024-06-18T01:59:55.604Z',
          type: 'default',
          value: '3',
          updatedAt: '2020-09-23T04:51:21.831Z',
        },
      ];
      configRepositoryMock.listAll.resolves(mockConfigs);
      configRepositoryMock.getAllData.resolves(mockConfigs);

      const result = await configService.show();
      expect(configRepositoryMock.getAllData.calledOnce).to.be.true;
    });
  });

  describe('refundValidity()', function () {
    it('should return refund validity config value', async () => {
      const mockConfig = {
        name: 'refundValidity',
        value: 30,
      };
      configRepositoryMock.getById.resolves(mockConfig);

      const result = await configService.refundValidity();
      expect(configRepositoryMock.getById.calledOnce).to.be.true;
      expect(result).to.deep.equal({ days: 30 });
    });

    it('should handle error when configRepository.getAllData throws', async () => {
      configRepositoryMock.getById.rejects(new Error('DB error'));
      try {
        await configService.refundValidity();
      } catch (err) {
        expect(err).to.be.an('error');
        expect(err.message).to.equal('DB error');
      }
    });
  });

  describe('getPSPaymentType()', function () {
    it('should return PSOR payment types', async () => {
      const mockTypes = [
        {
          name: 'PSORPaymentTypes',
          value: [
            {
              description: 'Payment type 1',
              name: 'Type1',
              or: 'OR001',
              orVat: 'VAT001',
            },
            {
              description: 'Payment type 2',
              name: 'Type2',
              or: 'OR002',
              orVat: 'VAT002',
            },
            {
              description: 'Payment type 3',
              name: 'Type3',
              or: 'OR003',
              orVat: 'VAT003',
            },
          ],
        },
      ];
      configRepositoryMock.getByName.resolves(mockTypes);
      const result = await configService.getPSPaymentType();
      expect(configRepositoryMock.getByName.calledOnce).to.be.true;
      expect(result).to.deep.equal(mockTypes[0].value);
    });
  });
});
