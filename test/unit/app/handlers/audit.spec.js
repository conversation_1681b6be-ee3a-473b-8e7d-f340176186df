const chai = require('chai');
const sinon = require('sinon');
const expect = chai.expect;

const Audit = require('../../../../src/app/handlers/audit');

describe('App :: Handlers :: Audit', () => {
  let auditService;
  let mockRepositories;
  let logger;

  beforeEach(() => {
    mockRepositories = {
      auditRepository: {
        getById: sinon.stub(),
        notifNotViewCount: sinon.stub(),
        notifications: sinon.stub(),
        update: sinon.stub(),
      },
      userRepository: {
        getById: sinon.stub(),
      },
      roleRepository: {
        getById: sinon.stub(),
      },
      channelRepository: {
        getChannelName: sinon.stub(),
      },
    };

    logger = { error: sinon.spy() };

    const container = {
      ...mockRepositories,
      logger,
    };

    auditService = new Audit(container);
    auditService.logger = logger;
    sinon.stub(auditService, 'searchData');
  });

  afterEach(() => sinon.restore());

  describe('show()', () => {
    it('should return audit with user, role, and channel data', async () => {
      const user = { id: 'u1', name: 'Alice', email: '<EMAIL>', roleId: 'r1', channel: 'c1' };
      const role = { name: 'Admin' };
      const channel = { name: 'Marketing' };

      mockRepositories.auditRepository.getById.resolves({
        id: 'a1',
        userId: 'u1',
        userName: 'Alice',
        userEmail: '<EMAIL>',
        newValue: { some: 'value' },
        oldValue: { other: 'value' },
      });
      mockRepositories.userRepository.getById.resolves(user);
      mockRepositories.roleRepository.getById.resolves(role);
      mockRepositories.channelRepository.getChannelName.resolves(channel);

      const result = await auditService.show({ id: 'a1' });

      expect(result.user.name).to.equal('Alice');
      expect(result.user.role).to.deep.equal(role);
      expect(result.user.channel).to.equal('Marketing');
      expect(result.newValue).to.be.a('string');
      expect(result.oldValue).to.be.a('string');
    });
  });

  describe('search()', () => {
    it('should return formatted search results', async () => {
      auditService.searchData.resolves({
        filteredData: [{ id: '1', newValue: { x: 1 }, oldValue: { x: 0 } }],
        lastKey: JSON.stringify({
          id: '1',
          sortKey: 's1',
          createdAt: 'now',
        }),
      });

      const result = await auditService.search({ some: 'criteria' });
      expect(result.filteredData[0].newValue).to.be.a('string');
      expect(result.lastKey).to.have.keys(['id', 'sortKey', 'createdAt']);
    });
  });

  describe('isNotViewCount()', () => {
    it('should return isNotViewed count', async () => {
      mockRepositories.auditRepository.notifNotViewCount.resolves({ count: 5 });

      const result = await auditService.isNotViewCount();
      expect(result).to.deep.equal({ isNotViewed: 5 });
    });
  });

  describe('notifications()', () => {
    it('should return empty result when count is 0', async () => {
      mockRepositories.auditRepository.notifications.resolves({ count: 0 });

      const result = await auditService.notifications({ id: 'x' });
      expect(result.filteredData).to.deep.equal([]);
      expect(result.count).to.equal(0);
    });

    it('should return filtered notifications', async () => {
      mockRepositories.auditRepository.notifications.resolves({ count: 2, data: ['n1', 'n2'] });

      const result = await auditService.notifications({ id: 'x' });
      expect(result.count).to.equal(2);
    });
  });

  describe('updateStatus()', () => {
    it('should call update on each ID', async () => {
      const data = { id: { a: 'id1', b: 'id2' } };

      await auditService.updateStatus(data);

      expect(mockRepositories.auditRepository.update.calledTwice).to.be.true;
      expect(mockRepositories.auditRepository.update.firstCall.args[1]).to.deep.equal({ isViewed: true });
    });
  });
});
