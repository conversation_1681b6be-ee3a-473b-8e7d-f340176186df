const chai = require('chai');
const sinon = require('sinon');
const { expect } = chai;

// Import the CF Brand class and mocks
const ConvenienceFeeBrand = require('../../../../src/app/handlers/convenienceFeeBrand');
const uuid = require('../../../../src/infra/utils/Uuid');
const category = require('../../../../src/infra/utils/Category');
const error = require('../../../../src/domain/errors');

describe('App :: Handlers :: Convenience Fee Brand', function () {
  let convenienceFeeBrandService;
  let convenienceFeeBrandRepositoryMock;
  let userRepositoryMock;
  let roleRepositoryMock;
  let auditRepositoryMock;
  let loggerMock;

  beforeEach(() => {
    // Mock dependencies
    convenienceFeeBrandRepositoryMock = {
      getById: sinon.stub(),
      listAll: sinon.stub(),
      getAllId: sinon.stub(),
      searchFilter: sinon.stub(),
      searchFilterNoPaginate: sinon.stub(),
    };

    userRepositoryMock = {
      getById: sinon.stub(),
    };

    roleRepositoryMock = {
      getById: sinon.stub(),
    };

    auditRepositoryMock = {
      add: sinon.stub(),
    };

    loggerMock = {
      error: sinon.stub(),
    };

    // Instantiate CF Brand with mock dependencies
    convenienceFeeBrandService = new ConvenienceFeeBrand({
      convenienceFeeBrandRepository: convenienceFeeBrandRepositoryMock,
      userRepository: userRepositoryMock,
      roleRepository: roleRepositoryMock,
      auditRepository: auditRepositoryMock,
      uuid: uuid,
      logger: loggerMock,
      Category: category,
      errors: error,
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('show()', function () {
    it('should return the convenience fee brand data when found', async function () {
      const id = '123';
      const mockCFBrandData = [{ id, name: 'TestBrand' }];

      convenienceFeeBrandRepositoryMock.getById.resolves(mockCFBrandData);

      const result = await convenienceFeeBrandService.show({ id: { id } });

      expect(convenienceFeeBrandRepositoryMock.getById.calledOnceWith(id)).to.be.true;
      expect(result).to.deep.equal(mockCFBrandData[0]);
    });

    it('should log an error and throw if repository.getById fails', async function () {
      const id = '123';
      const mockError = new Error('Database error');

      convenienceFeeBrandRepositoryMock.getById.rejects(mockError);

      try {
        await convenienceFeeBrandService.show({ id: { id } });
        throw new Error('Expected show() to throw');
      } catch (error) {
        expect(error).to.equal(mockError);
        expect(loggerMock.error.calledOnce).to.be.true;
      }
    });
  });

  describe('search()', function () {
    it('should return the convenience fee brand data when found with empty filter', async function () {
      const mockCFBrandData = [
        {
          name: 'TestBrand',
          createdAt: '2024-10-29T06:30:04.031Z',
          id: 'd2f78dfa-adff-4311-a239-d6a8ea630639',
          updatedAt: '2024-10-29T06:30:04.031Z',
        },
      ];
      const mockAllIdData = [{ id: 'd2f78dfa-adff-4311-a239-d6a8ea630639' }];

      Object.defineProperty(mockCFBrandData, 'count', { value: 1 });
      Object.defineProperty(mockAllIdData, 'count', { value: 1 });

      convenienceFeeBrandRepositoryMock.listAll.resolves(mockCFBrandData);
      convenienceFeeBrandRepositoryMock.getAllId.resolves(mockAllIdData);

      const result = await convenienceFeeBrandService.search({
        filter: {},
        pagination: { startKey: '', limit: 10 },
      });

      expect(convenienceFeeBrandRepositoryMock.listAll.calledOnce).to.be.true;
      expect(convenienceFeeBrandRepositoryMock.getAllId.calledOnce).to.be.true;
      expect(result).to.deep.equal({
        filteredData: mockCFBrandData,
        count: 1,
        cursors: [''],
      });
    });

    it('should return filtered data when filter is provided', async function () {
      const mockCFBrandData = [
        {
          name: 'TestBrand',
          id: 'd2f78dfa-adff-4311-a239-d6a8ea630639',
        },
      ];
      const mockAllIdData = [{ id: 'd2f78dfa-adff-4311-a239-d6a8ea630639' }];

      Object.defineProperty(mockCFBrandData, 'count', { value: 1 });
      Object.defineProperty(mockAllIdData, 'count', { value: 1 });

      convenienceFeeBrandRepositoryMock.searchFilter.resolves(mockCFBrandData);
      convenienceFeeBrandRepositoryMock.searchFilterNoPaginate.resolves(mockAllIdData);

      const result = await convenienceFeeBrandService.search({
        filter: { name: 'TestBrand' },
        pagination: { startKey: '', limit: 10 },
      });

      expect(convenienceFeeBrandRepositoryMock.searchFilter.calledOnce).to.be.true;
      expect(convenienceFeeBrandRepositoryMock.searchFilterNoPaginate.calledOnce).to.be.true;
      expect(result.filteredData).to.have.lengthOf(1);
    });

    it('should log an error and throw if repository.listAll fails', async function () {
      const mockError = new Error('Database error');

      convenienceFeeBrandRepositoryMock.listAll.rejects(mockError);

      try {
        await convenienceFeeBrandService.search({
          filter: {},
          pagination: { startKey: '', limit: 10 },
        });
        throw new Error('Expected search() to throw');
      } catch (error) {
        expect(error).to.equal(mockError);
      }
    });
  });

  describe('createConvenienceFeeBrand()', function () {
    it('should successfully create a convenience fee brand when valid data is provided', async function () {
      const data = { name: 'TestBrand' };
      const id = '123';

      convenienceFeeBrandService.create = sinon.stub().resolves(data);

      const result = await convenienceFeeBrandService.createConvenienceFeeBrand(
        data,
        { browser: 'chrome' },
        '<EMAIL>'
      );

      expect(convenienceFeeBrandService.create.calledOnce).to.be.true;
      expect(result).to.deep.equal(data);
    });

    it('should log an error and throw if repository.create fails', async function () {
      const data = { name: 'TestBrand' };
      const mockError = new Error('Database error');

      convenienceFeeBrandService.create = sinon.stub().rejects(mockError);

      try {
        await convenienceFeeBrandService.createConvenienceFeeBrand(data, { browser: 'chrome' }, '<EMAIL>');
        throw new Error('Expected createConvenienceFeeBrand() to throw');
      } catch (error) {
        expect(error).to.equal(mockError);
        expect(loggerMock.error.calledOnce).to.be.true;
        expect(loggerMock.error.firstCall.args[0]).to.be.an('error'); // Just the error object
      }
    });
  });

  describe('updateConvenienceFeeBrand()', function () {
    it('should successfully update a convenience fee brand when valid data is provided', async function () {
      const data = { name: 'UpdatedBrand' };
      const id = 'd2f78dfa-adff-4311-a239-d6a8ea630639';

      convenienceFeeBrandService.update = sinon.stub().resolves(data);

      const result = await convenienceFeeBrandService.updateConvenienceFeeBrand(
        data,
        { id },
        { browser: 'chrome' },
        '<EMAIL>'
      );

      expect(convenienceFeeBrandService.update.calledOnce).to.be.true;
      expect(result).to.deep.equal(data);
    });

    it('should log an error and throw if repository.update fails', async function () {
      const data = { name: 'UpdatedBrand' };
      const id = '123';
      const mockError = new Error('Database error');

      convenienceFeeBrandService.update = sinon.stub().rejects(mockError);

      try {
        await convenienceFeeBrandService.updateConvenienceFeeBrand(
          data,
          { id },
          { browser: 'chrome' },
          '<EMAIL>'
        );
        throw new Error('Expected updateConvenienceFeeBrand() to throw');
      } catch (error) {
        expect(error).to.equal(mockError);
        expect(loggerMock.error.calledOnce).to.be.true;
        expect(loggerMock.error.firstCall.args[0]).to.be.an('error'); // Just the error object
      }
    });
  });

  describe('deleteConvenienceFeeBrand()', function () {
    it('should successfully delete a convenience fee brand when valid data is provided', async function () {
      const id = 'd2f78dfa-adff-4311-a239-d6a8ea630639';

      const mockCFBrandData = [{ id, name: 'TestBrand' }];
      Object.defineProperty(mockCFBrandData, 'count', { value: 1 });

      convenienceFeeBrandRepositoryMock.getById.resolves(mockCFBrandData);
      convenienceFeeBrandService.delete = sinon.stub().resolves({ id });

      const result = await convenienceFeeBrandService.deleteConvenienceFeeBrand(
        { id },
        { browser: 'chrome' },
        '<EMAIL>'
      );

      expect(convenienceFeeBrandRepositoryMock.getById.calledOnceWith(id)).to.be.true;
      expect(result).to.deep.equal({ id });
    });

    it('should throw error when convenience fee brand not found', async function () {
      const id = 'nonexistent-id';

      const mockCFBrandData = [];
      Object.defineProperty(mockCFBrandData, 'count', { value: 0 });

      convenienceFeeBrandRepositoryMock.getById.resolves(mockCFBrandData);

      convenienceFeeBrandService.errors = {
        notFound: () => new Error('NOT_FOUND'),
      };

      try {
        await convenienceFeeBrandService.deleteConvenienceFeeBrand({ id }, { browser: 'chrome' }, '<EMAIL>');
        throw new Error('Expected deleteConvenienceFeeBrand() to throw');
      } catch (error) {
        expect(error.message).to.equal('NOT_FOUND');
        expect(loggerMock.error.calledOnce).to.be.true;
        expect(loggerMock.error.firstCall.args[0]).to.be.an('error');
      }
    });
  });
});
