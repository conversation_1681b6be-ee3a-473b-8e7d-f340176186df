const Role = require('../../../../src/app/handlers/role');
const uuid = require('../../../../src/infra/utils/Uuid');
const category = require('../../../../src/infra/utils/Category');
const csv = require('csvtojson');

jest.mock('csvtojson');
describe('App :: Handlers :: Roles', () => {
  let roleService;
  let userRepositoryMock;
  let roleRepositoryMock;
  let channelRepositoryMock;
  let configMock;
  let loggerMock;
  let auditRepositoryMock;
  let errorMocks;

  beforeEach(() => {
    roleRepositoryMock = {
      add: jest.fn(),
      getAllName: jest.fn(),
      getById: jest.fn(),
      getAllId: jest.fn(),
      listAll: jest.fn(),
      getAll: jest.fn(),
      update: jest.fn(),
      getByRoleCode: jest.fn(),
      remove: jest.fn(),
    };

    userRepositoryMock = {
      getById: jest.fn(),
      filteredCount: jest.fn(),
      filterRole: jest.fn(),
      extractAllUsers: jest.fn(),
      getAllName: jest.fn(),
    };

    loggerMock = {
      error: jest.fn(),
      info: jest.fn(),
    };

    errorMocks = {
      notFound: jest.fn(),
      roleIdError: jest.fn(),
    };

    auditRepositoryMock = {
      add: jest.fn(),
      batchPut: jest.fn(),
    };

    roleService = new Role({
      roleRepository: roleRepositoryMock,
      userRepository: userRepositoryMock,
      logger: loggerMock,
      config: configMock,
      auditRepository: auditRepositoryMock,
      uuid: uuid,
      Category: category,
      errors: errorMocks,
      channelRepository: channelRepositoryMock,
    });
  });

  describe('show()', () => {
    it('should return the result from show', async () => {
      // Mock data
      const mockData = {
        where: {
          id: 'role-id',
        },
      };
      const expectedResult = {
        id: global.faker.string.uuid(),
        name: global.faker.person.fullName({ sex: 'male' }),
        code: global.faker.string.uuid(),
        permissions: [],
      };
      jest.spyOn(roleRepositoryMock, 'getById').mockResolvedValue([expectedResult]);
      jest.spyOn(userRepositoryMock, 'filteredCount').mockResolvedValue(expectedResult);
      // Call the show method
      const result = await roleService.show(mockData);
      // Assertions
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when show fails', async () => {
      // Mock data
      const mockData = {
        where: {
          id: 'role-id',
        },
      };

      jest.spyOn(roleRepositoryMock, 'getById').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await roleService.show(mockData);
        // If we reach here, the test should fail
        fail('Expected show to throw an error');
      } catch (err) {
        // Assertions
        expect(roleRepositoryMock.getById).toHaveBeenCalledWith(mockData.where.id);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('search()', () => {
    it('should return the result from search: no filter', async () => {
      // Mock data
      const mockData = {
        filter: {},
        pagination: { limit: 10, startKey: '' },
      };

      const filteredData = {
        count: 1,
        lastKey: { id: 1 },
        filteredData: [
          {
            id: 'user-id',
            name: 'test',
            numberOfUsers: 1,
          },
        ],
      };

      const expectedResult = {
        filteredData: [filteredData.filteredData],
        count: 1,
        cursors: [''],
        lastKey: { id: 1 },
      };
      jest.spyOn(roleService, 'searchDataWithCursors').mockResolvedValue(expectedResult);
      jest.spyOn(userRepositoryMock, 'filteredCount').mockResolvedValue(filteredData.count);
      // Call the search method
      const result = await roleService.search(mockData);
      // Assertions
      expect(result).toEqual(expectedResult);
    });

    it('should return the result from search: with filter', async () => {
      // Mock data
      const mockData = {
        filter: { email: '<EMAIL>' },
        pagination: { limit: 10, startKey: '' },
      };
      const getItemResult = {
        Item: {
          id: 'user-id',
          name: 'test',
          numberOfUsers: 1,
        },
        Count: 1,
        ScannedCount: 1,
      };

      const expectedResult = {
        filteredData: [getItemResult.Item],
        count: 1,
        cursors: [''],
        lastKey: { id: 1 },
      };
      jest.spyOn(roleService, 'searchDataWithCursors').mockResolvedValue(expectedResult);
      jest.spyOn(userRepositoryMock, 'filteredCount').mockResolvedValue(getItemResult.Count);
      // Call the search method
      const result = await roleService.search(mockData);
      // Assertions
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when searchData fails', async () => {
      // Mock data
      const mockData = {
        filter: { paymentId: '123' },
        pagination: { limit: 10, startKey: '' },
      };

      jest.spyOn(roleService, 'searchDataWithCursors').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await roleService.search(mockData);
        // If we reach here, the test should fail
        fail('Expected search to throw an error');
      } catch (err) {
        // Assertions
        expect(roleService.searchDataWithCursors).toHaveBeenCalledWith(mockData);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('rolesLoose()', () => {
    it('should return the result from rolesLoose', async () => {
      // Mock data
      const mockData = {
        filter: { paymentId: '123' },
        pagination: { limit: 10, startKey: '' },
      };
      const expectedResult = [
        {
          id: 'user-id',
          name: 'test',
          numberOfUsers: 1,
        },
      ];
      jest.spyOn(roleRepositoryMock, 'getAll').mockResolvedValue(expectedResult);
      // Call the search method
      const result = await roleService.rolesLoose(mockData);
      // Assertions
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when rolesLoose fails', async () => {
      // Mock data
      const mockData = {
        where: {
          id: 'role-id',
        },
      };

      jest.spyOn(roleRepositoryMock, 'getAll').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await roleService.rolesLoose(mockData);
        // If we reach here, the test should fail
        fail('Expected rolesLoose to throw an error');
      } catch (err) {
        // Assertions
        expect(roleRepositoryMock.getAll).toHaveBeenCalledWith(mockData);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('createRole()', () => {
    it('should return the result from createRole', async () => {
      // Mock data
      const mockData = {
        name: global.faker.person.fullName({ sex: 'male' }),
        code: global.faker.string.uuid(),
        notes: global.faker.string.uuid(),
        permissions: [],
      };

      const expectedResult = {
        id: global.faker.string.uuid(),
        name: global.faker.person.fullName({ sex: 'male' }),
        code: global.faker.string.uuid(),
        permissions: [],
      };

      jest.spyOn(roleRepositoryMock, 'getByRoleCode').mockResolvedValue({ count: 0 });
      jest.spyOn(roleRepositoryMock, 'add').mockResolvedValue(expectedResult);

      const result = await roleService.createRole(mockData, null, null);

      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when createRole fails', async () => {
      // Mock data
      const mockData = {
        name: global.faker.person.fullName({ sex: 'male' }),
        code: global.faker.string.uuid(),
        notes: global.faker.string.uuid(),
        permissions: [],
      };

      jest.spyOn(roleRepositoryMock, 'getByRoleCode').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await roleService.createRole(mockData);
        // If we reach here, the test should fail
        fail('Expected createRole to throw an error');
      } catch (err) {
        // Assertions
        expect(roleRepositoryMock.getByRoleCode).toHaveBeenCalledWith(mockData.code);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('updateRole()', () => {
    it('should return the result from updateRole', async () => {
      // Mock data
      const mockData = {
        id: global.faker.string.uuid(),
        name: global.faker.person.fullName({ sex: 'male' }),
        code: global.faker.string.uuid(),
        notes: global.faker.string.uuid(),
        permissions: [],
        isActive: true,
        emailNotif: true,
        smsNotif: true,
      };
      const expectedResult = {
        id: global.faker.string.uuid(),
        name: global.faker.person.fullName({ sex: 'male' }),
        code: global.faker.string.uuid(),
        notes: global.faker.string.uuid(),
        permissions: [],
        isActive: false,
        emailNotif: true,
        smsNotif: true,
      };

      jest.spyOn(roleRepositoryMock, 'getById').mockResolvedValue(expectedResult);
      jest.spyOn(roleRepositoryMock, 'update').mockResolvedValue(expectedResult);
      jest.spyOn(roleRepositoryMock, 'getByRoleCode').mockResolvedValue(expectedResult);
      jest.spyOn(userRepositoryMock, 'filterRole').mockResolvedValue({ ...mockData, isActive: false });

      const result = await roleService.updateRole(mockData, mockData, null, null);

      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when updateRole fails', async () => {
      // Mock data
      const mockData = {
        name: global.faker.person.fullName({ sex: 'male' }),
        code: global.faker.string.uuid(),
        notes: global.faker.string.uuid(),
        permissions: [],
      };

      const mockId = { id: 'update-id' };

      jest.spyOn(roleRepositoryMock, 'getById').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await roleService.updateRole(mockData, { id: 'update-id' });
        // If we reach here, the test should fail
        fail('Expected updateRole to throw an error');
      } catch (err) {
        // Assertions
        expect(roleRepositoryMock.getById).toHaveBeenCalledWith(mockId.id);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('deleteRole()', () => {
    it('should return the result from deleteRole', async () => {
      // Mock data
      const mockData = {
        data: {
          reasonToDelete: 'test-reason',
        },
        where: {
          id: global.faker.string.uuid(),
        },
        currentUser: {
          id: global.faker.string.uuid(),
        },
      };
      const mockUser = {
        count: 1,
      };
      const mockRole = [
        {
          name: 'test-role',
        },
      ];
      const expectedResult = {
        unprocessedItems: 0,
      };

      jest.spyOn(roleRepositoryMock, 'getById').mockResolvedValue(mockRole);
      jest.spyOn(roleRepositoryMock, 'remove').mockResolvedValue(expectedResult);
      jest.spyOn(userRepositoryMock, 'getById').mockResolvedValue(mockUser);

      const result = await roleService.deleteRole(mockData.data, mockData.where, null, mockData.currentUser);

      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when deleteRole fails', async () => {
      // Mock data
      const mockData = {
        data: {
          reasonToDelete: 'test-reason',
        },
        where: {
          id: global.faker.string.uuid(),
        },
        currentUser: {
          id: global.faker.string.uuid(),
        },
      };

      const mockId = { id: 'update-id' };

      jest.spyOn(roleRepositoryMock, 'getById').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await roleService.deleteRole(mockData, { id: 'update-id' });
        // If we reach here, the test should fail
        fail('Expected deleteRole to throw an error');
      } catch (err) {
        // Assertions
        expect(roleRepositoryMock.getById).toHaveBeenCalledWith(mockId.id);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('deleteRoles()', () => {
    it('should return the result from deleteRoles', async () => {
      const mockData = {
        data: {
          reasonToDelete: 'test-reason',
        },
        where: {
          ids: [global.faker.string.uuid()],
        },
        currentUser: {
          id: global.faker.string.uuid(),
        },
      };
      const expectedResult = {
        unprocessedItems: 0,
      };
      const mockRole = [
        {
          name: 'test-role',
        },
      ];
      const mockUser = {
        count: 1,
        items: [
          {
            id: global.faker.string.uuid(),
          },
        ],
      };

      jest.spyOn(roleService, 'batchGet').mockResolvedValue(mockRole);
      jest.spyOn(roleRepositoryMock, 'getById').mockResolvedValue(mockRole);
      jest.spyOn(userRepositoryMock, 'getById').mockResolvedValue(mockUser);
      jest.spyOn(roleService, 'batchDelete').mockResolvedValue(expectedResult);

      const result = await roleService.deleteRoles(mockData.data, mockData.where, null, mockData.currentUser);

      expect(result).toEqual(expectedResult);
    });
  });

  describe('uploadRoles()', () => {
    it('should return the result from uploadRoles', async () => {
      const mockData = {
        file: {
          file: {
            filename: 'test-file.csv',
            mimetype: 'test-mimetype',
            createReadStream: () => ({
              pipe: jest.fn().mockReturnThis(),
              on: jest.fn().mockReturnThis(),
              destroy: jest.fn(),
            }),
          },
        },
        currentUser: {
          id: global.faker.string.uuid(),
        },
      };
      const mockResult = {
        count: 1,
        user: [
          {
            name: global.faker.person.fullName({ sex: 'male' }),
          },
        ],
        role: [
          {
            id: 1,
            name: 'user',
          },
        ],
      };
      const mockCsvData = [
        {
          name: 'role-1',
          code: '123',
          notes: 'test',
          permissions: 'User.view|User.create|User.update|User.delete',
        },
      ];
      const expectedResult = {
        filename: 'test-file.csv',
        mimetype: 'test-mimetype',
      };
      csv.mockReturnValue({ fromStream: jest.fn().mockResolvedValue(mockCsvData) });
      jest.spyOn(roleRepositoryMock, 'getAllName').mockResolvedValue(mockResult.role);
      jest.spyOn(userRepositoryMock, 'getAllName').mockResolvedValue(mockResult.user);

      const result = await roleService.upload({ file: mockData.file }, mockData.currentUser);

      expect(result).toEqual(expectedResult);
    });
  });
});
