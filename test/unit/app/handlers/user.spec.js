const User = require('../../../../src/app/handlers/user');
const uuid = require('../../../../src/infra/utils/Uuid');
const category = require('../../../../src/infra/utils/Category');
const csv = require('csvtojson');
const DateHelper = require('../../../../src/infra/utils/date');

jest.mock('csvtojson');
describe('App :: Handlers :: Reports', () => {
  let userService;
  let userRepositoryMock;
  let roleRepositoryMock;
  let channelRepositoryMock;
  let loginVerifierMock;
  let configMock;
  let mapperMock;
  let authorizationMock;
  let jwtMock;
  let loggerMock;
  let auditRepositoryMock;
  let errorMocks;

  beforeEach(() => {
    // Mock dependencies
    userRepositoryMock = {
      getByEmail: jest.fn(),
      getById: jest.fn(),
      getUserByEmail: jest.fn(),
      getAllName: jest.fn(),
      getAllData: jest.fn(),
      getVerified: jest.fn(),
      getVerifiedPayByLinkChannels: jest.fn(),
      update: jest.fn(),
      saveOauthTokens: jest.fn(),
      batchPut: jest.fn(),
      batchDelete: jest.fn(),
      add: jest.fn(),
      searchFilter: jest.fn(),
      searchFilterNoPaginate: jest.fn(),
      getAllId: jest.fn(),
      listAll: jest.fn(),
      listAllQuery: jest.fn(),
      postedAndFailedList: jest.fn(),
      postedAndFailedFilter: jest.fn(),
      getActiveUser: jest.fn(),
      getInactiveUser: jest.fn(),
      extractAllUsers: jest.fn(),
      getAllName: jest.fn(),
      saveOauthTokens: jest.fn(),
      batchGet: jest.fn(),
    };

    roleRepositoryMock = {
      getById: jest.fn(),
      whereIn: jest.fn(),
      getAllName: jest.fn(),
    };

    channelRepositoryMock = {
      getAllName: jest.fn(),
    };

    loginVerifierMock = {
      getTokenInfo: jest.fn(),
      getToken: jest.fn(),
    };

    configMock = {
      auth: {},
      notifPatternId: {
        emails: {
          User: {
            registration: '30161',
            activate: '30181',
            deactivate: '30201',
            update: '30221',
            delete: '30341',
            newRole: '30421',
          },
        },
        sms: {
          User: {
            registration: '30161',
            activate: '30181',
            deactivate: '30201',
            update: '30221',
            delete: '30341',
            newRole: '30421',
          },
        },
      },
    };

    mapperMock = jest.fn();

    authorizationMock = {
      call: jest.fn(),
    };

    jwtMock = {
      decode: jest.fn(),
      encode: jest.fn(),
      validate: jest.fn(),
    };

    dateHelperMock = {
      dateRange: jest.fn(),
    };

    loggerMock = {
      error: jest.fn(),
      info: jest.fn(),
    };

    auditRepositoryMock = {
      add: jest.fn(),
      batchPut: jest.fn(),
    };

    errorMocks = {
      notFound: () => 'test',
    };

    // Instantiate User with mock dependencies
    userService = new User({
      userRepository: userRepositoryMock,
      roleRepository: roleRepositoryMock,
      loginVerifier: loginVerifierMock,
      config: configMock,
      Mapper: mapperMock,
      authorization: authorizationMock,
      logger: loggerMock,
      jwt: jwtMock,
      DateHelper: DateHelper,
      auditRepository: auditRepositoryMock,
      uuid: uuid,
      Category: category,
      errors: errorMocks,
      channelRepository: channelRepositoryMock,
    });
  });

  describe('search()', () => {
    it('should return the result from search: no filter', async () => {
      // Mock data
      const mockData = {
        filter: {},
        pagination: { limit: 10, startKey: '' },
      };

      const filteredData = [
        {
          id: 'user-id',
          name: 'test',
          email: '<EMAIL>',
          role: {
            name: 'test-role',
          },
          group: 'test-group',
          channel: 'test-channel',
          department: 'test-department',
          division: 'test-division',
          isActive: true,
          createdAt: '2023-01-01T00:00:00.000Z',
          loginTime: '2023-01-01T00:00:00.000Z',
          mobileNumber: '1234567890',
          assignedChannels: [],
          cardAssignedChannels: [],
          ewalletAssignedChannels: [],
          postPaymentConfigChannels: [],
          billType: 'Bill',
        },
      ];

      const expectedResult = {
        filteredData,
        count: 1,
        cursors: [''],
        lastKey: null,
      };
      jest.spyOn(userRepositoryMock, 'listAll').mockResolvedValue(filteredData);
      jest.spyOn(userRepositoryMock, 'getAllId').mockResolvedValue({ count: filteredData.length });

      jest.spyOn(roleRepositoryMock, 'whereIn').mockResolvedValue([1]);
      jest.spyOn(userService, 'mapper').mockResolvedValue(filteredData);
      // Call the search method
      const result = await userService.search(mockData);
      // Assertions
      expect(result).toEqual(expectedResult);
    });

    it('should return the result from search: with filter', async () => {
      // Mock data
      const mockData = {
        filter: { email: '<EMAIL>' },
        pagination: { limit: 10, startKey: '' },
      };
      const getItemResult = {
        Item: {
          id: 'user-id',
          name: 'test',
          email: '<EMAIL>',
          roleId: 1,
          group: 'test-group',
          channel: 'test-channel',
          department: 'test-department',
          division: 'test-division',
          isActive: true,
          createdAt: '2023-01-01T00:00:00.000Z',
          loginTime: '2023-01-01T00:00:00.000Z',
          mobileNumber: '1234567890',
          assignedChannels: [],
          cardAssignedChannels: [],
          ewalletAssignedChannels: [],
          postPaymentConfigChannels: [],
          billType: 'Bill',
        },

        Count: 1,
        ScannedCount: 1,
      };

      const expectedResult = {
        filteredData: [getItemResult.Item],
        count: 1,
        cursors: [''],
        lastKey: { id: 1 },
      };
      jest.spyOn(userService, 'searchDataWithCursors').mockResolvedValue(expectedResult);
      jest.spyOn(roleRepositoryMock, 'whereIn').mockResolvedValue([1]);
      // Call the search method
      const result = await userService.search(mockData);
      // Assertions
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when searchData fails', async () => {
      // Mock data
      const mockData = {
        filter: { paymentId: '123' },
        pagination: { limit: 10, startKey: '' },
      };

      jest.spyOn(userService, 'searchDataWithCursors').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await userService.search(mockData);
        // If we reach here, the test should fail
        fail('Expected search to throw an error');
      } catch (err) {
        // Assertions
        expect(userService.searchDataWithCursors).toHaveBeenCalledWith(mockData);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('show()', () => {
    it('should return the result from show', async () => {
      // Mock data
      const mockData = {
        where: {
          id: 'user-id',
        },
      };
      const expectedResult = {
        id: global.faker.string.uuid(),
        name: global.faker.person.fullName({ sex: 'male' }),
        email: global.faker.internet.email(),
        roleId: 1,
        group: 'test-group',
        channel: 'test-channel',
        department: 'test-department',
        division: 'test-division',
        isActive: true,
        createdAt: '2023-01-01T00:00:00.000Z',
        loginTime: '2023-01-01T00:00:00.000Z',
        mobileNumber: '1234567890',
        assignedChannels: '[]',
        cardAssignedChannels: '[]',
        ewalletAssignedChannels: '[]',
        postPaymentConfigChannels: '[]',
        billType: 'Bill',
      };
      jest.spyOn(userRepositoryMock, 'getById').mockResolvedValue(expectedResult);
      jest.spyOn(roleRepositoryMock, 'getById').mockResolvedValue(expectedResult);
      // Call the show method
      const result = await userService.show(mockData);
      // Assertions
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when searchData fails', async () => {
      // Mock data
      const mockData = {
        where: {
          id: 'user-id',
        },
      };

      jest.spyOn(userRepositoryMock, 'getById').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await userService.show(mockData);
        // If we reach here, the test should fail
        fail('Expected show to throw an error');
      } catch (err) {
        // Assertions
        expect(userRepositoryMock.getById).toHaveBeenCalledWith({ id: 'user-id' });
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('summary()', () => {
    it('should return the result from summary', async () => {
      // Mock data
      const mockData = {
        range: {
          start: '2023-01-01T00:00:00.000Z',
          end: '2023-01-03T00:00:00.000Z',
        },
        isPolling: false,
      };
      const expectedResult = {
        activeUsers: [
          {
            month: 'January',
            count: 1,
          },
        ],
        inactiveUsers: [
          {
            month: 'January',
            count: 1,
          },
        ],
      };
      jest.spyOn(userRepositoryMock, 'getActiveUser').mockResolvedValue(expectedResult.activeUsers[0]);
      jest.spyOn(userRepositoryMock, 'getInactiveUser').mockResolvedValue(expectedResult.inactiveUsers[0]);
      // Call the summary method
      const result = await userService.summary(mockData);
      // Assertions
      expect(result).toEqual(expectedResult);
    });

    it('should return the result from summary', async () => {
      // Mock data
      const mockData = {
        range: {
          start: '2023-01-01T00:00:00.000Z',
          end: '2023-01-03T00:00:00.000Z',
        },
        isPolling: false,
      };

      const expectedResult = {
        start: mockData.range.start,
        end: mockData.range.end,
        month: 'January',
      };
      jest.spyOn(userRepositoryMock, 'getActiveUser').mockImplementation(() => {
        throw new Error('Database error');
      });

      // Assertions
      try {
        await userService.summary(mockData);
        // If we reach here, the test should fail
        fail('Expected summary to throw an error');
      } catch (err) {
        // Assertions
        expect(userRepositoryMock.getActiveUser).toHaveBeenCalledWith(expectedResult);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('login()', () => {
    it('should return the result from login', async () => {
      // Mock data
      const mockData = {
        idToken: 'id-token',
        email: '<EMAIL>',
        hostedDomain: 'test.com',
      };

      const expectedResult = {
        token: 'token',
        accessToken: 'access-token',
        user: {
          id: 'user-id',
          name: 'test',
          email: '<EMAIL>',
          role: {
            name: 'test-role',
          },
        },
        permissions: [],
      };

      // Mock login to return expected result
      jest.spyOn(userService, 'login').mockResolvedValue(expectedResult);

      // Call the login method
      const result = await userService.login(mockData);
      // Assertions
      expect(userService.login).toHaveBeenCalledWith(mockData);
      expect(result).toEqual(expectedResult);
    });

    it('should return a failed result: Google authentication failed!', async () => {
      // Mock data
      const mockData = {
        idToken: 'id-token',
        email: '<EMAIL>',
        hostedDomain: 'test.com',
      };

      const expectedResult = {};

      // Mock login to return expected result
      jest.spyOn(loginVerifierMock, 'getTokenInfo').mockResolvedValue(null);

      try {
        await userService.login(mockData);

        fail('Expected login to throw an error');
      } catch (err) {
        // Assertions
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Login Failed!');
      }
    });

    it('should return a failed result: User not Registered!', async () => {
      // Mock data
      const mockData = {
        email: global.faker.internet.email(),
      };

      // Mock login to return expected result
      jest.spyOn(userRepositoryMock, 'getUserByEmail').mockResolvedValue(undefined);

      try {
        await userService.login(mockData);

        fail('Expected login to throw an error');
      } catch (err) {
        // Assertions
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Login Failed!');
      }
    });
  });

  describe('registration()', () => {
    it('should return the result from registration', async () => {
      // Mock data
      const mockData = {
        name: global.faker.person.fullName({ sex: 'male' }),
        email: global.faker.internet.email(),
        roleId: 'test-role',
        division: 'test-division',
        mobileNumber: '1234567890',
        department: 'test-department',
        group: 'test-group',
      };
      const expectedResult = {
        id: 'user-id',
        name: 'test',
        email: '<EMAIL>',
        role: 'test-role',
        roleId: 1,
      };

      jest.spyOn(userRepositoryMock, 'getByEmail').mockResolvedValue({ count: 0 });
      jest.spyOn(userService, 'create').mockResolvedValue(expectedResult);
      // Call the registration method
      const result = await userService.registration(mockData);
      // Assertions
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when registration fails', async () => {
      // Mock data
      const mockData = {
        name: global.faker.person.fullName({ sex: 'male' }),
        email: global.faker.internet.email(),
        roleId: 'test-role',
        division: 'test-division',
        mobileNumber: '1234567890',
        department: 'test-department',
        group: 'test-group',
      };

      jest.spyOn(userRepositoryMock, 'getByEmail').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await userService.registration(mockData);
        // If we reach here, the test should fail
        fail('Expected registration to throw an error');
      } catch (err) {
        // Assertions
        expect(userRepositoryMock.getByEmail).toHaveBeenCalledWith(mockData.email);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('validateToken()', () => {
    it('should return the result from validateToken', async () => {
      // Mock data
      const mockData = {
        token: 'token',
      };

      const expectedResult = {
        id: global.faker.string.uuid(),
        name: global.faker.person.fullName({ sex: 'male' }),
        email: global.faker.internet.email(),
        roleId: 1,
        group: 'test-group',
        channel: 'test-channel',
        department: 'test-department',
        division: 'test-division',
        isActive: true,
        createdAt: '2023-01-01T00:00:00.000Z',
        loginTime: '2023-01-01T00:00:00.000Z',
        mobileNumber: '1234567890',
        assignedChannels: [],
        cardAssignedChannels: [],
        ewalletAssignedChannels: [],
        postPaymentConfigChannels: [],
        billType: 'Bill',
      };

      jest.spyOn(jwtMock, 'validate').mockResolvedValue(expectedResult);
      jest.spyOn(userRepositoryMock, 'getById').mockResolvedValue(expectedResult);
      jest.spyOn(roleRepositoryMock, 'getById').mockResolvedValue(expectedResult);

      const result = await userService.validateToken(mockData);
      // Assertions
      expect(result).toEqual(expectedResult);
    });

    it('should return null if token is invalid', async () => {
      // Mock data
      const mockData = {
        token: 'token',
      };

      jest.spyOn(jwtMock, 'validate').mockReturnValue(null);

      const result = await userService.validateToken(mockData);
      // Assertions
      expect(result).toEqual({ user: null });
    });
  });

  describe('updateUser()', () => {
    it('should return the result from updateUser', async () => {
      // Mock data
      const mockData = {
        id: global.faker.string.uuid(),
        name: global.faker.person.fullName({ sex: 'male' }),
        email: global.faker.internet.email(),
        roleId: 'test-role',
        division: 'test-division',
        mobileNumber: '1234567890',
        department: 'test-department',
        group: 'test-group',
        billType: 'Bill',
      };
      const expectedResult = {
        id: 'user-id',
        name: 'test',
        email: '<EMAIL>',
        role: 'test-role',
        roleId: 1,
      };

      jest.spyOn(userRepositoryMock, 'getByEmail').mockResolvedValue(expectedResult);
      jest.spyOn(userRepositoryMock, 'getById').mockResolvedValue({ count: 1 });
      jest.spyOn(userService, 'update').mockResolvedValue(expectedResult);
      // Call the updateUser method
      const result = await userService.updateUser(mockData);
      // Assertions
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when update fails', async () => {
      // Mock data
      const mockData = {
        id: global.faker.string.uuid(),
        name: global.faker.person.fullName({ sex: 'male' }),
        email: global.faker.internet.email(),
        billType: 'Bill',
      };

      jest.spyOn(userRepositoryMock, 'getById').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await userService.updateUser(mockData);
        // If we reach here, the test should fail
        fail('Expected updateUser to throw an error');
      } catch (err) {
        // Assertions
        expect(userRepositoryMock.getById).toHaveBeenCalled();
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('deleteUser()', () => {
    it('should return the result from deleteUser', async () => {
      // Mock data
      const mockData = {
        data: {
          reasonToDelete: 'test-reason',
        },
        where: {
          id: 'user-id',
        },
        currentUser: {
          id: 'current-user-id',
        },
      };
      const mockUser = {
        count: 1,
      };
      const expectedResult = null;

      jest.spyOn(userRepositoryMock, 'getById').mockResolvedValue(mockUser);
      jest.spyOn(userService, 'delete').mockResolvedValue(expectedResult);

      const result = await userService.deleteUser(mockData.data, mockData.where, null, mockData.currentUser);

      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when deleteUser fails', async () => {
      // Mock data
      const mockData = {
        data: {
          reasonToDelete: 'test-reason',
        },
        where: {
          id: 'user-id',
        },
        currentUser: {
          id: 'current-user-id',
        },
      };

      jest.spyOn(userRepositoryMock, 'getById').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await userService.deleteUser(mockData.data, mockData.where, null, mockData.currentUser);
        // If we reach here, the test should fail
        fail('Expected deleteUser to throw an error');
      } catch (err) {
        // Assertions
        expect(userRepositoryMock.getById).toHaveBeenCalled();
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('deleteUsers()', () => {
    it('should return the result from deleteUsers', async () => {
      const mockData = {
        data: {
          reasonToDelete: 'test-reason',
        },
        where: {
          ids: ['user-id'],
        },
        currentUser: {
          id: 'current-user-id',
        },
      };
      const expectedResult = {
        unprocessedItems: 0,
      };
      const mockUser = {
        count: 1,
        items: [
          {
            id: 'user-id',
          },
        ],
      };

      jest.spyOn(userService, 'batchGet').mockResolvedValue(mockUser.items);
      jest.spyOn(userRepositoryMock, 'getById').mockResolvedValue(mockUser);
      jest.spyOn(userService, 'batchDelete').mockResolvedValue(expectedResult);

      const result = await userService.deleteUsers(mockData.data, mockData.where, null, mockData.currentUser);

      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when deleteUsers fails', async () => {
      // Mock data
      const mockData = {
        data: {
          reasonToDelete: 'test-reason',
        },
        where: {
          ids: ['user-id'],
        },
        currentUser: {
          id: 'current-user-id',
        },
      };

      jest.spyOn(userRepositoryMock, 'batchGet').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await userService.deleteUsers(mockData.data, mockData.where, null, mockData.currentUser);
        // If we reach here, the test should fail
        fail('Expected deleteUsers to throw an error');
      } catch (err) {
        // Assertions
        expect(userRepositoryMock.batchGet).toHaveBeenCalled();
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('downloadUsers()', () => {
    it('should return the result from downloadUsers', async () => {
      const mockData = {
        data: {
          type: 'test-type',
        },
        currentUser: {
          id: 'current-user-id',
        },
      };
      const expectedResult = {
        id: global.faker.string.uuid(),
        name: global.faker.person.fullName({ sex: 'male' }),
        emailAddress: global.faker.internet.email(),
        group: 'undefined',
        createdAt: '2023-01-01T00:00:00.000Z',
        loginTime: '2023-01-01T00:00:00.000Z',
        permission: undefined,
        roleDescription: undefined,
        roleName: undefined,
        status: 'Active',
      };
      const mockUser = {
        count: 1,
        items: [{ ...expectedResult, roleId: 1, email: expectedResult.emailAddress }],
      };
      jest.spyOn(userRepositoryMock, 'extractAllUsers').mockResolvedValue(mockUser.items);
      jest.spyOn(userRepositoryMock, 'getById').mockResolvedValue(expectedResult);
      jest.spyOn(roleRepositoryMock, 'getById').mockResolvedValue([1]);
      const result = await userService.download(mockData.data, mockData.currentUser);

      expect(result).toEqual([expectedResult]);
    });

    it('should log and rethrow error when downloadUsers fails', async () => {
      // Mock data
      const mockData = {
        data: {
          type: 'test-type',
        },
        currentUser: {
          id: 'current-user-id',
        },
      };

      jest.spyOn(userRepositoryMock, 'extractAllUsers').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await userService.download(mockData.data, mockData.currentUser);
        // If we reach here, the test should fail
        fail('Expected downloadUsers to throw an error');
      } catch (err) {
        // Assertions
        expect(userRepositoryMock.extractAllUsers).toHaveBeenCalled();
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('uploadUsers()', () => {
    it('should return the result from uploadUsers', async () => {
      const mockData = {
        file: {
          file: {
            filename: 'test-file.csv',
            mimetype: 'test-mimetype',
            createReadStream: () => ({
              pipe: jest.fn().mockReturnThis(),
              on: jest.fn().mockReturnThis(),
              destroy: jest.fn(),
            }),
          },
        },
        currentUser: {
          id: 'current-user-id',
        },
      };

      const mockResult = {
        count: 1,
        user: [
          {
            name: global.faker.person.fullName({ sex: 'male' }),
          },
        ],
        role: [
          {
            id: 1,
            name: 'user',
          },
        ],
      };
      const mockCsvData = [
        {
          name: 'Alice',
          email: '<EMAIL>',
          mobileNumber: '123',
          role: 'user',
          channel: 'web',
          group: 'A',
          division: 'X',
          department: 'ops',
          roleId: 1,
        },
        {
          name: 'Bob',
          email: '<EMAIL>',
          mobileNumber: '456',
          role: 'user',
          channel: 'mobile',
          group: 'B',
          division: 'Y',
          department: 'dev',
          roleId: 1,
        },
      ];

      const expectedResult = {
        filename: 'test-file.csv',
        mimetype: 'test-mimetype',
      };
      csv.mockReturnValue({ fromStream: jest.fn().mockResolvedValue(mockCsvData) });
      jest.spyOn(userRepositoryMock, 'getAllName').mockResolvedValue(mockResult.user);
      jest.spyOn(roleRepositoryMock, 'getAllName').mockResolvedValue(mockResult.role);
      jest.spyOn(channelRepositoryMock, 'getAllName').mockResolvedValue(mockResult.role);
      const result = await userService.upload({ file: mockData.file }, mockData.currentUser);
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when uploadUsers fails', async () => {
      // Mock data
      const mockData = {
        data: {
          type: 'test-type',
        },
        currentUser: {
          id: 'current-user-id',
        },
      };

      jest.spyOn(userRepositoryMock, 'extractAllUsers').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await userService.download(mockData.data, mockData.currentUser);
        // If we reach here, the test should fail
        fail('Expected downloadUsers to throw an error');
      } catch (err) {
        // Assertions
        expect(userRepositoryMock.extractAllUsers).toHaveBeenCalled();
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('deactivateUser()', () => {
    it('should return the result from deactivateUser', async () => {
      const mockData = {
        data: {
          reasonToDeactivate: 'test-reason',
        },
        hashKeys: {
          ids: ['user-id'],
        },
        currentUser: {
          id: 'current-user-id',
          role: {
            name: 'admin',
          },
        },
      };
      const expectedResult = {
        id: global.faker.string.uuid(),
        name: global.faker.person.fullName({ sex: 'male' }),
        emailAddress: global.faker.internet.email(),
        group: 'undefined',
        createdAt: '2023-01-01T00:00:00.000Z',
        loginTime: '2023-01-01T00:00:00.000Z',
        permission: undefined,
        roleDescription: undefined,
        roleName: undefined,
        status: 'Active',
      };
      const mockUser = {
        count: 1,
        items: [{ ...expectedResult, roleId: 1, email: expectedResult.emailAddress }],
      };
      const mockBatchPutResult = [
        {
          unprocessedItems: 0,
        },
      ];
      jest.spyOn(userRepositoryMock, 'getById').mockResolvedValue(expectedResult);
      jest.spyOn(userRepositoryMock, 'batchGet').mockResolvedValue(mockUser.items);
      jest.spyOn(userRepositoryMock, 'batchPut').mockResolvedValue(mockBatchPutResult);

      const result = await userService.deactivateUser(mockData.data, mockData.hashKeys, null, mockData.currentUser);

      expect(result).toEqual(mockBatchPutResult[0]);
    });

    it('should log and rethrow error when deactivateUsers fails', async () => {
      // Mock data
      const mockData = {
        data: {
          reasonToDeactivate: 'test-reason',
        },
        hashKeys: {
          ids: ['user-id'],
        },
        currentUser: {
          id: 'current-user-id',
          role: {
            name: 'admin',
          },
        },
      };

      jest.spyOn(userService, 'batchGet').mockImplementation(() => {
        throw new Error('Database error');
      });

      try {
        await userService.deactivateUser(mockData.data, mockData.currentUser);
        // If we reach here, the test should fail
        fail('Expected deactivateUsers to throw an error');
      } catch (err) {
        // Assertions
        expect(userService.batchGet).toHaveBeenCalled();
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });
});
