// Import Jest's mocking functionality
const Reports = require('../../../../src/app/handlers/reports');
const uuid = require('../../../../src/infra/utils/Uuid');
const category = require('../../../../src/infra/utils/Category');

describe('App :: Handlers :: Reports', () => {
  let reportsService;
  let transactionRepositoryMock;
  let channelRepositoryMock;
  let loggerMock;
  let dateHelperMock;
  let configMock;
  let authClientMock;
  let uploadFileMock;
  let userRepositoryMock;
  let roleRepositoryMock;
  let auditRepositoryMock;
  let payByLinkRepositoryMock;
  let refundRepositoryMock;

  beforeEach(() => {
    // Mock dependencies
    transactionRepositoryMock = {
      listAll: jest.fn(),
      searchFilter: jest.fn(),
      getAll: jest.fn(),
      getById: jest.fn(),
      add: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
      postedAndFailedList: jest.fn(),
      postedAndFailedFilter: jest.fn(),
      getTransactionCount: jest.fn(),
      revenueWirelessMonthlyList: jest.fn(),
      billingDailyReport: jest.fn(),
      globeOneDailyReport: jest.fn(),
      ecpayDailyReport: jest.fn(),
    };

    channelRepositoryMock = {
      getChannelForTransaction: jest.fn(),
      getByChannelId: jest.fn(),
      getBatch: jest.fn(),
    };

    refundRepositoryMock = {
      getByPaymentAndTransactionId: jest.fn(),
    };

    loggerMock = {
      error: jest.fn(),
      info: jest.fn(),
    };

    dateHelperMock = {
      monthsCovered: jest.fn(),
      monthNumberConverted: jest.fn(),
      monthNames: jest
        .fn()
        .mockReturnValue([
          'January',
          'February',
          'March',
          'April',
          'May',
          'June',
          'July',
          'August',
          'September',
          'October',
          'November',
          'December',
        ]),
    };

    configMock = {
      recipient: {},
      notifPatternId: {
        emails: {
          Reports: {},
        },
      },
    };

    authClientMock = {
      call: jest.fn(),
    };

    uploadFileMock = {
      S3UploadFile: jest.fn(),
      S3DownloadFile: jest.fn(),
    };

    userRepositoryMock = {
      getById: jest.fn(),
    };

    roleRepositoryMock = {
      getById: jest.fn(),
    };

    auditRepositoryMock = {
      add: jest.fn(),
    };

    payByLinkRepositoryMock = {
      getByPaymentId: jest.fn(),
      getAll: jest.fn(),
    };

    // Instantiate Reports with mock dependencies
    reportsService = new Reports({
      transactionRepository: transactionRepositoryMock,
      channelRepository: channelRepositoryMock,
      DateHelper: dateHelperMock,
      logger: loggerMock,
      config: configMock,
      authClient: authClientMock,
      uploadFile: uploadFileMock,
      userRepository: userRepositoryMock,
      roleRepository: roleRepositoryMock,
      auditRepository: auditRepositoryMock,
      payByLinkRepository: payByLinkRepositoryMock,
      refundRepository: refundRepositoryMock,
      uuid: uuid,
      Category: category,
    });

    // Mock the searchData method for testing
    jest.spyOn(reportsService, 'searchData').mockImplementation();
    jest.spyOn(reportsService, 'searchDataForReport').mockImplementation();
  });

  describe('search()', () => {
    it('should return the result from searchData when successful', async () => {
      // Mock data
      const mockData = {
        filter: { paymentId: '123' },
        pagination: { limit: 10, startKey: '' },
      };

      const expectedResult = {
        filteredData: [
          {
            paymentId: '123',
            createDateTime: '2023-01-01T00:00:00.000Z',
            channelId: 'channel-123',
            customerId: 'customer-123',
            gatewayProcessor: 'gcash',
            paymentMethod: 'GCASH',
            status: 'POSTED',
            totalAmount: '100.00',
          },
        ],
        count: 1,
        lastKey: { id: 'last-key' },
      };

      // Mock searchData to return expected result
      reportsService.searchData.mockResolvedValue(expectedResult);

      // Call the search method
      const result = await reportsService.search(mockData);

      // Assertions
      expect(reportsService.searchData).toHaveBeenCalledWith(mockData);
      expect(reportsService.searchData).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when searchData fails', async () => {
      // Mock data
      const mockData = {
        filter: { paymentId: '123' },
        pagination: { limit: 10, startKey: '' },
      };

      // Mock searchData to throw error
      reportsService.searchData.mockImplementation(() => {
        throw new Error('Database error');
      });

      // Call the search method and expect it to throw
      try {
        await reportsService.search(mockData);
        // If we reach here, the test should fail
        fail('Expected search to throw an error');
      } catch (err) {
        // Assertions
        expect(reportsService.searchData).toHaveBeenCalledWith(mockData);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });

    it('should handle empty filter object correctly', async () => {
      // Mock data with empty filter
      const mockData = {
        filter: {},
        pagination: { limit: 10, startKey: '' },
      };

      const expectedResult = {
        filteredData: [
          {
            paymentId: '456',
            createDateTime: '2023-02-01T00:00:00.000Z',
            channelId: 'channel-456',
            customerId: 'customer-456',
            gatewayProcessor: 'adyen',
            paymentMethod: 'CREDIT_CARD',
            status: 'POSTED',
            totalAmount: '200.00',
          },
        ],
        count: 1,
        lastKey: { id: 'last-key-2' },
      };

      // Mock searchData to return expected result
      reportsService.searchData.mockResolvedValue(expectedResult);

      // Call the search method
      const result = await reportsService.search(mockData);

      // Assertions
      expect(reportsService.searchData).toHaveBeenCalledWith(mockData);
      expect(result).toEqual(expectedResult);
    });

    it('should handle undefined filter correctly', async () => {
      // Mock data with undefined filter
      const mockData = {
        pagination: { limit: 10, startKey: '' },
      };

      const expectedResult = {
        filteredData: [
          {
            paymentId: '789',
            createDateTime: '2023-03-01T00:00:00.000Z',
            channelId: 'channel-789',
            customerId: 'customer-789',
            gatewayProcessor: 'xendit',
            paymentMethod: 'BANK_TRANSFER',
            status: 'POSTED',
            totalAmount: '300.00',
          },
        ],
        count: 1,
        lastKey: { id: 'last-key-3' },
      };

      // Mock searchData to return expected result
      reportsService.searchData.mockResolvedValue(expectedResult);

      // Call the search method
      const result = await reportsService.search(mockData);

      // Assertions
      expect(reportsService.searchData).toHaveBeenCalledWith(mockData);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('installmentReport', () => {
    it('should return the result from installmentReport when successful', async () => {
      // Mock data
      const mockData = {
        filter: {},
        pagination: { limit: 10, startKey: '' },
      };

      const expectedResult = {
        filteredData: [
          {
            amountCurrency: 'PHP',
            amountValue: 1500,
            createDateTime: '2025-05-20T10:54:40.501Z',
            creditCardBank: null,
            creditCardCountry: null,
            creditCardHolderName: null,
            creditCardNumber: 'XXXXXXXXX',
            creditCardType: 'Credit',
            customerContactNumber: '*********',
            emailAddress: global.faker.internet.email(),
            installment: { count: 3, interval: 'month' },
            installmentPaymentId: null,
            installmentTerm: null,
            isThreeDFlag: false,
            itemPurchased: null,
            orderReference: null,
            paymentId: 'CARD1747709680500974',
            status: 'POSTED',
          },
        ],
        count: 1,
        lastKey: { id: 'last-key' },
      };

      // Mock searchData to return expected result
      reportsService.searchDataForReport.mockResolvedValue(expectedResult);

      // Call the search method
      const result = await reportsService.installmentReport(mockData);

      expect(reportsService.searchDataForReport).toHaveBeenCalledWith(mockData);
      expect(reportsService.searchDataForReport).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResult);
    });

    it('should log and rethrow error when installmentReport fails', async () => {
      // Mock data
      const mockData = {
        filter: { paymentId: '123' },
        pagination: { limit: 10, startKey: '' },
      };

      // Mock searchData to throw error
      reportsService.searchDataForReport.mockImplementation(() => {
        throw new Error('Database error');
      });

      // Call the search method and expect it to throw
      try {
        await reportsService.installmentReport(mockData);
        // If we reach here, the test should fail
        fail('Expected search to throw an error');
      } catch (err) {
        // Assertions
        expect(reportsService.searchDataForReport).toHaveBeenCalledWith(mockData);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('billingReport', () => {
    it('should return the result from billing when successful', async () => {
      // Mock data
      const mockData = {
        filter: {},
        pagination: { limit: 10, startKey: '' },
      };

      const expectedResult = {
        filteredData: [
          {
            channelId: '329077e0-dd1e-4142-a6d2-52b3ef86679b',
            channelName: 'For Testing Only',
            createDateTime: '2025-04-25T09:51:11.218Z',
            gatewayProcessor: 'xendit',
            paymentId: 'PAY1745545871216052',
            paymentMethod: 'otc',
            settlementBreakdown: [
              {
                accountNumber: null,
                accountType: null,
                amountValue: '250',
                brand: null,
                emailAddress: '<EMAIL>',
                mobileNumber: '**********',
                transactionType: 'N',
              },
            ],
            accountNumber: null,
            accountType: null,
            amountValue: '250',
            brand: null,
            emailAddress: '<EMAIL>',
            mobileNumber: '**********',
            transactionType: 'N',
            status: 'POSTED',
            totalAmount: '250',
            updateDateTime: '2025-04-25T09:51:11.218Z',
          },
        ],
        count: 1,
        lastKey: { id: 'last-key' },
      };

      // Mock searchData to return expected result
      reportsService.searchDataForReport.mockResolvedValue(expectedResult);

      // Call the search method
      const result = await reportsService.billing(mockData);

      expect(reportsService.searchDataForReport).toHaveBeenCalledWith(mockData);
      expect(reportsService.searchDataForReport).toHaveBeenCalledTimes(1);
      expect(result).toEqual(expectedResult);
    });
    it('should log and rethrow error when billing fails', async () => {
      // Mock data
      const mockData = {
        filter: { paymentId: '123' },
        pagination: { limit: 10, startKey: '' },
      };

      // Mock searchData to throw error
      reportsService.searchDataForReport.mockImplementation(() => {
        throw new Error('Database error');
      });

      // Call the search method and expect it to throw
      try {
        await reportsService.billing(mockData);
        // If we reach here, the test should fail
        fail('Expected search to throw an error');
      } catch (err) {
        // Assertions
        expect(reportsService.searchDataForReport).toHaveBeenCalledWith(mockData);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('Database error');
      }
    });
  });

  describe('payByLinkReport', () => {
    it('should return the result from payByLinkRepository.getByPaymentId when reference provided', async () => {
      // Mock data
      const mockData = {
        filter: { reference: 'ONE1719253006475720', status: 'SETTLED' },
        pagination: { limit: 10, startKey: '' },
      };

      const expectedResult = [
        {
          amount: 100,
          description: 'QAE Testing - ShopeePay',
          paymentLink: 'https://checkout.xendit.co/web/6679b80e2b2dc653ba0e5103',
          id: 'ONE1719253006475720',
          status: 'SETTLED',
          merchantReference: '6679b80e2b2dc653ba0e5103',
          createdAt: '2024-06-24T18:16:46.953Z',
        },
      ];

      payByLinkRepositoryMock.getByPaymentId.mockResolvedValue({
        ...expectedResult,
        lastKey: { id: 'last-key' },
      });

      const result = await reportsService.payByLinkReport(mockData);

      expect(payByLinkRepositoryMock.getByPaymentId).toHaveBeenCalledWith('ONE1719253006475720', mockData.filter);
      expect(payByLinkRepositoryMock.getByPaymentId).toHaveBeenCalledTimes(1);
      expect(result).toEqual({
        lastKey: JSON.stringify({ id: 'last-key' }),
        filteredData: { ...expectedResult, lastKey: { id: 'last-key' } },
      });
    });

    it('should return the result from payByLinkRepository.getAll when no reference provided', async () => {
      // Mock data
      const mockData = {
        filter: { status: 'PENDING' },
        pagination: { limit: 10, startKey: '' },
      };

      const expectedResult = [
        {
          amount: 250,
          description: 'QAE Testing - GrabPay',
          id: 'ONE1719340000000000',
          status: 'PENDING',
          createdAt: '2024-06-25T10:00:00.000Z',
        },
      ];

      payByLinkRepositoryMock.getAll.mockResolvedValue({
        ...expectedResult,
        lastKey: { sortKey: 'paybylink', createdAt: '2024-06-25T10:00:00.000Z' },
      });

      const result = await reportsService.payByLinkReport(mockData);

      expect(payByLinkRepositoryMock.getAll).toHaveBeenCalledWith(mockData);
      expect(payByLinkRepositoryMock.getAll).toHaveBeenCalledTimes(1);
      expect(result).toEqual({
        lastKey: JSON.stringify({ sortKey: 'paybylink', createdAt: '2024-06-25T10:00:00.000Z' }),
        filteredData: { ...expectedResult, lastKey: { sortKey: 'paybylink', createdAt: '2024-06-25T10:00:00.000Z' } },
      });
    });

    it('should log and rethrow error when payByLinkRepository fails', async () => {
      // Mock data
      const mockData = {
        filter: { reference: 'ONE1719253006475720' },
        pagination: { limit: 10, startKey: '' },
      };

      // Mock repository to throw error
      payByLinkRepositoryMock.getByPaymentId.mockImplementation(() => {
        throw new Error('PayByLink repository error');
      });

      // Call the method and expect it to throw
      try {
        await reportsService.payByLinkReport(mockData);
        fail('Expected payByLinkReport to throw an error');
      } catch (err) {
        expect(payByLinkRepositoryMock.getByPaymentId).toHaveBeenCalledWith('ONE1719253006475720', mockData.filter);
        expect(loggerMock.error).toHaveBeenCalled();
        expect(err.message).toBe('PayByLink repository error');
      }
    });
  });
});
