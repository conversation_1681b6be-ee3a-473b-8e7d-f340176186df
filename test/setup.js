const chai = require('chai');
const dirtyChai = require('dirty-chai');
const sinon = require('sinon');
const { faker } = require('@faker-js/faker');
const joi = require('joi');

const container = require('../src/container');

const handlers = container.resolve('handlers');

chai.use(dirtyChai);

global.sinon = sinon;
global.faker = faker;
global.expect = chai.expect;
global.joi = joi;
global.handlers = handlers;

exports.mochaHooks = {
  afterEach() {
    sinon.restore();
  },
};
