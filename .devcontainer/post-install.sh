#!/bin/sh 

echo "########### Installing all dependencies ##########"
npm install

echo "########### Creating profile ###########"
aws configure set aws_access_key_id test --profile=default
aws configure set aws_secret_access_key test --profile=default
aws configure set region ap-southeast-1 --profile=default

echo "########### Listing profile ############"
aws configure list --profile=default

echo "########### Generate env file ############"
cp .env.sample .env

echo "########### Creating SuperAdmin User ############"
npm run seeder

echo "########### Starting Application ############"
npm run dev