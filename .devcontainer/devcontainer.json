// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.241.1/containers/javascript-node
{
  "name": "UPS-CMS-API",
  "image": "mcr.microsoft.com/devcontainers/javascript-node:1-18-bookworm",
  // Features to add to the dev container. More info: https://containers.dev/features.
  "features": {
    "ghcr.io/devcontainers/features/aws-cli:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {}
  },
  // Configure tool-specific properties.
  "customizations": {
    // Configure properties specific to VS Code.
    "vscode": {
      "settings": {
        "workbench.colorTheme": "Default Dark+",
        "prettier.singleQuote": true
      },
      // Add the IDs of extensions you want installed when the container is created.
      "extensions": [
        "dbaeumer.vscode-eslint",
        "eamodio.gitlens",
        "esbenp.prettier-vscode",
        "sonarsource.sonarlint-vscode",
        "GitLab.gitlab-workflow"
      ]
    }
  },

  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  "forwardPorts": [5000],

  // Use 'postCreateCommand' to run commands after the container is created.
  "postCreateCommand": "bash $(pwd)/.devcontainer/post-install.sh",

  "runArgs": [
    "--network=localstack_default"
  ]
  // Uncomment if you want to bind ssh files to the container
  // "mounts": [
  //   "source=${localEnv:HOME}/.ssh,target=/home/<USER>/.ssh,type=bind,consistency=cached"
  // ]

  // Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
  // "remoteUser": "root"
}
