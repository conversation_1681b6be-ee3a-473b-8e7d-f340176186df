const globals = require('globals');
const pluginJs = require('@eslint/js');
const eslintConfigPrettier = require('eslint-config-prettier');

module.exports = [
  {
    files: ['**/*.js'],
    languageOptions: { sourceType: 'commonjs' },
    rules: {
      'comma-spacing': ['error', { before: false, after: true }],
      indent: ['error', 2, { SwitchCase: 1 }],
      'linebreak-style': 1,
      quotes: ['error', 'single'],
      semi: ['error', 'always'],
      'one-var': [
        1,
        {
          uninitialized: 'consecutive',
        },
      ],
      'max-len': [
        1,
        {
          code: 120,
          ignoreComments: true,
          ignoreTrailingComments: true,
          ignoreTemplateLiterals: true,
        },
      ],
      'no-unused-vars': ['error', { caughtErrors: 'none', args: 'none' }],
    },
  },
  eslintConfigPrettier,
  {
    ignores: ['test/*.js', 'test/**/*.js', 'test/**/*.spec.js'],
  },
  { languageOptions: { globals: globals.node } },
  pluginJs.configs.recommended,
];
