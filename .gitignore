# Logs
*.log
*.zip
*.gz
*.log.gz

# Storages
storage/

# Node
node_modules/

# Project specific
config/logging.j
.env

# Unit test / coverage reports
coverage/
.nyc_output

# OS auto-generated files
.DS_Store
._*


# Vim
*~
*.swp
*.swo

# IDE files
.idea/
.vscode/

# SonarQube ignore files.
#
# https://docs.sonarqube.org/display/SCAN/Analyzing+with+SonarQube+Scanner
# Sonar Scanner working directories
.sonar/
.scannerwork/

# http://www.sonarlint.org/commandline/
# SonarLint working directories, configuration files (including credentials)
.sonarlint/

reports