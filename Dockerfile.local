ARG NODE_VERSION=22.14.0

FROM node:$NODE_VERSION-alpine AS base

RUN apk --no-cache add \
    bash \
    g++ \
    make \
    python3 \
    zlib-dev \
    libc-dev \
    bsd-compat-headers \
    py-setuptools \
    openssl-dev \
    musl-dev

# Stage 1: Dependencies
FROM base AS deps

WORKDIR /home/<USER>/app

COPY package*.json ./

RUN npm install -g husky@9.1.5  \
    && npm ci --omit=dev --verbose

# Stage 2: Runtime
FROM base AS runner

# Install runtime dependencies
RUN apk --no-cache add \
    lz4-dev \
    cyrus-sasl \
    ca-certificates 

WORKDIR /home/<USER>/app

# Copy application files from the builder stage
COPY --from=deps /home/<USER>/app/node_modules ./node_modules
COPY package*.json ./
COPY index.js ./
COPY config ./config
COPY src ./src

# Uncomment this if local testing/development
COPY .env.example .env

# Set user to non-root
USER node
EXPOSE 3000

# Set the command to serve the built static files.
ENTRYPOINT ["node", "index.js"]
